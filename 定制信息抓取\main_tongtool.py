import re
import time
from DrissionPage import Chromium, ChromiumOptions
import requests
import pickle
import os
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import logging
import base64
import uuid
import json
# import msgLog
import concurrent.futures
from datetime import datetime, timedelta
import os
import subprocess
import configparser
import zipfile
import win32com.client as win32
from PIL import Image
import time
import pythoncom
import xlsxwriter
import requests
from io import BytesIO
import random
import urllib.parse
import mimetypes
import json
import tongtool
from requests.cookies import RequestsCookieJar

mimetypes.add_type('image/mpo', '.mpo')


def run_bat_file(bat_file_path):
    try:
        # 使用 subprocess.run 函数执行 bat 文件
        result = subprocess.run(bat_file_path, check=True, shell=True)
        # 打印命令的返回码
        print(f"命令的返回码: {result.returncode}")
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败，返回码: {e.returncode}，错误信息: {e.stderr}")


logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
SESSION_FILE = "session.pkl"


class SessionManager:
    """管理会话的类"""

    def __init__(self):
        self.session = None

    def save_session(self, session):
        if os.path.isfile(SESSION_FILE):
            os.remove(SESSION_FILE)
            logging.info(f'File {SESSION_FILE} has been deleted.')
        else:
            logging.error(f'File {SESSION_FILE} not found.')
        """保存 session 对象到文件"""
        with open(SESSION_FILE, "wb") as f:
            pickle.dump(session, f)
        logging.info("Session 已保存到文件")

    def load_session(self):
        """从文件加载 session 对象"""
        if os.path.exists(SESSION_FILE):
            with open(SESSION_FILE, "rb") as f:
                self.session = pickle.load(f)
            logging.info("Session 已从文件加载")

            return True
        else:
            logging.info("未找到保存的 Session 文件")
            return False

    def create_new_session(self):
        """创建一个新的 session 对象"""
        self.session = requests.Session()

    def get_session(self):
        """获取 session 对象，若不存在则创建"""
        if not self.session:
            self.load_session()
            if not self.session:
                self.create_new_session()
        return self.session

    def check_cookie(self):
        """检测并修正 Cookie 值的类型"""
        for cookie in self.session.cookies:
            # 检测值的类型，如果不是字符串或字节，则转换为字符串
            if not isinstance(cookie.value, (str, bytes)):
                # 将值转换为字符串
                corrected_value = str(cookie.value)
                self.session.cookies.set(cookie.name, corrected_value,
                                         domain=cookie.domain, path=cookie.path,
                                         expires=cookie.expires)
                logging.warning(
                    f"Cookie 值类型错误，已修正: {cookie.name} = {corrected_value}")

    logging.info("Cookie 检查完成，所有值均为字符串类型")

    def set_cookie(self, name, value, domain="muke.lingxing.com", path="/", expires=0):
        """设置 Cookie"""
        if expires != 0:
            # 获取当前时间并加上指定的天数 (默认 30 天)
            expires_time = datetime.utcnow() + timedelta(days=expires)

            # 将过期时间转换为时间戳（秒数）
            expires = int(expires_time.timestamp())
        self.session.cookies.set(name, value, domain=domain,
                                 path=path, expires=expires)
        logging.info(f"Cookie 设置: {name} = {str(value)}")

    def remove_cookie(self, name):
        """移除指定的 Cookie，若存在"""
        if name in self.session.cookies:
            self.session.cookies.clear(name)
            logging.info(f"Cookie 移除: {name}")
        else:
            logging.info(f"Cookie {name} 不存在，未执行移除操作")


class CommonClass:
    """公用工具类"""

    def __init__(self):
        pass

    def read_config(self, config_path="config_tongtool.ini"):
        """读取配置文件"""
        config = configparser.ConfigParser()
        if not os.path.exists(config_path):
            logging.error(f"配置文件 {config_path} 不存在")
            return None
        config.read(config_path, encoding="utf-8")
        config_data = {}
        for section in config.sections():
            config_data[section] = dict(config.items(section))
        logging.info(f"配置文件 {config_path} 已加载")
        return config_data

    def getuuid(self):
        """生成 UUID"""
        return str(uuid.uuid4())

    def encrypt(self, F, q):
        """加密方法"""
        if isinstance(F, str):  # 如果密钥是字符串，转换为字节
            F = F.encode('utf-8')

        # 将字符串 q 转换为字节
        l = q.encode('utf-8')

        # 创建 AES 加密对象，ECB 模式，不需要 IV
        cipher = AES.new(F, AES.MODE_ECB)

        # Pkcs7 填充
        encrypted = cipher.encrypt(pad(l, AES.block_size))

        # 转换为 Base64 字符串
        return base64.b64encode(encrypted).decode('utf-8')

    def get_today(self):
        """获取昨天到今天的日期范围"""
        today = datetime.today()
        yesterday = today - timedelta(days=1)
        start_date = yesterday.strftime("%Y-%m-%d")
        end_date = today.strftime("%Y-%m-%d")
        return start_date, end_date

    def get_yestoday_to_today(self):
        """获取昨天到今天的日期范围"""
        today = datetime.today()
        yesterday = today - timedelta(days=1)
        start_date = yesterday.strftime("%Y-%m-%d 00:00:00")
        end_date = today.strftime("%Y-%m-%d 23:59:59")
        return start_date, end_date

    def get_last_one_months_range(self):
        """
        获取最近三个月的日期范围，从当前日期往前推。
        :return: (start_date, end_date) 起始日期和结束日期
        """
        today = datetime.today()
        # 计算三个月前的日期
        three_months_ago = today - timedelta(days=30)  # 粗略三个月为90天
        # 格式化日期
        start_date = three_months_ago.strftime("%Y-%m-%d")
        end_date = today.strftime("%Y-%m-%d")
        return start_date, end_date

    def get_last_two_months_range(self):
        """
        获取最近两个月的日期范围，从当前日期往前推。
        :return: (start_date, end_date) 起始日期和结束日期
        """
        today = datetime.today()
        # 计算三个月前的日期
        three_months_ago = today - timedelta(days=60)  # 粗略三个月为90天
        # 格式化日期
        start_date = three_months_ago.strftime("%Y-%m-%d")
        end_date = today.strftime("%Y-%m-%d")
        return start_date, end_date


class Login:
    """处理登录操作的类"""

    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.session = self.session_manager.get_session()
        self.sensorsAnonymousId = "1932f022962709-09f55794af05688-********-2073600-1932f0229631113"

    def loginIN(self, uuid):
        secret_key_response = self.getLoginSecretKey(uuid)
        if secret_key_response and secret_key_response.get("code") == 1:
            secret_key = secret_key_response["data"]["secretKey"]
            secret_id = secret_key_response["data"]["secretId"]
            logging.info(f"登录密钥信息获取成功==>{secret_id} {secret_key}")

            # 第二步：登录
            account = "jszg01"
            password = "guornjszh1"
            encrypt_pwd = common_obj.encrypt(F=secret_key, q=password)
            x_ak_request_id = common_obj.getuuid()

            login_response = self.login(
                account, encrypt_pwd, uuid, x_ak_request_id, secret_id)
            logging.info(login_response)
            self.session_manager.load_session()

    def login(self, account, pwd, uuid, x_ak_request_id, secretId):
        """登录请求"""
        url = "https://gw.lingxingerp.com/newadmin/api/passport/login"

        payload = {
            "account": account,
            "pwd": pwd,
            "verify_code": "",
            "uuid": uuid,
            "auto_login": 1,
            "sensorsAnonymousId": self.sensorsAnonymousId,
            "secretId": secretId,
            "doubleCheckLoginReq": {
                "doubleCheckType": 1,
                "mobileLoginCode": "",
                "loginTick": ""
            }
        }

        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'ak-origin': 'https://muke.lingxing.com',
            'auth-token': '',
            'origin': 'https://muke.lingxing.com',
            'priority': 'u=1, i',
            'referer': 'https://muke.lingxing.com/',
            'sec-ch-ua': '"Chromium";v="130", "Microsoft Edge";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0',
            'x-ak-env-key': 'muke',
            'x-ak-request-id': x_ak_request_id,
            'x-ak-request-source': 'erp',
            'x-ak-version': 'AKVERSIONNUM',
            'x-ak-zid': '',
            'content-type': 'application/json;charset=UTF-8',
            'Host': 'gw.lingxingerp.com',
            'Connection': 'keep-alive'
        }

        response = self.session.post(
            url, data=json.dumps(payload), headers=headers)
        # print(response.json())
        if response.status_code == 200 and response.json().get('code') == 1:
            # print(response.json())

            # 删除特定的 cookies
            self.session_manager.remove_cookie("isCooperativeAccountLogin")
            self.session_manager.remove_cookie("cooperativeCustomer")
            self.session_manager.remove_cookie("coordinatedUsername")
            self.session_manager.remove_cookie("cooperativeAccount")
            self.session_manager.remove_cookie("cooperativeAccountUniqueKey")
            self.session_manager.remove_cookie("tokenExpireTime")

            # 设置新的 cookies
            e = response.json()
            token = urllib.parse.quote(e.get("token"))
            seller_auth_erp_url = urllib.parse.quote(e.get("sellerAuthErpUrl"))
            self.session_manager.set_cookie(
                "company_id", e.get("companyId"), expires=30)
            self.session_manager.set_cookie(
                "envKey", e.get("envKey"), expires=30)
            self.session_manager.set_cookie(
                "env_key", e.get("envKey"), expires=30)
            self.session_manager.set_cookie(
                "auth-token", e.get("token"), expires=30)
            self.session_manager.set_cookie(
                "authToken", token, expires=30)
            self.session_manager.set_cookie(
                "token", token, expires=30)
            self.session_manager.set_cookie("is_sellerAuth", "1", expires=30)
            # self.session_manager.set_cookie(
            #     "authToken", token, expires=30)

            self.session_manager.set_cookie("uid", e.get("uid"), expires=30)
            self.session_manager.set_cookie(
                "isNeedReset", str(e.get("needReset")), expires=30)
            if e.get("sellerAuthErpUrl"):
                self.session_manager.set_cookie(
                    "seller-auth-erp-url", seller_auth_erp_url, expires=30, domain="lingxing.com")
            is_pwd_notice = "1" if e.get("isPwdNotice") else "0"
            self.session_manager.set_cookie(
                "isUpdatePwd", is_pwd_notice, expires=30)
            self.session_manager.set_cookie(
                "zid", e.get("zid"), expires=30)  # 30 days
            self.session_manager.set_cookie("isLogin", "true", expires=0)
            # self.session_manager.check_cookie()
            # 登录成功后保存 session
            self.session_manager.save_session(self.session)
        else:
            logging.error(f"登录失败: {response.json()}")
            if response.json().get('code') == 1012107:
                logging.error("出现验证码")
                co = ChromiumOptions().set_local_port(9222)
                page = Chromium(co).new_tab()
                login_num = 1
                while login_num <= 3:
                    page.get("https://muke.lingxing.com/login")
                    if "login" not in page.url:
                        logging.info("登录成功")
                        # 获取浏览器中的 cookies 并同步到 session
                        cookies = page.cookies()
                        self.session.cookies = cookies

                        # 删除特定的 cookies
                        self.session_manager.remove_cookie("isCooperativeAccountLogin")
                        self.session_manager.remove_cookie("cooperativeCustomer")
                        self.session_manager.remove_cookie("coordinatedUsername")
                        self.session_manager.remove_cookie("cooperativeAccount")
                        self.session_manager.remove_cookie("cooperativeAccountUniqueKey")
                        self.session_manager.remove_cookie("tokenExpireTime")
                        # 将 Chromium cookies 列表转为 requests.cookies.RequestsCookieJar
                        jar = RequestsCookieJar()
                        for c in cookies:
                            jar.set(
                                c.get("name"),
                                c.get("value"),
                                domain=c.get("domain"),
                                path=c.get("path"),
                                expires=c.get("expires")
                            )
                        self.session.cookies = jar
                        self.session_manager.save_session(self.session)
                        logging.info("Session 已保存")
                        logging.info("请重新打开脚本运行")
                        exit(0)
                    logging.warning("请在浏览器中完成登录")
                    time.sleep(60)
                    login_num += 1
        return response.json()

    def getLoginSecretKey(self, uuid):
        """获取登录密钥"""
        url = "https://gw.lingxingerp.com/newadmin/api/passport/getLoginSecretKey"

        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'ak-origin': 'https://muke.lingxing.com',
            'auth-token': '',
            'origin': 'https://muke.lingxing.com',
            'priority': 'u=1, i',
            'referer': 'https://muke.lingxing.com/',
            'sec-ch-ua': '"Chromium";v="130", "Microsoft Edge";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0',
            'x-ak-env-key': 'muke',
            'x-ak-request-id': uuid,
            'x-ak-request-source': 'erp',
            'x-ak-version': 'AKVERSIONNUM',
            'x-ak-zid': '',
            'Host': 'gw.lingxingerp.com',
            'Connection': 'keep-alive'
        }

        response = self.session.post(url, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            logging.error(f"获取登录密钥失败: {response.text}")
            return None


class Task:
    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.req_session = self.session_manager.get_session()
        self.proxies = {
            "http": "http://127.0.0.1:8888",
            "https": "http://127.0.0.1:8888",
        }

    def get_cookie(self, name):
        """从 session 中获取指定的 cookie"""
        # 检查 cookie 是否存在
        cookie_value = self.req_session.cookies.get(name)

        if cookie_value:
            logging.info(f"Cookie 获取成功: {name} = {cookie_value}")
            return cookie_value
        else:
            logging.warning(f"Cookie {name} 不存在")
            return None

    def upload_imgs(self, file_url_or_path):
        url = 'https://erpapi.yxyglobal.com/index/upload/index'
        headers = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive',
            'Origin': 'https://erpapi.yxyglobal.com',
            'Referer': 'https://erpapi.yxyglobal.com',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36'
        }

        temp_file_path = None
        try:
            # 判断是URL还是本地文件路径
            if file_url_or_path.startswith('http://') or file_url_or_path.startswith('https://'):
                # 是URL，需要先下载
                logging.info(f"下载图片: {file_url_or_path}")

                # 创建临时目录
                if not os.path.exists("temp_images"):
                    os.makedirs("temp_images")

                # 生成临时文件名
                file_extension = os.path.splitext(file_url_or_path.split('?')[0])[-1] or '.jpg'
                temp_filename = f"temp_{int(time.time())}_{random.randint(1000, 9999)}{file_extension}"
                temp_file_path = os.path.join("temp_images", temp_filename)

                # 下载图片
                download_headers = {'User-Agent': random.choice([
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                ])}

                download_response = requests.get(file_url_or_path, headers=download_headers, timeout=30)
                if download_response.status_code != 200:
                    logging.error(f"下载图片失败: {file_url_or_path}, 状态码: {download_response.status_code}")
                    return {"code": 0, "message": "下载图片失败"}

                with open(temp_file_path, 'wb') as f:
                    f.write(download_response.content)

                file_path = temp_file_path
                filename = temp_filename
            else:
                # 是本地文件路径
                file_path = file_url_or_path
                filename = os.path.basename(file_path)

            # 检查文件是否存在
            if not os.path.exists(file_path):
                logging.error(f"文件不存在: {file_path}")
                return {"code": 0, "message": "文件不存在"}

            # 上传文件
            with open(file_path, 'rb') as img_file:
                files = {
                    'type': (None, 'image'),
                    'from': (None, '2'),
                    'file': (filename, img_file, 'image/jpeg')
                }
                # 打印上传请求内容
                logging.info(f"上传图片接口: {url}")
                logging.info(f"上传图片headers: {headers}")
                logging.info(f"上传图片files: type=image, from=2, file={filename}")
                # Send POST request with the file
                response = requests.post(url, headers=headers, files=files)

            # 检查响应状态
            if response.status_code != 200:
                logging.error(f"上传请求失败: 状态码 {response.status_code}")
                return {"code": 0, "message": f"上传请求失败: 状态码 {response.status_code}"}

            # 检查响应内容是否为空
            if not response.text.strip():
                logging.error("服务器返回空响应")
                return {"code": 0, "message": "服务器返回空响应"}

            # 尝试解析JSON
            try:
                result = response.json()
                logging.info(f"上传成功: {result}")
                return result
            except json.JSONDecodeError as e:
                logging.error(f"JSON解析失败: {e}, 响应内容: {response.text[:200]}")
                return {"code": 0, "message": f"JSON解析失败: {response.text[:100]}"}

        except Exception as e:
            logging.error(f"上传图片异常: {e}")
            return {"code": 0, "message": f"上传异常: {str(e)}"}
        finally:
            # 清理临时文件
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                    logging.info(f"清理临时文件: {temp_file_path}")
                except Exception as e:
                    logging.warning(f"清理临时文件失败: {e}")

    # 获取订单列表

    def OrderList(self, matched_store_ids, req_id, start_date, end_date, offset, length):
        token = urllib.parse.unquote(self.get_cookie("auth-token"))

        uid = self.get_cookie("uid")
        company_id = self.get_cookie('company_id')

        url = "https://gw.lingxingerp.com/cepf-oms-sw/list/order"

        payload = {
            "sort_field": "global_purchase_time",
            "sort_type": "desc",
            "status": "4",
            "site_code": [],
            "store_id": [],
            "platform_code": [],
            "search_field": "platform_order_name",
            "search_value": [""],
            "search_field_time": "global_purchase_time",
            "start_time": start_date,
            "end_time": end_date,
            "offset": offset,
            "length": length,
            "is_pending": "0",
            "receiver_country_code": [],
            "order_from": "",
            "order_type": "",
            "buyer_note_status": "",
            "remark_has": "",
            "platform_status": [],
            "address_type": "",
            "is_marking": "",
            "wid": "",
            "logistics_type_id": "",
            "logistics_provider_id": "",
            "asin_principal_uid": [],
            "req_time_sequence": "/cepf-oms-sw/list/order$$2"
        }
        if len(matched_store_ids) > 0:
            payload["store_id"] = matched_store_ids
        print(payload)
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'ak-client-type': 'web',
            'ak-origin': 'https://muke.lingxing.com',
            'auth-token': token,
            'origin': 'https://muke.lingxing.com',
            'priority': 'u=1, i',
            'referer': 'https://muke.lingxing.com/',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'sec-fetch-storage-access': 'active',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-ak-company-id': company_id,
            'x-ak-env-key': 'muke',
            'x-ak-language': 'zh',
            'x-ak-platform': '1',
            'x-ak-request-id': req_id,
            'x-ak-request-source': 'erp',
            'x-ak-uid': uid,
            'x-ak-version': '3.5.8.1.0.001',
            'x-ak-zid': '1',
            'content-type': 'application/json;charset=UTF-8'
        }

        response = requests.request("POST", url, headers=headers, json=payload)

        # print(response.json())
        return response.json()

    def getAllPlatformShopList(self, req_id):
        token = self.get_cookie("auth-token")
        uid = self.get_cookie("uid")

        company_id = self.get_cookie('company_id')
        url = "https://gw.lingxingerp.com/pb-mp-shop/api/getAllPlatformShopList"

        payload = {
            "uid": uid,
            "codeTo1": 1,
            "req_time_sequence": "/pb-mp-shop/api/getAllPlatformShopList$$1"
        }
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'ak-client-type': 'web',
            'ak-origin': 'https://muke.lingxing.com',
            'auth-token': token,
            'origin': 'https://muke.lingxing.com',
            'priority': 'u=1, i',
            'referer': 'https://muke.lingxing.com/',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'sec-fetch-storage-access': 'active',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-ak-company-id': company_id,
            'x-ak-env-key': 'muke',
            'x-ak-language': 'zh',
            'x-ak-platform': '1',
            'x-ak-request-id': req_id,
            'x-ak-request-source': 'erp',
            'x-ak-uid': uid,
            'x-ak-version': '3.5.8.1.0.001',
            'x-ak-zid': '1',
            'content-type': 'application/json;charset=UTF-8'
        }

        response = requests.request("POST", url, headers=headers, json=payload)

        return response.json()

    def downFile(self, report_id):
        url = f"https://muke.lingxing.com/api/download/downloadCenterReport/downloadResource?report_id={report_id}"
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }

        # 填充 req_session 的 cookies
        cookies = self.req_session.cookies.get_dict()
        print(cookies)
        response = requests.get(url, headers=headers, cookies=cookies)

        if response.status_code == 200:
            file_name = f"{report_id}.zip"
            folder_name = "定制信息导出"
            os.makedirs(folder_name, exist_ok=True)

            with open(file_name, 'wb') as f:
                f.write(response.content)
            logging.info(f"文件已成功下载并保存为 {file_name}")

            # 解压文件到指定文件夹
            try:
                with zipfile.ZipFile(file_name, 'r') as zip_ref:
                    zip_ref.extractall(folder_name)
                logging.info(f"文件已成功解压到文件夹 {folder_name}")
                os.remove(file_name)  # 删除原始压缩包
                logging.info(f"原始压缩包 {file_name} 已删除")
            except Exception as e:
                logging.error(f"解压文件失败: {e}")
                return

            # 遍历解压后的文件夹，检查是否还有压缩包
            for root, _, files in os.walk(folder_name):
                for file in files:
                    if file.endswith(".zip"):
                        zip_path = os.path.join(root, file)
                        try:
                            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                                zip_ref.extractall(root)
                            logging.info(f"文件 {zip_path} 已成功解压")
                            os.remove(zip_path)  # 删除解压后的压缩包
                            logging.info(f"压缩包 {zip_path} 已删除")
                        except Exception as e:
                            logging.error(f"解压文件 {zip_path} 失败: {e}")
        else:
            logging.error(
                f"文件下载失败，状态码: {response.status_code}, 响应: {response.text}")

    def export_file(self, req_uid, stores):
        token = self.get_cookie("auth-token")
        uid = self.get_cookie("uid")
        company_id = self.get_cookie("company_id")

        url = "https://gw.lingxingerp.com/cepf-oms-api/order/exportAttachmentCreate"

        payload = {
            "global_order_no": stores,
            "file_type": 2,
            "req_time_sequence": "/cepf-oms-api/order/exportAttachmentCreate$$1"
        }
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'ak-client-type': 'web',
            'ak-origin': 'https://muke.lingxing.com',
            'auth-token': token,
            'origin': 'https://muke.lingxing.com',
            'priority': 'u=1, i',
            'referer': 'https://muke.lingxing.com/',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'sec-fetch-storage-access': 'active',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-ak-company-id': company_id,
            'x-ak-env-key': 'muke',
            'x-ak-language': 'zh',
            'x-ak-platform': '1',
            'x-ak-request-id': req_uid,
            'x-ak-request-source': 'erp',
            'x-ak-uid': uid,
            'x-ak-version': '3.5.8.1.0.001',
            'x-ak-zid': '1',
            'content-type': 'application/json;charset=UTF-8'
        }

        response = requests.request("POST", url, headers=headers, json=payload)

        return response.json()

    def get_export_list(self, req_uid, start_time, end_time, offset, length):
        token = self.get_cookie("auth-token")
        uid = self.get_cookie("uid")
        company_id = self.get_cookie("company_id")

        url = f"https://muke.lingxing.com/api/download/downloadCenterReport/getReportData?offset={offset}&length={length}&report_time_type=0&start_time={start_time}&end_time={end_time}&keyword=%E8%AE%A2%E5%8D%95%E5%95%86%E5%93%81%E9%99%84%E4%BB%B6-&req_time_sequence=%2Fapi%2Fdownload%2FdownloadCenterReport%2FgetReportData$$25"

        payload = {}
        headers = {
            'AK-Client-Type': 'web',
            'AK-Origin': 'https://muke.lingxing.com',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Connection': 'keep-alive',
            'Referer': 'https://muke.lingxing.com/erp/muser/downloadCenter',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'X-AK-Company-Id': company_id,
            'X-AK-ENV-KEY': 'muke',
            'X-AK-Language': 'zh',
            'X-AK-PLATFORM': '1',
            'X-AK-Request-Id': req_uid,
            'X-AK-Request-Source': 'erp',
            'X-AK-Uid': uid,
            'X-AK-Version': '3.5.8.1.0.001',
            'X-AK-Zid': '1',
            'auth-token': token,
            'sec-ch-ua': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }

        response = requests.request("GET", url, headers=headers, data=payload)

        return response.json()


class ExcelTask:
    def __init__(self):
        pass

    # def create_excel_with_timestamp(self):
    #     """创建一个包含当前时间和时间戳的 Excel 文件（使用 win32com）"""

    #     # 获取当前日期和时间戳
    #     current_date = datetime.now().strftime("%Y%m%d")
    #     timestamp = int(time.time())

    #     # 文件名格式：定制信息导出-日期-时间戳.xlsx
    #     file_name = f"定制信息导出-{current_date}-{timestamp}.xlsx"

    #     # 使用 win32com 创建 Excel 文件
    #     excel = win32.Dispatch("Excel.Application")
    #     excel.Visible = False  # 不显示 Excel 窗口
    #     workbook = excel.Workbooks.Add()
    #     sheet = workbook.Sheets(1)
    #     sheet.Name = "Sheet1"

    #     # 保存文件
    #     workbook.SaveAs(os.path.abspath(file_name))
    #     workbook.Close(SaveChanges=0)
    #     excel.Quit()
    #     logging.info(f"Excel 文件已创建: {file_name}")

    #     return file_name


if __name__ == "__main__":
    # 检查通途可用性
    tongtool_task = tongtool.DowningTongtool()
    tongtool_status = tongtool_task.check_login_status()
    if not tongtool_status:
        logging.error("通途登录状态异常，请检查通途登录状态")
        exit(1)
    img_url = "http://img.yxyglobal.com"
    current_timestamp = int(time.time())
    logging.info(f"当前时间戳: {current_timestamp}")
    excel_task = ExcelTask()
    # excel_file = excel_task.create_excel_with_timestamp()

    read_config = CommonClass().read_config()
    print(read_config)
    # while True:
    status_map_str = read_config.get("Status").get("status_map")
    status_map = dict(item.split(":") for item in status_map_str.split(","))
    start_time = read_config.get("BrowserInfo").get("start_time")
    end_time = read_config.get("BrowserInfo").get("end_time")
    store_map = read_config.get("BrowserInfo").get("store_name")
    store_names = store_map.split(",")
    if start_time != "" and end_time != "":
        logging.info(f"开始时间: {start_time}, 结束时间: {end_time}")
    else:
        logging.info("当前未设置开始与结束时间，将默认拉取昨天数据")

    # logging.info(status_map)
    # try:
    # 初始化工具和会话管理
    session_manager = SessionManager()
    common_obj = CommonClass()
    login_task = Login(session_manager)
    session = session_manager.load_session()
    # 清理定制信息导出目录下的所有文件和文件夹
    export_folder = "定制信息导出"
    if os.path.exists(export_folder):
        for root, dirs, files in os.walk(export_folder, topdown=False):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    # logging.info(f"已删除文件: {file_path}")
                except Exception as e:
                    logging.error(f"删除文件失败: {file_path}, 错误: {e}")
            for dir in dirs:
                dir_path = os.path.join(root, dir)
                try:
                    os.rmdir(dir_path)
                    logging.info(f"已删除文件夹: {dir_path}")
                except Exception as e:
                    logging.error(f"删除文件夹失败: {dir_path}, 错误: {e}")
    else:
        logging.info(f"目录 {export_folder} 不存在，无需清理")
    if not session:
        get_uuid = common_obj.getuuid()
        login_task.loginIN(uuid=get_uuid)

    task_run = Task(session_manager)
    uid_str = common_obj.getuuid()
    # 获取所有店铺站点信息
    PlatformShopList = task_run.getAllPlatformShopList(uid_str)
    # 提取店铺名列表
    matched_store_ids = []

    # 遍历 PlatformShopList 数据，添加店铺id
    for store in PlatformShopList.get("data", []):
        # 提取店铺名（name 的最后一个 '-' 前的部分）
        if len(store_names) == 0:
            # logging.info("没有设置店铺名称，默认匹配所有店铺")
            matched_store_ids.append(store.get("store_id"))
            continue
        else:
            store_name = store.get("name", "").rsplit("-", 1)[0]
            if store_name in store_names:
                matched_store_ids.append(store.get("store_id"))

    logging.info(f"匹配的 store_ids 数量: {len(matched_store_ids)}")
    if start_time != "" and end_time != "":
        # 使用指定的开始和结束时间
        yestoday = start_time + " 00:00:00"
        today = end_time + " 23:59:59"
        logging.info(f"使用指定的开始时间: {yestoday}, 结束时间: {today}")
    else:
        yestoday, today = common_obj.get_yestoday_to_today()
    logging.info(f"yestoday: {yestoday}, today: {today}")
    total_num = 0
    index_num = 0
    index_length = 100
    order_ids = []
    # 去除案例数量限制
    # downloaded_count = 0  # 新增计数器
    # max_download = 2      # 最多下载2个案例
    # stop_flag = False     # 控制外层循环
    while True:
        uid_str = common_obj.getuuid()
        orders = task_run.OrderList(
            matched_store_ids, uid_str, yestoday, today, index_num * index_length, index_length)
        print(orders)
        print("打印订单")

        order_list = orders.get("data", {}).get("list", [])
        order_ids = order_list + order_ids
        total_num = total_num + len(order_list)
        total = orders.get("data", {}).get("total", 0)
        logging.info(f"当前页码-{index_num + 1},数据量-{total_num}，总数据量：{total}")
        platform_order_nos = []
        if len(order_list) > 0:
            uid_str = common_obj.getuuid()
            for item in order_list:
                order_items = item.get("order_item", [])
                if len(order_items) == 0:
                    logging.info(
                        f"订单号：{item.get('global_order_no')}，没有订单项，跳过")
                    continue
                else:
                    for order_i in order_items:
                        if order_i.get("new_attachment", []) == [] and order_i.get("remark", "") == "":
                            logging.info(
                                f"订单号：{item.get('global_order_no')}，订单项：{order_i.get('order_item_no')}，没有定制信息，跳过")
                            continue
                        platform_order_no = order_i.get("platform_order_no", "")
                        if platform_order_no not in platform_order_nos:
                            platform_order_nos.append(platform_order_no)
        if len(platform_order_nos) > 0:
            tongtool_task.run(platform_order_nos)
        index_num = index_num + 1
        if total <= total_num:
            logging.info("没有更多数据了，结束循环")
            break

    # 获取定制信息导出目录下的第一层文件夹名称
    folder_names = []
    excel_dict = []
    if os.path.exists("temp_images"):
        for temp_file in os.listdir("temp_images"):
            temp_file_path = os.path.join("temp_images", temp_file)
            try:
                os.remove(temp_file_path)
            except Exception as e:
                logging.error(f"删除文件失败: {temp_file_path}, 错误: {e}")
    else:
        os.makedirs("temp_images", exist_ok=True)
    if os.path.exists(export_folder):
        folder_names = [name.split("_")[0] for name in os.listdir(
            export_folder) if os.path.isdir(os.path.join(export_folder, name))]
        logging.info(f"定制信息导出目录下的第一层文件夹名称: {folder_names}")
        for item in order_ids:
            tag_type_deal = item.get("tag_type_deal", [])
            cancle_tag = False
            for tag_item in tag_type_deal:
                tag_name = tag_item.get("tag_name", "")
                if tag_name == "买家申请取消":
                    cancle_tag = True
            if cancle_tag:
                logging.info(
                    f"订单号：{item.get('global_order_no')}，买家申请取消，跳过")
                continue
            order_data_items = item.get("order_item", [])
            for order_i in order_data_items:
                platform_order_no = order_i.get("platform_order_no", "")
                logging.info(f"当前订单号：{platform_order_no}")
                if platform_order_no in folder_names:
                    # 初始化数据字典

                    # folder_path 下面还有一层子文件夹，需要再遍历一层
                    folder_path = os.path.join(export_folder, platform_order_no)

                    global_payment_time = item.get("global_payment_time")
                    store_name = item.get("store_name")
                    statuts = item.get("status", "未知")
                    statuts_str = status_map.get(str(statuts), "未知")
                    for q_item in item.get("tag_type_deal", []):
                        statuts_str = statuts_str + '\n' + \
                                      q_item.get("tag_name", "")

                    # logging.info(
                    #     f"订单号：{item.get('global_order_no')}，付款时间：{global_payment_time}，店铺名称：{store_name}，订单状态：{statuts_str}")
                    order_items = item.get("order_item", [])

                    logging.info(
                        f"订单号-{platform_order_no}=>订单数量：{len(order_items)}")
                    for order_item in order_items:
                        data_dict = {
                            "date": "",
                            "data_type": "",
                            "store_name": "",
                            "is_grass_screen": "",
                            "model_config": "",
                            "model_size": "",
                            "order_item_no": "",
                            "json_file": "",
                            "model_before": "",
                            "sku1": "",
                            "quantity1": "",
                            "sku2": "",
                            "quantity2": "",
                            "sku3": "",
                            "quantity3": "",
                            "sku4": "",
                            "quantity4": "",
                            "mku_img": "",
                            "mku_img_url": "",
                            "design_01_text": "",
                            "design_01_front_text": "",
                            "design_01_front_color": "",
                            "design_02_text": "",
                            "design_02_front_text": "",
                            "design_02_front_color": "",
                            "design_03_text": "",
                            "design_03_front_text": "",
                            "design_03_front_color": "",
                            "icon_text": "",
                            "border_text": "",
                            "base_img_template": "",
                            "snapshot_image": "",
                            "back_snapshot_image": "",
                            "snapshot_image_url": "",
                            "is_img_border": "",
                            "is_text_line": "",
                            "design_text": "",
                            "screen_image": "",
                            "design_image_file_01": "",
                            "design_image_01": "",
                            "design_image_01_conf": "",
                            "design_image_file_02": "",
                            "design_image_02": "",
                            "design_image_02_conf": "",
                            "design_image_file_03": "",
                            "design_image_03": "",
                            "design_image_03_conf": "",
                            "design_image_file_04": "",
                            "design_image_04": "",
                            "design_image_04_conf": "",
                            "design_image_file_05": "",
                            "design_image_05": "",
                            "design_image_05_conf": "",
                            "design_image_file_06": "",
                            "design_image_06": ""

                        }
                        msku = order_item.get("msku")
                        asin = order_item.get("asin")
                        order_item_no = order_item.get("order_item_no")
                        local_sku = order_item.get("local_sku", "")
                        if order_item_no in local_sku:
                            logging.error("定制已配对别名，跳过")
                            continue
                        main_image = order_item.get("pic_url", "")
                        data_dict["date"] = global_payment_time
                        data_dict["store_name"] = store_name
                        # data_dict["status"] = statuts_str
                        data_dict["sku1"] = msku
                        data_dict["quantity1"] = order_item.get("quantity", "")
                        if main_image:
                            main_url = main_image.rsplit("/", 1)[0]
                            main_image = main_image.rsplit(
                                "/", 1)[-1].split("._")[0] + ".jpg"
                            data_dict["mku_img"] = main_url + "/" + \
                                                   main_image.split(".jpg")[0] + "._SL200_" + ".jpg"
                            data_dict["mku_img_url"] = main_url + "/" + \
                                                       main_image.split(".jpg")[0] + "._SL200_" + ".jpg"
                        subfolders = os.path.join(folder_path, platform_order_no + "_" + order_item_no)
                        local_folder = subfolders
                        json_file_path = os.path.join(
                            subfolders, order_item_no + ".json")
                        try:
                            data_dict['json_file'] = order_item_no
                            with open(json_file_path, 'r', encoding='utf-8') as file:
                                json_data = json.load(file)
                                # logging.info(
                                #     f"读取文件 {json_file_path} 成功: {json_data}")
                                # 订单号
                                orderId = json_data.get("orderId", "")
                                data_dict["order_item_no"] = orderId
                                # 配置数据
                                customizationData = json_data.get(
                                    "customizationData", {})
                                child_num = 1
                                image_num = 0
                                text_num = 0
                                selection_dict = {}
                                is_glass_screen = False
                                is_Borders_and_Lines = False
                                is_image_border = False
                                img_template = ""

                                # if
                                for item in customizationData.get("children", []):
                                    if child_num == 1:
                                        data_dict["snapshot_image"] = os.path.join(local_folder, item.get(
                                            "snapshot").get("imageName"))
                                        data_dict["snapshot_image_url"] = os.path.join(local_folder, item.get(
                                            "snapshot").get("imageName"))
                                    if child_num == 2:
                                        data_dict["back_snapshot_image"] = os.path.join(local_folder, item.get(
                                            "snapshot").get("imageName"))
                                    child_num = child_num + 1
                                    childrens = item.get("children", [])

                                    for item_child in childrens:
                                        children_items = item_child.get(
                                            "children", [])
                                        for children_item in children_items:

                                            if "图片" in children_item.get("name"):
                                                image_num += 1
                                                for item_img in children_item.get("children", []):
                                                    # print("图片在这：", item_img)
                                                    image_file = item_img.get(
                                                        "image", {}).get("imageName", "")

                                                    if image_num == 1:
                                                        data_dict['design_image_file_01'] = os.path.join(
                                                            local_folder, image_file)
                                                        data_dict['design_image_01'] = image_file
                                                        data_dict['design_image_01_conf'] = children_item
                                                    elif image_num == 2:
                                                        data_dict['design_image_file_02'] = os.path.join(
                                                            local_folder, image_file)
                                                        data_dict['design_image_02'] = image_file
                                                        data_dict['design_image_02_conf'] = children_item
                                                    elif image_num == 3:
                                                        data_dict['design_image_file_03'] = os.path.join(
                                                            local_folder, image_file)
                                                        data_dict['design_image_03'] = image_file
                                                        data_dict['design_image_03_conf'] = children_item
                                                    elif image_num == 4:
                                                        data_dict['design_image_file_04'] = os.path.join(
                                                            local_folder, image_file)
                                                        data_dict['design_image_04'] = image_file
                                                        data_dict['design_image_04_conf'] = children_item
                                                    elif image_num == 5:
                                                        data_dict['design_image_file_05'] = os.path.join(
                                                            local_folder, image_file)
                                                        data_dict['design_image_05'] = image_file
                                                        data_dict['design_image_05_conf'] = children_item
                                                    elif image_num == 6:
                                                        data_dict['design_image_file_06'] = os.path.join(
                                                            local_folder, image_file)
                                                        data_dict['design_image_06'] = image_file
                                                        data_dict['design_image_06_conf'] = children_item

                                            elif "文本" in children_item.get("name"):
                                                text_num = text_num + 1
                                                for item_text in children_item.get("children", []):

                                                    # 字体
                                                    if item_text.get("type") == "FontCustomization":
                                                        data_dict["design_text"] = data_dict[
                                                                                       "design_text"] + "字体：" + item_text.get(
                                                            "fontSelection", {}).get("family", "") + "\n"
                                                        # if text_num == 1:
                                                        data_dict[f'design_0{text_num}_front_text'] = item_text.get(
                                                            "fontSelection", {}).get("family", "")

                                                    # 字体颜色
                                                    elif item_text.get("type", "") == "ColorCustomization":
                                                        data_dict["design_text"] = data_dict[
                                                                                       "design_text"] + "字体颜色：" + item_text.get(
                                                            "colorSelection", {}).get(
                                                            "name", "") + "(" + item_text.get("colorSelection", {}).get(
                                                            "value", "") + ")" + "\n"
                                                        data_dict[f"design_0{text_num}_front_color"] = item_text.get(
                                                            "colorSelection", {}).get("value", "")

                                                    #
                                                    elif item_text.get("type", "") == "ContainerCustomization":
                                                        for i_item in item_text.get("children", []):
                                                            if i_item.get("type") == "PlacementContainerCustomization":
                                                                for j_item in i_item.get("children", []):
                                                                    if j_item.get("type",
                                                                                  "") == "FlatRatePriceDeltaContainerCustomization":
                                                                        for k_item in j_item.get("children", []):
                                                                            if k_item.get("type",
                                                                                          "") == "TextCustomization":
                                                                                data_dict["design_text"] = data_dict[
                                                                                                               "design_text"] + "定制文字：" + k_item.get(
                                                                                    "inputValue", "") + "\n"
                                                                                data_dict[
                                                                                    f'design_0{text_num}_text'] = k_item.get(
                                                                                    "inputValue", "")

                                            elif "OptionCustomization" in children_item.get("type", ""):
                                                if children_item.get("optionSelection") != "":
                                                    optionkey = children_item.get(
                                                        "name", "") + "_" + children_item.get("label", "")
                                                    select_label = children_item.get(
                                                        "optionSelection", {}).get("label", "")
                                                    if "Selezionare il modello" in optionkey:
                                                        data_dict["fixed_img_template"] = select_label
                                                    if select_label == "Rahmen und Linien":
                                                        data_dict["is_img_border"] = "是"
                                                        data_dict["is_text_line"] = "是"

                                                    pattern = r'^BK\d+$'
                                                    if re.match(pattern, select_label):
                                                        data_dict['border_text'] = select_label
                                                    if re.search(r'glass screen protector',
                                                                 select_label) or re.search(
                                                        r'Glass Screen Protector', optionkey):
                                                        data_dict["is_grass_screen"] = "是"
                                                    data_dict['model_config'] = data_dict['model_config'] + \
                                                                                optionkey + ":" + select_label + "\n"

                                logging.info(
                                    f"order_no-{order_item_no}-图片数量：{image_num}，文本数量：{text_num}，选择项数量：{len(selection_dict)}")
                                if image_num > 1:
                                    data_dict["data_type"] = "多图+文字"
                                elif image_num == 1 and text_num > 0:
                                    data_dict["data_type"] = "单图+文字"
                                elif image_num == 1 and text_num == 0:
                                    data_dict["data_type"] = "单图"
                                elif len(selection_dict) > 0 and image_num == 0:
                                    data_dict["data_type"] = "单文字"
                                    key_list = ["Selezionare il modello", "Pattern Options", "Optionen für das Muster",
                                                "Options de motif", "Opzioni del modello", "Opciones de patrón"]
                                    for key in selection_dict:
                                        if key.split("_")[1] in key_list:
                                            data_dict["data_type"] = "固定底图＋文字"
                                            break
                                elif text_num > 0 and image_num == 0 and len(selection_dict) == 0:
                                    data_dict["data_type"] = "单文字"
                                else:
                                    data_dict["data_type"] = "未知类型"

                            excel_dict.append(data_dict)

                        except Exception as e:
                            logging.error(f"读取文件 {json_file_path} 失败: {e}")

                elif len(item.get("order_item", [])) > 0:
                    logging.warning(
                        f"备注订单号-{platform_order_no}=>订单数量：{len(item.get('order_item', []))}"
                    )
                    global_payment_time = item.get("global_payment_time")
                    store_name = item.get("store_name")
                    statuts = item.get("status", "未知")
                    statuts_str = status_map.get(str(statuts), "未知")
                    for q_item in item.get("tag_type_deal", []):
                        statuts_str = statuts_str + '\n' + \
                                      q_item.get("tag_name", "")

                    for order_item in item.get("order_item", []):
                        if order_item.get("remark", "") != "":
                            data_dict = {
                                "date": "",
                                "data_type": "",
                                "store_name": "",
                                "is_grass_screen": "",
                                "model_config": "",
                                "model_size": "",
                                "order_item_no": "",
                                "json_file": "",
                                "model_before": "",
                                "sku1": "",
                                "quantity1": "",
                                "sku2": "",
                                "quantity2": "",
                                "sku3": "",
                                "quantity3": "",
                                "sku4": "",
                                "quantity4": "",
                                "mku_img": "",
                                "mku_img_url": "",
                                "design_01_text": "",
                                "design_01_front_text": "",
                                "design_01_front_color": "",
                                "design_02_text": "",
                                "design_02_front_text": "",
                                "design_02_front_color": "",
                                "design_03_text": "",
                                "design_03_front_text": "",
                                "design_03_front_color": "",
                                "icon_text": "",
                                "border_text": "",
                                "base_img_template": "",
                                "snapshot_image": "",
                                "back_snapshot_image": "",
                                "snapshot_image_url": "",
                                "is_img_border": "",
                                "is_text_line": "",
                                "design_text": "",
                                "screen_image": "",
                                "design_image_file_01": "",
                                "design_image_01": "",
                                "design_image_01_conf": "",
                                "design_image_file_02": "",
                                "design_image_02": "",
                                "design_image_02_conf": "",
                                "design_image_file_03": "",
                                "design_image_03": "",
                                "design_image_03_conf": "",
                                "design_image_file_04": "",
                                "design_image_04": "",
                                "design_image_04_conf": "",
                                "design_image_file_05": "",
                                "design_image_05": "",
                                "design_image_05_conf": "",
                                "design_image_file_06": "",
                                "design_image_06": ""

                            }
                            msku = order_item.get("msku")
                            asin = order_item.get("asin")
                            order_item_no = order_item.get("order_item_no", "")
                            platform_order_no = order_item.get("platform_order_no")
                            main_image = order_item.get("pic_url", "")
                            data_dict["date"] = global_payment_time
                            data_dict["store_name"] = store_name
                            data_dict["order_item_no"] = platform_order_no
                            # data_dict["status"] = statuts_str
                            data_dict["sku1"] = msku
                            data_dict["quantity1"] = order_item.get("quantity", "")
                            if main_image:
                                main_url = main_image.rsplit("/", 1)[0]
                                main_image = main_image.rsplit(
                                    "/", 1)[-1].split("._")[0] + ".jpg"
                                data_dict["mku_img"] = main_url + "/" + \
                                                       main_image.split(".jpg")[0] + "._SL200_" + ".jpg"
                                data_dict["mku_img_url"] = main_url + "/" + \
                                                           main_image.split(".jpg")[0] + "._SL200_" + ".jpg"
                            data_dict["design_text"] = order_item.get("remark", "")
                            data_dict["data_type"] = "其他备注"
                            excel_dict.append(data_dict)


        def download_image(url, save_path):
            try:
                headers = {'User-Agent': random.choice([
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 14_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
                ])}
                response = requests.get(url, headers=headers, timeout=60)
                if response.status_code == 200:
                    with open(save_path, 'wb') as f:
                        f.write(response.content)
                    return save_path
            except Exception as e:
                logging.error(f"下载失败: {url} 错误: {e}")
            return None


        def export_to_excel_xlsxwriter(data, out_dir="."):
            """
            使用 XlsxWriter 将 data 导出到 Excel，并在指定列插入图片。
            data: list of dict，字段与之前 COM 版一一对应。
            out_dir: 输出目录
            """
            # 1. 生成文件名
            current_date = datetime.now().strftime("%Y-%m-%d")
            timestamp = int(time.time())
            filename = f"{current_date}-订单信息导出-{timestamp}.xlsx"
            filepath = os.path.join(out_dir, filename)

            # 2. 创建工作簿和三个 Worksheet
            wb = xlsxwriter.Workbook(filepath, {'constant_memory': True})
            ws1 = wb.add_worksheet("JSON数据")
            ws2 = wb.add_worksheet("内部作图数据")
            ws3 = wb.add_worksheet("外部作图数据")

            # 3. 定义表头
            headers1 = [
                "日期", "类型", "店铺", "是否加钢化膜", "型号配置等", "皮套尺寸", "订单编号", "json文件名",
                "底壳前缀", "平台SKU1", "平台SKU1数量", "平台SKU2", "平台SKU2数量", "平台SKU3",
                "平台SKU3数量", "平台SKU4", "平台SKU4数量", "MSKU图片", "MSKU图片链接",
                "定制文案-1", "字体", "字体颜色", "定制文案-2", "字体", "字体颜色", "定制文案-3",
                "字体", "字体颜色", "图标", "边框", "底图模板", "效果图", "背面预览效果",
                "预览效果转图片链接", "图案是否加边框", "文字是否加线条", "定制内容", "截图",
                "图片1文件名", "定制图片1", "图片1参数", "图片2文件名", "定制图片2", "图片2参数",
                "图片3文件名", "定制图片3", "图片3参数", "图片4文件名", "定制图片4", "图片4参数",
                "图片5文件名", "定制图片5", "图片5参数", "图片6文件名", "定制图片6", "图片6参数"
            ]
            headers2 = [
                "日期", "类型", "店铺", "皮套尺寸", "订单编号", "json文件名", "底壳前缀", "平台SKU1", "平台SKU1数量",
                "定制文案-1", "字体", "字体颜色", "定制文案-2", "字体", "字体颜色", "定制文案-3",
                "字体", "字体颜色", "图标", "边框", "底图模板", "效果图", "图案是否加边框", "文字是否加线条",
                "定制内容", "截图", "图片1文件名", "图片1参数", "图片2文件名", "图片2参数",
                "图片3文件名", "图片3参数", "图片4文件名", "图片4参数"
            ]
            headers3 = [
                "日期", "类型", "店铺", "订单号", "MSKU", "下单数量", "图片", "图片链接",
                "定制图片1", "定制图片2", "定制图片3", "定制图片4", "定制图片5",
                "定制图片6", "预览效果", "背面预览效果", "预览效果转图片链接", "定制文字信息"
            ]

            # 4. 写入表头
            for col, h in enumerate(headers1):
                ws1.write(0, col, h)
            for col, h in enumerate(headers2):
                ws2.write(0, col, h)
            for col, h in enumerate(headers3):
                ws3.write(0, col, h)

            # 5. 格式设置
            # 文本格式（确保 json 文件名列是文本）
            text_fmt = wb.add_format({'num_format': '@'})
            ws1.set_column(7, 7, 20, text_fmt)  # H 列
            # 固定行高、列宽
            ws1.set_default_row(80)
            ws2.set_default_row(80)
            ws3.set_default_row(80)
            ws1.set_column(0, len(headers1) - 1, 15)
            ws2.set_column(0, len(headers2) - 1, 15)
            ws3.set_column(0, len(headers3) - 1, 15)

            # 6. 图片字段对应 (sheet1, 0-based 列号)
            image_map = {
                'mku_img_url': 17,
                'snapshot_image_url': 31,
                'back_snapshot_image': 32,
                'design_image_file_01': 39,
                'design_image_file_02': 42,
                'design_image_file_03': 45,
                'design_image_file_04': 48,
                'design_image_file_05': 51,
                'design_image_file_06': 54,
            }

            # 7. 写入 JSON 数据表以及插图
            for r, item in enumerate(data, start=1):
                snapshot_link = ""
                raw_url = item.get("snapshot_image_url", "")
                if raw_url and any(ext in raw_url.lower() for ext in ["jpg", "jpeg", "png"]):
                    try:
                        result = task_run.upload_imgs(raw_url)
                        if result.get("code") == 1:
                            snapshot_link = img_url + result["data"]["file"]
                            logging.info(f"图片上传成功: {raw_url} -> {snapshot_link}")
                        else:
                            logging.warning(f"图片上传失败: {raw_url}, 返回: {result}")
                    except Exception as e:
                        logging.error(f"图片上传异常: {raw_url}, 错误: {e}")

                print(f"snapshot_link: {snapshot_link}")
                # 组装一行数据（除图片外）
                row_data1 = [
                    item.get("date", ""), item.get("data_type"), item.get(
                        "store_name", ""), item.get("is_grass_screen", ""),
                    item.get("model_config", ""), item.get("model_size", ""), item.get(
                        "order_item_no", ""), item.get("json_file", ""), item.get("model_before", ""),
                    item.get("sku1", ""), item.get("quantity1", ""), item.get(
                        "sku2", ""), item.get("quantity2", ""),
                    item.get("sku3", ""), item.get("quantity3", ""), item.get(
                        "sku4", ""), item.get("quantity4", ""), "",
                    item.get("mku_img_url", ""), item.get(
                        "design_01_text", ""), item.get("design_01_front_text", ""),
                    item.get("design_01_front_color", ""), item.get(
                        "design_02_text", ""), item.get("design_02_front_text", ""),
                    item.get("design_02_front_color", ""), item.get(
                        "design_03_text", ""), item.get("design_03_front_text", ""),
                    item.get("design_03_front_color", ""), item.get(
                        "icon_text", ""), item.get("border_text", ""),
                    item.get("base_img_template", ""), "", "", snapshot_link,
                    item.get("is_img_border", ""), item.get("is_text_line", ""), item.get(
                        "design_text", ""), item.get("screen_image", ""),
                    item.get("design_image_01", ""), "", str(
                        item.get("design_image_01_conf", "")),
                    item.get("design_image_02", ""), "", str(
                        item.get("design_image_02_conf", "")),
                    item.get("design_image_03", ""), "", str(
                        item.get("design_image_03_conf", "")),
                    item.get("design_image_04", ""), "", str(
                        item.get("design_image_04_conf", "")),
                    item.get("design_image_05", ""), "", str(
                        item.get("design_image_05_conf", "")),
                    item.get("design_image_06", ""), "", str(
                        item.get("design_image_06_conf", ""))

                ]

                for field, col in image_map.items():
                    url = item.get(field, "")
                    if not url:
                        continue
                    try:
                        if url.startswith('http://') or url.startswith('https://'):
                            # 网络图片

                            # 下载图片，失败重试最多3次
                            for attempt in range(3):
                                try:
                                    headers = {'User-Agent': random.choice([
                                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                                        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                                        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                                        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                                        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (iPad; CPU OS 14_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
                                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
                                        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
                                        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36",
                                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36",
                                        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36",
                                        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (iPad; CPU OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
                                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
                                        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
                                        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (iPad; CPU OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                                        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                                        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (iPad; CPU OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
                                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
                                        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
                                        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (iPad; CPU OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36",
                                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36",
                                        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36",
                                        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (iPad; CPU OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                                        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                                        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (iPad; CPU OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
                                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
                                        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
                                        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                                        "Mozilla/5.0 (iPad; CPU OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
                                    ])}

                                    resp = requests.get(url, timeout=30, headers=headers)
                                    if resp.status_code == 200:
                                        break
                                except Exception as e:
                                    if attempt == 2:
                                        raise
                                    time.sleep(1)
                            else:
                                raise Exception(f"Failed to download image after 3 attempts: {url}")
                            img_data = BytesIO(resp.content)
                            img = Image.open(img_data)
                            img_data.seek(0)
                            image_opt = {'image_data': img_data}
                        else:
                            # 本地图片
                            if not os.path.isabs(url):
                                url = os.path.abspath(url)
                            img = Image.open(url)
                            image_opt = {}

                        img_width, img_height = img.size

                        # 用实际单元格尺寸来缩放图片
                        col_width = 11
                        row_height = 80

                        cell_width = int(col_width * 7)  # 1列宽=7像素
                        cell_height = int(row_height * 96 / 72)  # 1pt=96/72像素

                        scale = min(cell_width / img_width, cell_height / img_height, 1.0)
                        display_width = int(img_width * scale)
                        display_height = int(img_height * scale)
                        x_offset = max((cell_width - display_width) // 2, 0)
                        y_offset = max((cell_height - display_height) // 2, 0)

                        image_opt.update({
                            'x_scale': scale,
                            'y_scale': scale,
                            'x_offset': x_offset,
                            'y_offset': y_offset,
                            'object_position': 1
                        })

                        ws1.insert_image(r, col, url, image_opt)
                    except Exception as e:
                        print(f"❌ 插图失败: {url}，错误: {e}")

                        # 写入
                for c, v in enumerate(row_data1):
                    ws1.write(r, c, v)
            # 8. 填写公式：内部作图 & 外部作图
            max_row = len(data) + 1

            # JSON 数据 -> 内部作图数据 sheet2
            for r in range(1, max_row + 1):
                ws2.write_formula(r, 0, f'=IF(JSON数据!A{r + 1}="","",JSON数据!A{r + 1})')  # 日期
                ws2.write_formula(r, 1, f'=JSON数据!B{r + 1}')  # 类型
                ws2.write_formula(r, 2, f'=JSON数据!C{r + 1}')  # 店铺
                ws2.write_formula(r, 3, f'=JSON数据!F{r + 1}')  # 皮套尺寸
                ws2.write_formula(r, 4, f'=JSON数据!G{r + 1}')  # 订单编号
                ws2.write_formula(r, 5, f'=JSON数据!H{r + 1}')  # json文件名
                ws2.write_formula(r, 6, f'=JSON数据!I{r + 1}')  # 底壳前缀
                ws2.write_formula(r, 7, f'=JSON数据!J{r + 1}')  # 平台SKU1
                ws2.write_formula(r, 8, f'=JSON数据!K{r + 1}')  # 平台SKU1数量
                ws2.write_formula(r, 9, f'=JSON数据!U{r + 1}')  # 定制文案-1
                ws2.write_formula(r, 10, f'=JSON数据!V{r + 1}')  # 字体
                ws2.write_formula(r, 11, f'=JSON数据!W{r + 1}')  # 字体颜色
                ws2.write_formula(r, 12, f'=JSON数据!X{r + 1}')  # 定制文案-2
                ws2.write_formula(r, 13, f'=JSON数据!Y{r + 1}')  # 字体
                ws2.write_formula(r, 14, f'=JSON数据!Z{r + 1}')  # 字体颜色
                ws2.write_formula(r, 15, f'=JSON数据!AA{r + 1}')  # 定制文案-3
                ws2.write_formula(r, 16, f'=JSON数据!AB{r + 1}')  # 字体
                ws2.write_formula(r, 17, f'=JSON数据!AC{r + 1}')  # 字体颜色
                ws2.write_formula(r, 18, f'=JSON数据!AD{r + 1}')  # 图标
                ws2.write_formula(r, 19, f'=JSON数据!AE{r + 1}')  # 边框
                ws2.write_formula(r, 20, f'=JSON数据!AF{r + 1}')  # 底图模板
                ws2.write_formula(r, 21, f'=JSON数据!AG{r + 1}')  # 效果图
                ws2.write_formula(r, 22, f'=JSON数据!AJ{r + 1}')  # 图案是否加边框
                ws2.write_formula(r, 23, f'=JSON数据!AK{r + 1}')  # 文字是否加线条
                ws2.write_formula(r, 24, f'=JSON数据!AL{r + 1}')  # 定制内容
                ws2.write_formula(r, 25, f'=JSON数据!AM{r + 1}')  # 截图
                ws2.write_formula(r, 26, f'=JSON数据!AN{r + 1}')  # 图片1文件名
                ws2.write_formula(r, 27, f'=JSON数据!AP{r + 1}')  # 图片1参数
                ws2.write_formula(r, 28, f'=JSON数据!AQ{r + 1}')  # 图片2文件名
                ws2.write_formula(r, 29, f'=JSON数据!AS{r + 1}')  # 图片2参数
                ws2.write_formula(r, 30, f'=JSON数据!AT{r + 1}')  # 图片3文件名
                ws2.write_formula(r, 31, f'=JSON数据!AV{r + 1}')  # 图片3参数
                ws2.write_formula(r, 32, f'=JSON数据!AW{r + 1}')  # 图片4文件名
                ws2.write_formula(r, 33, f'=JSON数据!AY{r + 1}')  # 图片4参数

            # JSON 数据 -> 外部作图数据 sheet3
            for r in range(1, max_row + 1):
                ws3.write_formula(r, 0, f'=JSON数据!A{r + 1}')  # 日期
                ws3.write_formula(r, 1, f'=JSON数据!B{r + 1}')  # 类型
                ws3.write_formula(r, 2, f'=JSON数据!C{r + 1}')  # 店铺
                ws3.write_formula(r, 3, f'=JSON数据!G{r + 1}')  # 订单编号
                ws3.write_formula(r, 4, f'=JSON数据!J{r + 1}')  # MSKU
                ws3.write_formula(r, 5, f'=JSON数据!K{r + 1}')  # 下单数量
                ws3.write_formula(r, 6, f'=JSON数据!R{r + 1}')  # 图片
                ws3.write_formula(r, 7, f'=JSON数据!S{r + 1}')  # 图片链接
                ws3.write_formula(r, 8, f'=JSON数据!AO{r + 1}')  # 定制图片1
                ws3.write_formula(r, 9, f'=JSON数据!AR{r + 1}')  # 定制图片2
                ws3.write_formula(r, 10, f'=JSON数据!AU{r + 1}')  # 定制图片3
                ws3.write_formula(r, 11, f'=JSON数据!AX{r + 1}')  # 定制图片4
                ws3.write_formula(r, 12, f'=JSON数据!BA{r + 1}')  # 定制图片5
                ws3.write_formula(r, 13, f'=JSON数据!BD{r + 1}')  # 定制图片6
                ws3.write_formula(r, 14, f'=JSON数据!AG{r + 1}')  # 预览效果
                ws3.write_formula(r, 15, f'=JSON数据!AH{r + 1}')  # 背面预览效果
                ws3.write_formula(r, 16, f'=JSON数据!AI{r + 1}')  # 预览效果转图片链接
                ws3.write_formula(r, 17, f'=JSON数据!AL{r + 1}')  # 定制文字信息

            # 9. 关闭并保存
            wb.close()
            print(f"✅ 导出成功：{filepath}")
            return filepath


        # # 执行导出
        # export_to_excel_wps(excel_dict

        #                     )
        export_to_excel_xlsxwriter(excel_dict, out_dir=".")

        # 清理临时图片文件
        if os.path.exists("temp_images"):
            for temp_file in os.listdir("temp_images"):
                temp_file_path = os.path.join("temp_images", temp_file)
                os.remove(temp_file_path)

            os.rmdir("temp_images")

    else:
        logging.error(f"目录 {export_folder} 不存在，无法读取文件夹名称")

        #

        # else:
        #     time.sleep(2)
        #     # 第一步：获取登录密钥
        #     get_uuid = common_obj.getuuid()
        #     login_task.loginIN(uuid=get_uuid)
        #     # continue

        # time.sleep(3600)
    # except Exception as e:
    #     logging.info(e)
