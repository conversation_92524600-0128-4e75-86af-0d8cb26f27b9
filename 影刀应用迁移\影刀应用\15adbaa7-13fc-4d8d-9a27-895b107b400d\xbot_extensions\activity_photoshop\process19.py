import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        ps_instance = None
        layer_name = ""
    else:
        ps_instance = args.get("ps_instance", None)
        layer_name = args.get("layer_name", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="invoke_module", package=__name__, function="delete_layer", params={
            "ps_instance": ps_instance,
            "layer_name": layer_name,
        }, _block=("删除图层", 1, "调用模块"))
    finally:
        pass
