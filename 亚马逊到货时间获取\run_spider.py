"""
Amazon配送信息爬虫运行脚本
"""
import sys
import os

import logging
from amazon_delivery_spider import AmazonDeliverySpider

def setup_logging():
    """
    设置日志配置
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('spider.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """
    主函数
    """
    try:
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        
        logger.info("=" * 50)
        logger.info("开始运行Amazon配送信息爬虫")
        logger.info("=" * 50)
        
        # 创建爬虫实例 - 使用单线程
        spider = AmazonDeliverySpider(
            thread_count=5,  # 单线程避免Playwright线程冲突
        )
        
        # 运行爬虫
        spider.run()
        
        logger.info("=" * 50)
        logger.info("爬虫运行完成")
        logger.info("=" * 50)
        
    except KeyboardInterrupt:
        logger.info("用户中断爬虫运行")
    except Exception as e:
        logger.error(f"爬虫运行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # os.environ["DEBUG"] = "pw:browser"
    main()
