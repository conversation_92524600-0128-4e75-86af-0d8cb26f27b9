import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        photoshop_instance = ""
        save_path = ""
    else:
        photoshop_instance = args.get("photoshop_instance", "")
        save_path = args.get("save_path", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="save_as_psd", params={
            "photoshop_instance": photoshop_instance,
            "save_path": save_path,
        }, _block=("另存为 PSD 文件", 1, "调用模块"))
    finally:
        pass
