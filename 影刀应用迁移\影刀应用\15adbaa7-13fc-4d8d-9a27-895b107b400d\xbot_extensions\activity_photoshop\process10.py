import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    layer_text = ""
    if args is None:
        ps_instance = None
        layer_name = ""
        only_visible = True
    else:
        ps_instance = args.get("ps_instance", None)
        layer_name = args.get("layer_name", "")
        only_visible = args.get("only_visible", True)
    try:
        layer_text = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="get_artlayer_text", params={
            "ps_instance": ps_instance,
            "layer_name": layer_name,
            "only_visible": only_visible,
        }, _block=("获取图层文字", 1, "调用模块"))
    finally:
        args["layer_text"] = layer_text
