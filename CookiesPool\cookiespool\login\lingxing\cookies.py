import random
import time
from utils_mrc.pub_fun import decode_values
from DrissionPage import ChromiumOptions, WebPage, ChromiumPage


class LingXingCookies:
    home_url = 'https://muke.lingxing.com/'
    home_url_ad = 'https://ads.lingxing.com/home'
    login_url_erp = 'https://muke.lingxing.com/login'
    login_url_ad = 'https://muke.lingxing.com/erp/adverAuthLogin?adver_url=/erp/login'  # 登录广告页

    def __init__(self, username, password):
        co = ChromiumOptions()
        co.set_local_port(9335)
        co.remove_extensions()
        # co.set_browser_path(f'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe')
        self.co = co
        self.browser = ChromiumPage(addr_or_opts=co)
        self.browser.set.auto_handle_alert()
        # self.browser.set.window.mini()
        # self.browser.set.window.hide()
        # self.page = self.browser.new_tab()
        self.page = self.browser
        self.username = username
        self.password = password

    def open(self):
        """
        打开网页输入用户名密码并点击
        :return: None
        """
        self.page.set.cookies.clear()
        self.login()

    def login(self):
        self.page.get(self.login_url_erp)
        if '/login' not in self.page.url:
            print(f'{self.username}登录erp成功！')
        elif self.page('tag:input@@name=account'):
            self.page('tag:input@@name=account').input(self.username, True)
            self.page('tag:input@@name=pwd').input(self.password, True)
            self.page('tag:span@@class=text-wrap@@text()=登录').click()
            time.sleep(2)
        else:
            print(f'{self.username}登录erp失败！')

    def login_successfully(self):
        """
        判断是否登录成功
        :return:
        """
        try:
            if '/login' not in self.page.url:
                print(f'{self.username}登录erp成功！')
            return True
        except Exception:
            return False

    def get_cookies(self):
        """
        获取Cookies
        :return:
        """
        cookies = self.page.cookies().as_dict()
        cookies = decode_values(cookies)
        return cookies

    def get_token(self):
        """
        获取token
        :return:
        """
        cookies = self.page.cookies().as_dict()
        token = decode_values(cookies.get('token') or cookies.get('auth-token') or cookies.get('authToken') or cookies.get('auth_token') or '')
        return token

    def main(self):
        """
        破解入口
        :return:
        """
        self.open()
        # cookies = self.get_cookies()
        token = self.get_token()
        self.browser.quit()
        if self.login_successfully and token:
            return {
                'status': 1,
                'content': token
            }
        else:
            return {
                'status': 3,
                'content': '登录失败'
            }

    def main_ad(self):
        self.open()
        if self.login_successfully():
            self.page.get(self.login_url_ad)
            time.sleep(8)
            self.page.get(self.home_url_ad)
            if self.page('返回ERP'):
                return {
                    'status': 1,
                    'content': self.page.cookies().as_dict()
                }
            else:
                return {
                    'status': 3,
                    'content': '登录失败'
                }


if __name__ == '__main__':
    s1 = time.time()
    # result = LingXingCookies('yxy023', 'yxy023..').main()
    result = LingXingCookies('jszg01', 'guornjszh1').main_ad()
    print(result)
    s2 = time.time()
    print(f'耗时: {s2 - s1:.6f}秒')
