import activity
import activity.彩底_挂绳单个装
import activity.侧边瞳眼_挂绳
import activity.彩底磨砂挂绳彩绘Aadd
import activity.手机壳2个装套图
from task.feishu_notify import send_feishu_notify
import task
import os
import time
#配置索引目录

folderIds = {
    "彩底磨砂挂绳彩绘A+": activity.彩底磨砂挂绳彩绘Aadd,
    "侧边瞳眼_挂绳": activity.侧边瞳眼_挂绳,
    "彩底_挂绳单个装": activity.彩底_挂绳单个装,
    "手机壳2个装套图": activity.手机壳2个装套图,
}

folder_path = r"\\192.168.1.188\图片集散\7月-自动化需求"
# folder_path = r"C:\Users\<USER>\Desktop\test_PS自动化"
print("脚本启动")
print("合法文件夹命名示例：20240701-百事队-任务模版-彩底磨砂挂绳彩绘A+-任务状态-待处理")
print("当前支持的任务模版：" + ", ".join(folderIds.keys()))

while True:
    print("开始新一轮检测...")
    found_valid_folder = False
    for subfolder in os.listdir(folder_path):
        subfolder_path = os.path.join(folder_path, subfolder)
        if os.path.isdir(subfolder_path):
            print(f"Subfolder: {subfolder_path}")
            if "任务模版" in subfolder and "任务状态" in subfolder:
                parts = subfolder.split("-")
                task_template_index = parts.index("任务模版") + 1
                task_status_index = parts.index("任务状态") + 1
                task_template = parts[task_template_index]
                task_status = parts[task_status_index]
                print(f"任务模版: {task_template},任务状态: {task_status}")
                Realy_task_template = folderIds.get(task_template, None)
                if Realy_task_template != None and task_status != None:
                    found_valid_folder = True
                    if task_status == "已完成":
                        print("任务状态完成，跳过")
                        continue
                    # Scan for .xlsx files in the subfolder
                    xlsx_files = [f for f in os.listdir(subfolder_path) if f.endswith('.xlsx') and "~$" not in f]
                    if not xlsx_files:
                        print(f"No .xlsx files found in {subfolder_path}.")
                        print("不存在模版文件，跳过")
                        # 发送飞书通知
                        send_feishu_notify("存在缺失文件", f"文件路径: {subfolder_path}")
                        continue
                    else:
                        print(f"Found .xlsx files: {xlsx_files}")
                    temp_task_file = os.path.join(subfolder_path, xlsx_files[0])
                    print("任务模版:",temp_task_file)
                    Realy_task_template.run(subfolder_path,temp_task_file)
                    # Update the task status to "已完成"
                    new_subfolder_name = subfolder.replace("任务状态-待处理", "任务状态-已完成")
                    new_subfolder_path = os.path.join(folder_path, new_subfolder_name)
                    os.rename(subfolder_path, new_subfolder_path)
                    print(f"任务状态已更新为已完成: {new_subfolder_path}")
                else:
                    print(f"文件夹【{subfolder}】的任务模版或任务状态不合法，已跳过。")
    if not found_valid_folder:
        print("未发现任何符合标准的任务文件夹，请检查文件夹命名格式和任务模版名称！")
    print("本轮检测结束，等待3小时后再次检测...\n")
    time.sleep(10800)  # 3小时
