# -*- coding:UTF-8 -*-
import json
import traceback

from .config import *
from .db import RedisClient
from .login.amazon.cookies import AmazonCookies
from .login.lingxing.cookies import LingXingCookies
from .login.sprite.cookies import SpriteExpansionCookies, SpriteCookies
from DrissionPage import WebPage, ChromiumOptions, SessionOptions, SessionPage


class CookiesGenerator(object):
    def __init__(self, website='default'):
        """
        父类, 初始化一些对象
        :param website: 名称
        :param browser: 浏览器, 若不使用浏览器则可设置为 None
        """
        self.website = website
        self.cookies_db = RedisClient('cookies', self.website)
        self.accounts_db = RedisClient('accounts', self.website)
        # self.init_browser()

    def __del__(self):
        self.close()

    def init_browser(self):
        """
        通过browser参数初始化全局浏览器供模拟登录使用
        :return:
        """
        if BROWSER_TYPE == 'S':
            self.browser = SessionPage()
        elif BROWSER_TYPE == 'Chrome':
            self.browser = WebPage()

    def new_cookies(self, username, password):
        """
        新生成Cookies，子类需要重写
        :param username: 用户名
        :param password: 密码
        :return:
        """
        raise NotImplementedError

    def run(self):
        """
        运行, 得到所有账户, 然后顺次模拟登录
        :return:
        """
        accounts_usernames = self.accounts_db.usernames()
        cookies_usernames = self.cookies_db.usernames()

        if not accounts_usernames:
            print(f'{self.website}未添加账号！')
            return

        for username in accounts_usernames:
            if not username in cookies_usernames:
                password = self.accounts_db.get(username)
                print('正在生成Cookies', '账号', username, '密码', password)
                result = self.new_cookies(username, password)
                # 成功获取
                if result.get('status') == 1:
                    content = result.get('content')
                    print('成功获取到Cookies', content)
                    if isinstance(content, (dict, list)):
                        cookies_str = json.dumps(content)
                    elif isinstance(content, str):
                        cookies_str = content
                    else:
                        cookies_str = ''
                    if self.cookies_db.set(username, cookies_str):
                        print('成功保存Cookies')
                # 密码错误，移除账号
                elif result.get('status') == 2:
                    print(result.get('content'))
                    if self.accounts_db.delete(username):
                        print('成功删除账号')
                else:
                    print(result.get('content'))
        else:
            print('所有账号都已经成功获取Cookies')

    def close(self):
        """
        关闭
        :return:
        """
        try:
            # print('关闭浏览器')
            self.browser.close()
            del self.browser
        except:
            # print('浏览器未打开')
            pass


class AmazonCookiesGenerator(CookiesGenerator):
    def __init__(self, website='amazon'):
        """
        初始化操作
        :param website: 站点名称
        :param browser: 使用的浏览器
        """
        CookiesGenerator.__init__(self, website)
        self.website = website

    def new_cookies(self, username, password):
        """
        生成Cookies
        :param username: 用户名
        :param password: 密码
        :return: 用户名和Cookies
        """
        try:
            cookies = AmazonCookies(username).main()
        except:
            traceback.print_exc()
            cookies = {}
        return cookies


class AmazonUSCookiesGenerator(CookiesGenerator):
    def __init__(self, website='amazon'):
        """
        初始化操作
        :param website: 站点名称
        :param browser: 使用的浏览器
        """
        CookiesGenerator.__init__(self, website)
        self.website = website

    def new_cookies(self, username, password):
        """
        生成Cookies
        :param username: 用户名
        :param password: 密码
        :return: 用户名和Cookies
        """
        try:
            cookies = AmazonCookies(9555).main('us')
        except:
            traceback.print_exc()
            cookies = {}
        return cookies


class LingXingCookiesGenerator(CookiesGenerator):
    def __init__(self, website='lingxing'):
        """
        初始化操作
        :param website: 站点名称
        :param browser: 使用的浏览器
        """
        CookiesGenerator.__init__(self, website)
        self.website = website

    def new_cookies(self, username, password):
        """
        生成Cookies
        :param username: 用户名
        :param password: 密码
        :return: 用户名和Cookies
        """
        try:
            cookies = LingXingCookies(username, password).main()
        except:
            traceback.print_exc()
            cookies = {}
        return cookies


class LingXingADCookiesGenerator(CookiesGenerator):
    def __init__(self, website='lingxingAD'):
        """
        初始化操作
        :param website: 站点名称
        :param browser: 使用的浏览器
        """
        CookiesGenerator.__init__(self, website)
        self.website = website

    def new_cookies(self, username, password):
        """
        生成Cookies
        :param username: 用户名
        :param password: 密码
        :return: 用户名和Cookies
        """
        try:
            cookies = LingXingCookies(username, password).main_ad()
        except:
            traceback.print_exc()
            cookies = {}
        return cookies


class SpriteCookiesGenerator(CookiesGenerator):
    def __init__(self, website='sprite'):
        """
        初始化操作
        :param website: 站点名称
        :param browser: 使用的浏览器
        """
        CookiesGenerator.__init__(self, website)
        self.website = website

    def new_cookies(self, username, password):
        """
        生成Cookies
        :param username: 用户名
        :param password: 密码
        :return: 用户名和Cookies
        """
        try:
            cookies = SpriteCookies(username, password).main()
        except:
            traceback.print_exc()
            cookies = {}
        return cookies


class SpriteExpansionCookiesGenerator(CookiesGenerator):
    def __init__(self, website='spriteEp'):
        """
        初始化操作
        :param website: 站点名称
        :param browser: 使用的浏览器
        """
        CookiesGenerator.__init__(self, website)
        self.website = website

    def new_cookies(self, username, password):
        """
        生成Cookies
        :param username: 用户名
        :param password: 密码
        :return: 用户名和Cookies
        """
        try:
            cookies = SpriteExpansionCookies(username, password).main()
        except:
            traceback.print_exc()
            cookies = {}
        return cookies


if __name__ == '__main__':
    # generator = SpriteExpansionCookiesGenerator()
    generator = LingXingADCookiesGenerator()
    generator.run()
