import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    name_of_all_layers = []
    if args is None:
        photoshop_instance = ""
        only_text_layer = False
    else:
        photoshop_instance = args.get("photoshop_instance", "")
        only_text_layer = args.get("only_text_layer", False)
    try:
        name_of_all_layers = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="get_names_of_all_layers", params={
            "photoshop_instance": photoshop_instance,
            "only_text_layer": only_text_layer,
        }, _block=("获取所有图层名", 1, "调用模块"))
    finally:
        args["name_of_all_layers"] = name_of_all_layers
