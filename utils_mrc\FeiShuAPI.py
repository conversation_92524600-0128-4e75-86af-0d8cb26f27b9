import json
import os
import requests
import inspect
from datetime import datetime
from hashlib import md5
from socket import gethostname


class FeiShuAPI:
    def __init__(self, app_id=None, app_secret=None, chat_id=None):
        self.app_id = app_id or "cli_a5c20a8a1ffed00e"
        self.app_secret = app_secret or "k4YiWLDNInXvgvpu3J2fFgblhMUvVgwL"
        self.chat_id = chat_id or "oc_84dc57bc82b6aa153f6c11769e35876b"
        self.app_table_token = 'EKR7bRB24aZIKFszqO5cn3mCn9d'  # 多维表格ID
        self.session = requests.Session()
        self.token = ''
        self.session.headers.update({
            "Content-Type": "application/json; charset=utf-8",
            "Accept": "application/json",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
        })
        self.get_token()

    def get_token(self):
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        data = {
            "app_id": self.app_id,
            "app_secret": self.app_secret,
        }
        response = self.session.post(url, data=json.dumps(data))
        token = response.json().get("tenant_access_token", "")
        self.token = token
        self.session.headers.update({
            "Authorization": f"Bearer {token}"
        })
        return token

    def send(self, except_source=None, content=None, except_info=None, except_time=None, template_id=None, title=None):
        try:
            # 获取当前帧（即调用some_method()的帧）的上一级帧
            frame = inspect.currentframe().f_back
            # 从帧中获取文件名
            file_name = inspect.getframeinfo(frame).filename
            # print(f"调用方法的文件名: {os.path.basename(file_name)}")
            except_source = except_source or os.path.basename(file_name)
            content = content or "程序异常"
            except_info = except_info or content
            except_time = except_time or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            title = title or "程序异常通知"

            template_variable = {
                "except_time": except_time,
                "except_source": except_source,
                "content": content,
                "except_info": except_info,
                "title": title,
            }
            unique = template_variable.copy()
            del unique["except_time"]
            uuid = md5(json.dumps(unique).encode()).hexdigest()

            url = "https://open.feishu.cn/open-apis/im/v1/messages"
            params = {"receive_id_type": "chat_id"}
            req = {
                "receive_id": self.chat_id,
                "msg_type": "interactive",
                "content": json.dumps({
                    "data": {
                        "template_id": template_id or "AAq07E3c0HUOn",
                        "template_variable": template_variable
                    },
                    "type": "template",
                }),
                "uuid": uuid,
            }
            payload = json.dumps(req)
            headers = {
                'Authorization': f'Bearer {self.get_token()}',
                'Content-Type': 'application/json; charset=utf-8'
            }
            response = self.session.post(url, params=params, headers=headers, data=payload)
            # print(response.json())
        except Exception as e:
            print(e)

    def send_task_stats(self):
        try:
            currentTime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            taskStats = [
                {
                    "table_name": "task_amazon_rule",
                    "abnormal_count": {
                        "text": 1,
                        "color": "red"
                    },
                    "pending_count": {
                        "text": 0,
                        "color": "green"
                    },
                    "executing_count": {
                        "text": 0,
                        "color": "green"
                    }
                },
                {
                    "table_name": "task_asins_loc",
                    "abnormal_count": {
                        "text": 0,
                        "color": "green"
                    },
                    "pending_count": {
                        "text": 0,
                        "color": "green"
                    },
                    "executing_count": {
                        "text": 1,
                        "color": "red"
                    }
                },
                {
                    "table_name": "task_goods",
                    "abnormal_count": {
                        "text": 0,
                        "color": "green"
                    },
                    "pending_count": {
                        "text": 10,
                        "color": "red"
                    },
                    "executing_count": {
                        "text": 2,
                        "color": "red"
                    }
                },
                {
                    "table_name": "task_lingxing",
                    "abnormal_count": {
                        "text": 3,
                        "color": "red"
                    },
                    "pending_count": {
                        "text": 0,
                        "color": "green"
                    },
                    "executing_count": {
                        "text": 0,
                        "color": "green"
                    }
                }
            ]
            tableNum = len(taskStats)
            hoursBeforeExecution = 9
            hoursDuringExecution = 3

            template_variable = {
                "currentTime": currentTime,
                "tableNum": tableNum,
                "hoursBeforeExecution": hoursBeforeExecution,
                "hoursDuringExecution": hoursDuringExecution,
                "taskStats": taskStats,
            }

            data = {
                "msg_type": "interactive",
                "card": json.dumps({
                    "data": {
                        "template_id": "AAq0Dq8MVuI8w",
                        "template_variable": template_variable
                    },
                    "type": "template"
                })
            }
            url = "https://open.feishu.cn/open-apis/bot/v2/hook/5a8411c1-774f-46fe-82dd-7c196742255e"
            data = json.dumps(data)
            response = self.session.post(url, data=data)
            '''
            url = "https://open.feishu.cn/open-apis/im/v1/messages"
            params = {"receive_id_type": "chat_id"}
            req = {
                "receive_id": self.chat_id,
                "msg_type": "interactive",
                "content": json.dumps({
                    "data": {
                        "template_id": "AAq0Dq8MVuI8w",
                        "template_variable": template_variable
                    },
                    "type": "template"
                })
            }
            payload = json.dumps(req)
            headers = {
                'Authorization': f'Bearer {self.get_token()}',
                'Content-Type': 'application/json; charset=utf-8'
            }
            response = self.session.post(url, params=params, headers=headers, data=payload)
            '''
            # print(response.json())
        except Exception as e:
            print(e)

    def req_api(self, url, **kwargs):
        response = self.session.post(url, **kwargs)
        if 'Invalid access token for authorization' in response.text:
            self.get_token()
            return self.req_api(url, **kwargs)
        return response.json()

    def drop_table(self, table_ids):
        # url = f'https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app_table_token}/tables/{table_id}'
        result = {}
        if table_ids:
            url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app_table_token}/tables/batch_delete"
            json_data = {
                "table_ids": table_ids
            }
            result = self.session.post(url, json=json_data).json()
            if result.get('code') == 0:
                print(f'{len(table_ids)} 张表已删除')
                return result

        return result

    def get_table_ids(self):
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app_table_token}/tables?page_size=100"
        ids = []
        result = self.session.get(url).json()
        if result.get('code') == 0:
            ids = [item.get("table_id") for item in result["data"]["items"] if item["name"] != "标识数据表(请勿删除)"]
            # ids = [item.get("table_id") for item in result["data"]["items"] if item["revision"] != 0]
            print(f'{len(ids)} 张表已获取(去除默认表)')
        return ids

    def drop_all_table(self, app_table_token=None):
        if app_table_token:
            self.app_table_token = app_table_token
        return self.drop_table(self.get_table_ids())


fsmsg = FeiShuAPI() if gethostname() != 'MRC' else None

if __name__ == '__main__':
    # fsmsg.send(
    #         except_source='测试应用',
    #         content='异常通知测试',
    #         except_info='异常信息测试',
    # )
    fsmsg.send(title='测试')
