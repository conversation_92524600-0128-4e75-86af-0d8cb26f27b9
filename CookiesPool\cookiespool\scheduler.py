# -*- coding:UTF-8 -*-
import time
from multiprocessing import Process
from .api import app
from .config import *
from .generator import *
from .tester import *
import sys


class Scheduler(object):
    @staticmethod
    def valid_cookie(cycle=CYCLE):
        while True:
            print('\n-- Cookies检测进程开始运行')
            try:
                for website, cls in TESTER_MAP.items():
                    tester = eval(cls + '(website="' + website + '")')
                    tester.run()
                    print(f'<{website}> Cookies检测完成\n')
                    del tester
            except Exception as e:
                print(e.args)
            print(f'等待 {cycle} 秒进行下一次检测')
            while cycle > 0:
                sys.stdout.write(".")
                cycle -= 1
                time.sleep(1)
            # time.sleep(cycle)

    @staticmethod
    def generate_cookie(cycle=CYCLE):
        while True:
            print('\n-- Cookies生成进程开始运行')
            try:
                for website, cls in GENERATOR_MAP.items():
                    generator = eval(cls + '(website="' + website + '")')
                    generator.run()
                    print(f'<{website}> Cookies生成完成\n')
                    generator.close()
            except Exception as e:
                print(e.args)
            print(f'等待 {cycle} 秒进行下一次生成')
            time.sleep(cycle)

    @staticmethod
    def api():
        print('\n-- API接口开始运行')
        app.run(host=API_HOST, port=API_PORT)

    @staticmethod
    def run():
        if API_PROCESS:
            api_process = Process(target=Scheduler.api)
            api_process.start()

        if GENERATOR_PROCESS:
            generate_process = Process(target=Scheduler.generate_cookie)
            generate_process.start()

        if VALID_PROCESS:
            valid_process = Process(target=Scheduler.valid_cookie)
            valid_process.start()


if __name__ == '__main__':
    s = Scheduler()
    # s.generate_cookie()
    s.run()
