{"types": [], "blocks": [{"name": "xbot_extensions.activity_photoshop.main", "statement": "process.invoke_activity", "title": "Photoshop 扩展", "keywords": "", "description": "提供包括打开 PSD 文件、替换图层内容、导出为图片、显示和隐藏图层等等 Photoshop 功能。在 21.0.2 版本上测试通过。", "comment": "提供包括打开 PSD 文件、替换图层内容、导出为图片、显示和隐藏图层等等 Photoshop 功能。在 21.0.2 版本上测试通过。", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/images/1e/80/1e80a971e586a0883fae751a29d1d03c.png", "function": "xbot_extensions.activity_photoshop.main", "helpUrl": null, "extension": "Photoshop 扩展", "hidden": true, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.ps", "statement": "process.invoke_activity", "title": "ps", "keywords": "", "description": null, "comment": null, "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.ps", "helpUrl": null, "extension": "Photoshop 扩展", "hidden": true, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.pil_api", "statement": "process.invoke_activity", "title": "pil_api", "keywords": "", "description": null, "comment": null, "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.pil_api", "helpUrl": null, "extension": "Photoshop 扩展", "hidden": true, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.ps_back", "statement": "process.invoke_activity", "title": "module2", "keywords": "", "description": null, "comment": null, "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.ps_back", "helpUrl": null, "extension": "Photoshop 扩展", "hidden": true, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.打开 PSD 文件", "statement": "process.invoke_activity", "title": "打开 PSD 文件", "keywords": "", "description": "打开 PSD 文件", "comment": "打开路径 %ps_file% 指向的 PSD 文件，保存 Photoshop 对象到 %photoshop_instance%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/02%E5%8A%9E%E5%85%AC/icons8-art_prices.png", "function": "xbot_extensions.activity_photoshop.process1", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/打开psd文件.html", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_file", "label": "PSD 文件路径", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null, "dialog": {"type": "OpenFile", "filter": "所有文件|*.*", "defaultFileName": null}}, "category": "general"}], "outputs": [{"id": "photoshop_instance", "label": "Photoshop 对象", "name": "photoshop_instance", "type": "str"}]}, {"name": "xbot_extensions.activity_photoshop.显示或隐藏图层", "statement": "process.invoke_activity", "title": "显示/隐藏图层", "keywords": "", "description": "在 PSD 文件中显示或隐藏一个图层", "comment": "在打开的 Photoshop 对象 %photoshop_instance% 中 %is_visible% 图层 %name_of_layer%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/04%E7%95%8C%E9%9D%A2/hide.png", "function": "xbot_extensions.activity_photoshop.process2", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/显示（隐藏）图层.html", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "photoshop_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "name_of_layer", "label": "图层名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "is_visible", "label": "设置为", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "select", "useVariableOptions": false, "options": [{"display": "隐藏", "value": "隐藏", "Unicode": null}, {"display": "显示", "value": "显示", "Unicode": null}]}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.激活图层", "statement": "process.invoke_activity", "title": "激活图层", "keywords": "", "description": "激活一个 PSD 文件的图层", "comment": "在打开的 Photoshop 对象 %photoshop_instance% 中激活图层 %name_of_layer%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/01%E9%80%9A%E7%94%A8/aspect_ratio.png", "function": "xbot_extensions.activity_photoshop.process3", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/激活图层.html", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "photoshop_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "name_of_layer", "label": "图层名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.修改图层文字", "statement": "process.invoke_activity", "title": "修改图层文字", "keywords": "", "description": "在 PSD 文件中修改文字图层的文字", "comment": "在打开的 Photoshop 对象 %photoshop_instance% 中将图层 %name_of_layer% 内容修改为 %text_to_be_set%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/02%E5%8A%9E%E5%85%AC/icons8-answers.png", "function": "xbot_extensions.activity_photoshop.process4", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/修改图层文字.html", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "photoshop_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "name_of_layer", "label": "图层名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "text_to_be_set", "label": "新文字内容", "required": false, "tips": "如果需要修改换行文字，点亮 Python 图标后输入内容，例如 \"90ml\\r\\n8瓶大\"，需要带半角引号，\\r\\n 表示一个换行", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.替换图层图片", "statement": "process.invoke_activity", "title": "替换图层图片", "keywords": "", "description": "在 PSD 文件中将图片图层的内容替换为另一张图片", "comment": "在打开的 Photoshop 对象 %photoshop_instance% 中将图层 %name_of_layer% 的内容替换为图片 %path_to_image_to_replace%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/01%E9%80%9A%E7%94%A8/static_views.png", "function": "xbot_extensions.activity_photoshop.process5", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/替换图层图片.html", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "photoshop_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "name_of_layer", "label": "图层名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "path_to_image_to_replace", "label": "图片路径", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "要替换上去的图片文件路径，支持常用图片格式", "dialog": {"type": "OpenFile", "filter": "所有文件|*.*", "defaultFileName": null}}, "category": "general"}, {"name": "keep_original_size_of_image_file", "label": "保留该图片原始大小", "required": false, "tips": "如果勾选，则调整该图层大小为图片大小；若不勾选，则保留\r\n原始图层大小，调整图片为图层大小插入", "type": "bool", "default": "13:True", "editor": {"kind": "checkbox", "label": "保留该图片原始大小"}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.导出为图片", "statement": "process.invoke_activity", "title": "导出为图片", "keywords": "", "description": "将打开的 PSD 文件导出为图片", "comment": "将 %photoshop_instance% 导出为图片，保存在 %save_path%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/01%E9%80%9A%E7%94%A8/open_view.png", "function": "xbot_extensions.activity_photoshop.process6", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/导出为图片.html", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "photoshop_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "save_path", "label": "保存路径", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "保存路径需以 png、jpeg、jpg、bmp 结尾"}, "category": "general"}, {"name": "quality", "label": "导出质量", "required": false, "tips": "导出图片的质量, 仅支持jpg, jepg 格式", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "导出图片的质量, 1-12, 如, 1, 5; 仅支持jpg, jepg 格式, "}, "category": "advanced"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.关闭 PSD 文件", "statement": "process.invoke_activity", "title": "关闭 PSD 文件", "keywords": "", "description": "关闭 PSD 文件", "comment": "关闭 Photoshop 对象 %photoshop_instance% 对应的 PSD 文件", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/01%E9%80%9A%E7%94%A8/delete.png", "function": "xbot_extensions.activity_photoshop.process7", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/关闭psd文件.html", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "photoshop_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "save_change", "label": "保存更改", "required": false, "tips": "", "type": "bool", "default": "13:<PERSON><PERSON><PERSON>", "editor": {"kind": "checkbox", "label": "保存更改"}, "category": "general"}, {"name": "quit", "label": "", "required": false, "tips": "", "type": "bool", "default": "13:True", "editor": {"kind": "checkbox", "label": "关闭PS"}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.另存为 PSD 文件", "statement": "process.invoke_activity", "title": "另存为 PSD 文件", "keywords": "", "description": "将 PSD 文件另存为一份", "comment": "将 Photoshop 对象 %photoshop_instance% 对应的 PSD 文件另存为 %save_path%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/04%E7%95%8C%E9%9D%A2/save_as.png", "function": "xbot_extensions.activity_photoshop.process8", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/另存为psd文件.html", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "photoshop_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "save_path", "label": "保存路径", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "C:\\Users\\<USER>\\Desktop\\example.psd"}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.获取所有图层名", "statement": "process.invoke_activity", "title": "获取所有图层名", "keywords": "", "description": "获取 PDF 文件中所有图层名", "comment": "获取 Photoshop 对象 %photoshop_instance% 中所有图层的名字，保存在 %name_of_all_layers% 中", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/01%E9%80%9A%E7%94%A8/questions.png", "function": "xbot_extensions.activity_photoshop.process9", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/获取所有图层名.html", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "photoshop_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "only_text_layer", "label": "", "required": false, "tips": "", "type": "bool", "default": "13:<PERSON><PERSON><PERSON>", "editor": {"kind": "checkbox", "label": "仅文字图层"}, "category": "general"}], "outputs": [{"id": "name_of_all_layers", "label": "所有图层名", "name": "name_of_all_layers", "type": "list"}]}, {"name": "xbot_extensions.activity_photoshop.获取图层文字", "statement": "process.invoke_activity", "title": "获取图层文字", "keywords": "", "description": "该指令实现获取文字图层的文本", "comment": "获取%ps_instance%的%layer_name%图层的文字内容", "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.process10", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/获取图层文字.html?", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": ""}, "category": "general"}, {"name": "only_visible", "label": "仅可见图层", "required": false, "tips": "", "type": "bool", "default": "13:True", "editor": {"kind": "checkbox", "label": "仅可见图层"}, "category": "general"}], "outputs": [{"id": "layer_text", "label": "图层文字", "name": "layer_text", "type": "str"}]}, {"name": "xbot_extensions.activity_photoshop.设置文字图层字体", "statement": "process.invoke_activity", "title": "设置文字图层字体", "keywords": "", "description": "该指令设置文字图层的字体样式和大小", "comment": "设置%ps_instance%的%layer_name%图层的字体大小为%font_size%, 字体为%font%", "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.process11", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/设置文字图层字体.html?", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "font", "label": "字体名称", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "font_size", "label": "字体大小", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "hex_value", "label": "字体颜色", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "only_visible", "label": "仅可见图层", "required": false, "tips": "", "type": "bool", "default": "13:True", "editor": {"kind": "checkbox", "label": "仅可见图层"}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.获取图层所属组", "statement": "process.invoke_activity", "title": "获取图层所属组", "keywords": "", "description": "该指令实现获取指定图层所属组名", "comment": "获取%ps_instance%的%layer_name%图层的所属组名", "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.process12", "helpUrl": "yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/获取图层所属组.html?", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "PS 对象", "required": false, "tips": "", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}], "outputs": [{"id": "parent_group", "label": "所属组名", "name": "parent_group", "type": "str"}, {"id": "parent_groups", "label": "全部所属组名", "name": "parent_groups", "type": "list"}]}, {"name": "xbot_extensions.activity_photoshop.test_invoke_ps", "statement": "process.invoke_activity", "title": "module1", "keywords": "", "description": null, "comment": null, "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.test_invoke_ps", "helpUrl": null, "extension": "Photoshop 扩展", "hidden": true, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.重命名图层", "statement": "process.invoke_activity", "title": "重命名图层", "keywords": "", "description": "该指令实现了重命名 PS 图层名称的功能", "comment": "将%ps_instance%对象的%layer_name%图层重命名为%new_layer_name%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/05%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86/edit_property.png", "function": "xbot_extensions.activity_photoshop.process13", "helpUrl": "/yddoc/rpa/712598633364492288", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "Photoshop 对象", "required": false, "tips": "待处理的PS对象实例", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "原图层名称", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "输入待重命名图层的名称"}, "category": "general"}, {"name": "new_layer_name", "label": "新图层名称", "required": false, "tips": "新图层的名称", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "输入新图层的名称"}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.内容识别填充", "statement": "process.invoke_activity", "title": "内容识别填充", "keywords": "", "description": "该指令实现了在指令路径区域内进行内容识别填充", "comment": "在%ps_instance%对象中%layer_name%上的%path%路径选区内进行内容识别填充", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/05%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86/grid_2.png", "function": "xbot_extensions.activity_photoshop.process14", "helpUrl": "/yddoc/rpa/712598704502784000", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "Photoshop 对象", "required": false, "tips": "待处理的PS对象实例", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "待操作的图层名称", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "输入操作的图层名称"}, "category": "general"}, {"name": "path", "label": "路径坐标", "required": false, "tips": "待选中的路径坐标", "type": "list", "default": "13:[]", "editor": {"kind": "textbox", "placeholder": "输入路径选区的坐标, 如 [[200, 200],[200,300], [300,240], [200, 240]]"}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.修改颜色填充图层", "statement": "process.invoke_activity", "title": "修改颜色填充图层", "keywords": "", "description": "该指令实现了在 PS 中修改颜色填充图层的功能", "comment": "在%ps_instance%对象中的%layer_name%图层中修改颜色填充图层的颜色为%hex_value%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/01%E9%80%9A%E7%94%A8/picture.png", "function": "xbot_extensions.activity_photoshop.process15", "helpUrl": "/yddoc/rpa/712598794688708608", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "Photoshop 对象", "required": false, "tips": "待处理的PS对象实例", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "待操作的图层名称", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "请输入待操作图层的名称"}, "category": "general"}, {"name": "hex_value", "label": "RGB 值", "required": false, "tips": "图层填充RGB颜色HEX字符串", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "输入RGB颜色的HEX值, 如, #FFEEFF, FFEEFF"}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.设置形状图层描边", "statement": "process.invoke_activity", "title": "设置形状图层描边", "keywords": "", "description": "该指令实现了 PS 设置形态图层描边颜色的功能", "comment": "在%ps_instance%中的%layer_name%中设置形状图层描边颜色为%hex_value%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/05%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86/cd.png", "function": "xbot_extensions.activity_photoshop.process16", "helpUrl": "/yddoc/rpa/712598877912088576", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "Photoshop 对象", "required": false, "tips": "待处理的PS对象实例", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "待操作的图层名称", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "输入待处理图层的名称"}, "category": "general"}, {"name": "hex_value", "label": "RGB 值", "required": false, "tips": "图层描边RGB颜色HEX字符串", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "输入RGB颜色的HEX值, 如, #FFEEFF, FFEEFF"}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.invoke_module", "statement": "process.invoke_activity", "title": "module1", "keywords": "", "description": null, "comment": null, "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.invoke_module", "helpUrl": null, "extension": "Photoshop 扩展", "hidden": true, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.导出图层为PNG", "statement": "process.invoke_activity", "title": "导出图层为PNG", "keywords": "", "description": "该指令将指定图层或图层组导出为 PNG 图片", "comment": "导图%ps_instance%的%name%%mode%为PNG图片, 并保存在%save_path%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/05%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86/micro_sd.png", "function": "xbot_extensions.activity_photoshop.process17", "helpUrl": "/yddoc/rpa/712598921859035136", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "mode", "label": "导出方式", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "select", "useVariableOptions": false, "options": [{"display": "图层名", "value": "artlayer", "Unicode": null}, {"display": "组名", "value": "layerset", "Unicode": null}]}, "category": "general"}, {"name": "name", "label": "图层或组名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "save_path", "label": "保存路径", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.添加图片图层", "statement": "process.invoke_activity", "title": "添加图片图层", "keywords": "", "description": "该指令实现了在 Photoshop 中添加文字图层的能力", "comment": "在%ps_instance%上将%file_path%添加为图片图层", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/01%E9%80%9A%E7%94%A8/news.png", "function": "xbot_extensions.activity_photoshop.process18", "helpUrl": "/yddoc/rpa/712598963015815168", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "新建图层的名称, 为空将系统生成"}, "category": "general"}, {"name": "file_path", "label": "图片路径", "required": true, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "请选择用于创建图层的图片", "dialog": {"type": "OpenFile", "filter": "所有文件|*.*", "defaultFileName": null}}, "category": "general"}], "outputs": [{"id": "new_layer_name", "label": "图层名称", "name": "new_layer_name", "type": "str"}]}, {"name": "xbot_extensions.activity_photoshop.删除图层", "statement": "process.invoke_activity", "title": "删除图层", "keywords": "", "description": "改指令实现了删除 Photoshop 图层的功能", "comment": "删除%ps_instance%的%layer_name%图层", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/01%E9%80%9A%E7%94%A8/delete.png", "function": "xbot_extensions.activity_photoshop.process19", "helpUrl": "/yddoc/rpa/712599007972712448", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "Photoshop 对象", "required": false, "tips": "", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "待删除的图层的名称"}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.test_invoke_pyd", "statement": "process.invoke_activity", "title": "module1", "keywords": "", "description": null, "comment": null, "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.test_invoke_pyd", "helpUrl": null, "extension": "Photoshop 扩展", "hidden": true, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.获取图层字体信息", "statement": "process.invoke_activity", "title": "获取图层字体信息", "keywords": "", "description": "该指令获取指定图层的字体信息", "comment": "获取%ps_instance%的%layer_name%图层的字体大小%font_size%, 字体名称%font_name%, 字体RGB HexValue %font_name%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/01%E9%80%9A%E7%94%A8/info_popup.png", "function": "xbot_extensions.activity_photoshop.process20", "helpUrl": "/yddoc/rpa/712599049102057472", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "PS 对象", "required": false, "tips": "待处理的ps_instance", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "待操作的图层名称", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "only_visible", "label": "", "required": false, "tips": "是否仅可见的图层", "type": "bool", "default": "13:<PERSON><PERSON><PERSON>", "editor": {"kind": "checkbox", "label": "仅可见图层"}, "category": "general"}], "outputs": [{"id": "font_name", "label": "字体名称", "tips": "字体的名称", "name": "font_name", "type": "str"}, {"id": "hex_value", "label": "字体RGB", "tips": "字体RGB的HexValue", "name": "hex_value", "type": "str"}, {"id": "font_size", "label": "字体大小", "tips": "字体的大小", "name": "font_size", "type": "int"}]}, {"name": "xbot_extensions.activity_photoshop.激活文档", "statement": "process.invoke_activity", "title": "激活文档", "keywords": "", "description": "该指令实现了将指定 ps_instance 设置为激活的文档", "comment": "将 %ps_instance% PS 对象设置成当前激活的文档", "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.process21", "helpUrl": "/yddoc/rpa/750524152562864128?", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "ps_instance", "required": false, "tips": "", "type": "any", "default": "13:None", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.替换图框", "statement": "process.invoke_activity", "title": "替换图框", "keywords": "", "description": "该指令实现在 Photoshop 替换图框中图片的功能", "comment": "将%ps_instance%对象的%layer_name%的图框内的图片替换为%image_path%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/04%E7%95%8C%E9%9D%A2/replace.png", "function": "xbot_extensions.activity_photoshop.process22", "helpUrl": "/yddoc/rpa/750524230149099520", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "PS 对象", "required": true, "tips": "待操作的 ps_instance", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "layer_name", "label": "图层名称", "required": false, "tips": "待操作的图层名称, 支持组1>组2>组3>图层1", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "待操作的图层名称, 支持组1>组2>组3>图层1"}, "category": "general"}, {"name": "image_path", "label": "图片路径", "required": false, "tips": "替换图片的路径", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "选择待替换的图片文件路径", "dialog": {"type": "OpenFile", "filter": "所有文件|*.*", "defaultFileName": null}}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.js_code", "statement": "process.invoke_activity", "title": "module1", "keywords": "", "description": null, "comment": null, "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.js_code", "helpUrl": null, "extension": "Photoshop 扩展", "hidden": true, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.export_artboard_as_png", "statement": "process.invoke_activity", "title": "画板切图", "keywords": "", "description": "该指令实现了将画板导出PNG格式至指定目录", "comment": "将%ps_instance%ps对象中的画板导出成PNG图片至%save_dir%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/05%E6%95%B0%E6%8D%AE%E5%A4%84%E7%90%86/export.png", "function": "xbot_extensions.activity_photoshop.export_artboard_as_png", "helpUrl": "/yddoc/rpa/750524289745813504", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "PS 对象", "required": false, "tips": "待处理的ps_instance", "type": "any", "default": "13:None", "editor": {"kind": "select", "useVariableOptions": true, "options": []}, "category": "general"}, {"name": "file_type", "label": "文件类型", "required": false, "tips": "保存文件的类型", "type": "str", "default": "10:", "editor": {"kind": "select", "useVariableOptions": false, "options": [{"display": "png", "value": "png", "Unicode": null}, {"display": "jpg", "value": "jpg", "Unicode": null}]}, "category": "general"}, {"name": "save_dir", "label": "保存目录", "required": false, "tips": "导出图片保存的目录", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": "选择保存的文件目录", "dialog": {"type": "SelectFolder", "filter": "所有文件|*.*", "defaultFileName": null}}, "category": "general"}], "outputs": [{"id": "files_path", "label": "导出图片路径", "tips": "保存文件的所有路径", "name": "files_path", "type": "list"}]}, {"name": "xbot_extensions.activity_photoshop.test_replace_image", "statement": "process.invoke_activity", "title": "module1", "keywords": "", "description": null, "comment": null, "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.test_replace_image", "helpUrl": null, "extension": "Photoshop 扩展", "hidden": true, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.replace_layer_image", "statement": "process.invoke_activity", "title": "替换图层图片(自动缩放)", "keywords": "", "description": "该指令实现了在PS中替换图层图片并自动缩放大小", "comment": "替换%ps_instance%文档中%layer_name%图层的图片为%image_path%", "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.replace_layer_image", "helpUrl": "/yddoc/rpa/770888169164795904", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "PS 对象", "required": false, "tips": "待处理的 ps_instance对象", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "layer_name", "label": "图层名称", "required": false, "tips": "待操作的图层名称", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "image_path", "label": "图片路径", "required": false, "tips": "替换图片的路径", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null, "dialog": {"type": "OpenFile", "filter": "所有文件|*.*", "defaultFileName": null}}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.set_text_format", "statement": "process.invoke_activity", "title": "设置文字图层格式", "keywords": "", "description": "该指令实现了设置文字图层的文字格式的功能", "comment": "设置%ps_instance%的%layer_name%图层的文字格式为%text_styles%", "icon": "BlockIcons/21-2.png", "function": "xbot_extensions.activity_photoshop.set_text_format", "helpUrl": "/yddoc/rpa/770888082639388672", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "PS 对象", "required": false, "tips": "", "type": "any", "default": "13:None", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "layer_name", "label": "图层名称", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "text_styles", "label": "文字格式", "required": false, "tips": "", "type": "dict", "default": "13:{}", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.edit_smart_object", "statement": "process.invoke_activity", "title": "编辑智能对象", "keywords": "", "description": "该指令实现了双击编辑智能对象图层的功能", "comment": "编辑%ps_instance%文档的%layer_name%智能对象图层", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/01%E9%80%9A%E7%94%A8/edit.png", "function": "xbot_extensions.activity_photoshop.edit_smart_object", "helpUrl": "/yddoc/rpa/770887980479209472", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [{"name": "ps_instance", "label": "PS 对象", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}, {"name": "layer_name", "label": "图层名", "required": false, "tips": "", "type": "str", "default": "10:", "editor": {"kind": "textbox", "placeholder": null}, "category": "general"}], "outputs": []}, {"name": "xbot_extensions.activity_photoshop.get_active_doc", "statement": "process.invoke_activity", "title": "获取激活的 PS 对象", "keywords": "", "description": "该指令实现了获取当前激活的PS文档对象", "comment": "获取当前激活的 Photoshop 文档对象保存至%ps_instance%", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/icons/activity-icons/04%E7%95%8C%E9%9D%A2/inactive_state.png", "function": "xbot_extensions.activity_photoshop.get_active_doc", "helpUrl": "/yddoc/rpa/770887805809700864", "extension": "Photoshop 扩展", "hidden": false, "canDebug": true, "isCondition": false, "isLoop": false, "isPseudo": false, "inputs": [], "outputs": [{"id": "ps_instance", "label": "PS 对象", "name": "ps_instance", "type": "str"}]}]}