import os
import sys
import time
import requests
import xlwings as xw
import pandas as pd
from tkinter import filedialog
import tkinter as tk


def download_image(url):
    file_name = r'amazon_screenshots\temp.png'
    response = requests.get(url, stream=True)
    if response.status_code == 200:
        with open(file_name, 'wb') as f:
            for chunk in response.iter_content(1024):
                f.write(chunk)
        return os.path.abspath(file_name)
    else:
        return None


def logging(text):
    time_stamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f'{time_stamp} | {text}')


def select_file_if_not_exist(file_path='', type='all'):
    """如果文件不存在，则弹出文件选择对话框让用户选择"""
    if not file_path or not os.path.exists(file_path):
        print('请选择要处理的文件')
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        if type == 'all':
            filetypes = []
        else:
            filetypes = [('Excel Files', '*.xlsx;*.xls;*.xlsm')]
        file_path = filedialog.askopenfilename(title=f'请选择数据表格:', filetypes=filetypes)
        if not file_path:  # 用户取消选择
            sys.exit('未选择表格，程序即将退出')
    return file_path


class ExcelProcessor:
    def __init__(self, workbook_path, sheet_name=None, visible=True):
        """
        初始化ExcelProcessor类，连接到指定的工作簿。

        :param workbook_path: str, Excel工作簿的完整路径。
        :param sheet_name: str, 要打开的工作表名称。
        :param visible: bool, 是否显示Excel程序。
        """
        self.app = xw.App(visible=visible, add_book=False)  # 程序可见，只打开不新建工作簿
        self.app.display_alerts = False  # 警告关闭
        # self.app.screen_updating = False  # 屏幕更新关闭
        self.workbook_path = workbook_path
        self.wb = self.app.books.open(workbook_path)
        # 如果没有指定sheet_name，使用活动工作表
        if sheet_name is None:
            self.sheet = self.wb.sheets.active
            self.sheet_name = self.sheet.name
        else:
            self.sheet = self.wb.sheets[sheet_name]

        self.sheet_template = None
        self.supported_sheet_names = ['Vorlage', 'Modèle', 'Modello', 'Plantilla', 'Template']
        # self.check_or_copy_column()

    def embed_images(self):
        if self.sheet.pictures:
            logging("已存在图片，跳过下载环节")
            return
        column_index = self.sheet.used_range.value[0].index('主图') + 1
        data_range = self.sheet.range((2, column_index), (self.sheet.used_range.last_cell.row, column_index))

        for cell in data_range:
            image_url = cell.value
            if image_url:
                try:
                    local_image_path = download_image(image_url)
                    if not local_image_path:
                        logging(f"无法下载图片：{image_url}")
                        continue
                    cell.api.RowHeight = 70  # 设置行高以适应图像
                    # cell.api.ColumnWidth = 20  # 设置列宽以适应图像
                    cell.api.Select()
                    # self.sheet.pictures.add(local_image_path, left=cell.left, top=cell.top, height=cell.height)
                    # self.sheet.pictures.add(local_image_path, anchor=cell, height=cell.height)
                    self.sheet.pictures.add(local_image_path, left=cell.left, top=cell.top + 5, height=cell.height - 10)
                except Exception as e:
                    # logging(e)
                    logging(f"从 URL 插入图片失败: {image_url}")

        self.wb.save()
        logging("图片插入完成")

    def read_excel(self, workbook_path=None, sheet_name=None, header=0):
        if not workbook_path:
            workbook_path = self.workbook_path
        if sheet_name is None:
            sheet_name = self.sheet.name

        return pd.read_excel(workbook_path, sheet_name=sheet_name, header=header)

    def read_active_data(self):
        """
        读取Excel表格中的数据。

        :param sheet_name: str, 工作表名称。如果不指定，默认为当前活动工作表。
        :param range_address: str, 单元格范围。如果不指定，默认读取活动单元格。
        :return: DataFrame 或 str, 包含所读取数据的Pandas DataFrame或单个单元格的值。
        """

        # 读取活动单元格的值
        data = self.sheet.used_range.value

        return data

    def read_active_data_todict(self):
        """
        读取Excel表格中的数据。

        :param sheet_name: str, 工作表名称。如果不指定，默认为当前活动工作表。
        :param range_address: str, 单元格范围。如果不指定，默认读取活动单元格。
        :return: DataFrame 或 str, 包含所读取数据的Pandas DataFrame或单个单元格的值。
        """
        # 读取活动单元格的值
        data = self.sheet.used_range.value

        # 检查数据是否为空或不满足预期结构
        if not data or not all(len(row) == len(data[0]) for row in data):
            return None

        # object_list = pd.DataFrame(data[1:], columns=data[0]).to_dict(orient='records')
        object_list = pd.DataFrame(data[1:], columns=data[0])

        return object_list

    def update_sheet_by_sheet_data(self):
        # 优化查找性能，首先将可用表单名称转换为集合
        sheet_names = set(self.wb.sheet_names)
        # 遍历支持的表单名称，查找匹配项
        for sheet in self.supported_sheet_names:
            if sheet in sheet_names:
                self.sheet_template = self.wb.sheets[sheet]
                break  # 找到匹配项后即停止循环
        if not self.sheet_template:
            logging(f"未找到支持的模板表格,请确认是否存在{self.supported_sheet_names}")
            return
        # 查找
        data_template = self.sheet_template.used_range.value
        columns_index = 2
        df_tl = pd.DataFrame(data_template[columns_index + 1:], columns=data_template[columns_index])
        df_dt = self.read_active_data_todict()
        df_dt = df_dt.dropna(how='all')  # 确保所有列都非空才保留该行，设置how='all'

        count = 0
        for index, row in df_dt.iterrows():
            if '最终文案' in row['Asin（销售必填）']:
                # print(index, row)
                df_tl.loc[count, 'item_name'] = row['item_name']
                df_tl.loc[count, 'product_description'] = row['product_description']
                df_tl.loc[count, 'bullet_point1'] = row['bullet_point1']
                df_tl.loc[count, 'bullet_point2'] = row['bullet_point2']
                df_tl.loc[count, 'bullet_point3'] = row['bullet_point3']
                df_tl.loc[count, 'bullet_point4'] = row['bullet_point4']
                df_tl.loc[count, 'bullet_point5'] = row['bullet_point5']
                # df_tl.loc[count, 'bullet_point6'] = row['bullet_point6']
                count += 1
        new_data = df_tl.to_numpy()
        self.sheet_template.range('A4').value = new_data
        self.wb.save()
        logging('已更新最终文案')

    def check_or_copy_column(self):
        headers = self.sheet.used_range.value[0]

        # 检查'bullet_point6'是否在表头中
        if 'bullet_point6' not in headers:
            logging('未检测到bullet_point6,正在添加...')
            # 获取'bullet_point5'列的索引
            bullet_point5_index = headers.index('bullet_point5') + 1
            # 复制'bullet_point5'整列（包括格式和数据）
            # 插入新列并保留左侧列的格式
            self.sheet.range((1, bullet_point5_index + 1)).api.EntireColumn.Insert()

            # 设置新列的第一格为'bullet_point6'
            self.sheet.range(f'{chr(bullet_point5_index + 65)}1').value = 'bullet_point6'

            # 保存更改
            self.wb.save()

    def update_data(self, data, range='A1'):
        """
        更新Excel表格中的数据。
        :param data: list, 二维列表，包含要更新的数据
        :param range: str, 单元格范围，默认为A1
        """
        self.sheet.range(range).value = data
        self.wb.save()

    def close(self):
        """
        关闭工作簿并退出应用程序。
        """
        self.wb.close()
        self.app.quit()


# 使用示例
if __name__ == "__main__":
    logging("开始执行")
    # 假设Excel文件路径为'example.xlsx'
    elp = ExcelProcessor(r'D:\Downloads\0505-上架模板\上架模板-UK - 副本.xlsx', sheet_name='上架文案')
    elp.embed_images()
    # 使用完毕后关闭工作簿
    elp.close()
