import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        ps_instance = None
        layer_name = ""
        new_layer_name = ""
    else:
        ps_instance = args.get("ps_instance", None)
        layer_name = args.get("layer_name", "")
        new_layer_name = args.get("new_layer_name", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="rename_layer_name", params={
            "ps_instance": ps_instance,
            "layer_name": layer_name,
            "new_layer_name": new_layer_name,
        }, _block=("重命名图层", 1, "调用模块"))
    finally:
        pass
