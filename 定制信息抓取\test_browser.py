from DrissionPage import Chromium, ChromiumOptions
import time

# 配置浏览器
co = ChromiumOptions().set_local_port(9222)
# 如果你有自定义浏览器路径，可以加上：
# co.set_browser_path("C:\\Program Files\\ESBrowser\\ESBrowser.exe")

# 启动浏览器并打开通途页面
page = Chromium(co).new_tab()
page.get("https://erp115.tongtool.com/dashboard/homepage/index.htm")

print("浏览器已启动并打开通途页面，请手动操作。5分钟后自动关闭。")
time.sleep(300)  # 300秒=5分钟，你可以改成更长

# 可选：关闭浏览器
# page.browser.quit()
