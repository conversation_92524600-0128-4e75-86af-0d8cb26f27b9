import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        ps_instance = None
        layer_name = ""
        image_path = ""
    else:
        ps_instance = args.get("ps_instance", None)
        layer_name = args.get("layer_name", "")
        image_path = args.get("image_path", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="invoke_module", package=__name__, function="replace_image_frame", params={
            "ps_instance": ps_instance,
            "layer_name": layer_name,
            "image_path": image_path,
        }, _block=("替换图框", 1, "调用模块"))
    finally:
        pass
