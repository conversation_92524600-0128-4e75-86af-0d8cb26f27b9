import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        photoshop_instance = ""
        name_of_layer = ""
        text_to_be_set = ""
    else:
        photoshop_instance = args.get("photoshop_instance", "")
        name_of_layer = args.get("name_of_layer", "")
        text_to_be_set = args.get("text_to_be_set", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="set_text_of_layer_by_name", params={
            "photoshop_instance": photoshop_instance,
            "name_of_layer": name_of_layer,
            "text_to_be_set": text_to_be_set,
        }, _block=("修改图层文字", 1, "调用模块"))
    finally:
        pass
