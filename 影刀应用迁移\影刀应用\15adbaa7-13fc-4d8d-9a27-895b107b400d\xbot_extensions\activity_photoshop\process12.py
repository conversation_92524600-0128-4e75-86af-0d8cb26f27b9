import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    parent_group = ""
    parent_groups = []
    if args is None:
        ps_instance = None
        layer_name = ""
    else:
        ps_instance = args.get("ps_instance", None)
        layer_name = args.get("layer_name", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="get_layer_parent_name", params={
            "ps_instance": ps_instance,
            "layer_name": layer_name,
        }, _block=("获取图层所属组", 1, "调用模块"))
        parent_group = invoke_result[0]
        parent_groups = invoke_result[1]
    finally:
        args["parent_group"] = parent_group
        args["parent_groups"] = parent_groups
