import os
import platform
import shutil
import time
import schedule
from datetime import datetime
import traceback
import uuid
import json
import requests
import subprocess
import random
import psutil
import re
from DrissionPage import Chromium, ChromiumOptions
from loguru import logger
import pymysql

from urllib.parse import urlparse

import pandas as pd

from loguru import logger


# # 添加文件处理器，将日志写入文件
logger.add(
    sink="logfile.log",  # 日志文件的名称
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",  # 定义日志格式
    rotation="500 MB",  # 当文件大小达到 500 MB 时，自动创建新文件
    compression="zip"  # 旧文件压缩为 zip 格式
)

# 用于标识任务是否正在执行
is_job_running = False

def write_dict_list_to_xlsx_pandas(browser_list, filename='output.xlsx'):
    if not browser_list:
        return
    # 将列表转换为 DataFrame
    df = pd.DataFrame(browser_list)
    # 将 DataFrame 写入 Excel 文件
    df.to_excel(filename, index=False)


# logger.remove()  # 移除默认的控制台处理器
# logger.add(
#     sink=lambda msg: print(msg, end=''),  # 定义日志输出到控制台
#     format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"  # 定义日志格式
# )


class SqlConnect():
    def __init__(self, host='localhost', user='root', password='your_password', database='your_database'):
        # 初始化数据库连接参数
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
        try:
            # 尝试连接到 MySQL 数据库
            self.connection = pymysql.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            print("成功连接到 MySQL 数据库")
        except pymysql.MySQLError as e:
            print(f"连接到 MySQL 数据库时出错: {e}")

    def close(self):
        # 关闭数据库连接
        if self.connection:
            self.connection.close()
            print("数据库连接已关闭")

    def read_data(self, query):
        """
        读取数据的方法
        :param query: 要执行的 SQL 查询语句
        :return: 查询结果，如果出现错误返回 None，正常结果以列表中包含字典的形式返回
        """
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(query)
                result = cursor.fetchall()
                return result
        except pymysql.MySQLError as e:
            print(f"读取数据时出错: {e}")
            return None

    def update_data(self, update_query):
        """
        更新数据的方法
        :param update_query: 要执行的 SQL 更新语句
        :return: 受影响的行数，如果出现错误返回 None
        """
        try:
            with self.connection.cursor() as cursor:
                rows_affected = cursor.execute(update_query)
                self.connection.commit()  # 提交事务
                return rows_affected
        except pymysql.MySQLError as e:
            print(f"更新数据时出错: {e}")
            self.connection.rollback()  # 出错时回滚事务
            return None


# 公共类
class Common():
    def __init__(self):
        pass

    def find_available_port(self):
        while True:
            port = random.randint(10000, 20000)
            # 检查端口是否被占用
            if not self.is_port_in_use(port):
                return port

    def is_port_in_use(self, port):
        for conn in psutil.net_connections():
            if conn.laddr.port == port:
                return True
        return False

    def find_dict_by_browser_name(self, dicts_list, target_name):
        """
        从字典列表中查找 browserName 符合指定字符串的字典
        :param dicts_list: 包含多个字典的列表
        :param target_name: 要查找的 browserName 的目标字符串
        :return: 找到的字典，如果未找到返回 None
        """
        for dictionary in dicts_list:
            if dictionary.get("browserName","") == target_name or dictionary.get("store_username") == target_name:
                return dictionary
        return False

    def get_domain_of_existing_page(self, url):
        """
        获取网址的域名
        :param url: 传入的网址
        :return: 返回的域名
        """
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        return domain

# 紫鸟封装类


class superBrowser():
    def __init__(self,socket_port):
        self.matching_requests = []
        self.socket_port = socket_port

    def kill_process(self, is_windows):
        """
        杀紫鸟客户端进程
        """
        logger.info("正在杀死所有紫鸟客户端进程......")
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                # 忽略大小写，判断进程名中是否包含 'superbrowser'
                if 'superbrowser' in proc.info['name']:
                    logger.info(f"终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    proc.terminate()  # 终止进程
                    proc.wait()  # 等待进程终止
                    logger.info(f"进程 {proc.info['pid']} 已终止")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                # 捕获进程已结束或没有权限的情况
                pass
    def kill_main_process(self, is_windows):
        """
        杀紫鸟客户端进程
        """
        logger.info("正在杀死所有紫鸟客户端进程......")
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                # 忽略大小写，判断进程名中是否包含 'superbrowser'
                if 'SuperBrowser' in proc.info['name']:
                    logger.info(f"终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    proc.terminate()  # 终止进程
                    proc.wait()  # 等待进程终止
                    logger.info(f"进程 {proc.info['pid']} 已终止")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                # 捕获进程已结束或没有权限的情况
                pass

    def start_browser(self, is_windows, is_mac, client_path, socket_port):
        """
        启动客户端
        :return:
        """
        try:
            if is_windows:
                cmd = [client_path, '--run_type=web_driver',
                       '--ipc_type=http', '--port=' + str(socket_port)]
            elif is_mac:
                cmd = ['open', '-a', client_path, '--args', '--run_type=web_driver', '--ipc_type=http',
                       '--port=' + str(socket_port)]
            else:
                exit()
            subprocess.Popen(cmd)
            time.sleep(5)
        except Exception:
            logger.error('start browser process failed')
            return

    def update_core(self, user_info, socket_port):
        """
        下载所有内核，打开店铺前调用，需客户端版本5.285.7以上
        因为http有超时时间，所以这个action适合循环调用，直到返回成功
        """
        data = {
            "action": "updataCore",
            "requestId": str(uuid.uuid4()),
        }
        data.update(user_info)
        time.sleep(2)
        # while True:
        result = self.send_http(data, socket_port)
        
        logger.info(result)
        if result is None:
            logger.info("等待客户端启动...")
            time.sleep(2)
            raise Exception("客户端未启动")
        if result.get("statusCode") is None or result.get("statusCode") == -10003:
            logger.error("当前版本不支持此接口，请升级客户端")
            return "VERSION ERROR"
        elif result.get("statusCode") == 0:
            logger.info("更新内核完成")
            return True
        else:
            logger.info(f"等待更新内核: {json.dumps(result)}")
            time.sleep(2)

    def send_http(self, data, socket_port):
        """
        通讯方式
        :param data:
        :return:
        """
        try:
            url = 'http://127.0.0.1:{}'.format(socket_port)
            response = requests.post(url, json.dumps(
                data).encode('utf-8'), timeout=120)
            return json.loads(response.text)
        except Exception as err:
            logger.error(err)

    def delete_all_cache(self):
        """
        删除所有店铺缓存
        非必要的，如果店铺特别多、硬盘空间不够了才要删除
        """
        if not platform.system() == 'Windows':
            return
        local_appdata = os.getenv('LOCALAPPDATA')
        cache_path = os.path.join(local_appdata, 'SuperBrowser')
        if os.path.exists(cache_path):
            shutil.rmtree(cache_path)

    def delete_all_cache_with_path(self, path):
        """
        :param path: 启动客户端参数使用--enforce-cache-path时设置的缓存路径
        删除所有店铺缓存
        非必要的，如果店铺特别多、硬盘空间不够了才要删除
        """
        if not platform.system() == 'Windows':
            return
        cache_path = os.path.join(path, 'SuperBrowser')
        if os.path.exists(cache_path):
            shutil.rmtree(cache_path)

    def open_store(self, store_info, isWebDriverReadOnlyMode=0, isprivacy=0, isHeadless=0, cookieTypeSave=0, jsInfo="", user_info={}):
        while True:
            request_id = str(uuid.uuid4())
            data = {
                "action": "startBrowser",
                "isWaitPluginUpdate": 0,
                "isHeadless": isHeadless,
                "requestId": request_id,
                "isWebDriverReadOnlyMode": isWebDriverReadOnlyMode,
                "cookieTypeLoad": 0,
                "cookieTypeSave": cookieTypeSave,
                "runMode": "1",
                "isLoadUserPlugin": False,
                "pluginIdType": 1,
                "privacyMode": isprivacy
            }
            data.update(user_info)

            if store_info.isdigit():
                data["browserId"] = store_info
            else:
                data["browserOauth"] = store_info

            if len(str(jsInfo)) > 2:
                data["injectJsInfo"] = json.dumps(jsInfo)

            r = self.send_http(data, self.socket_port)
            if str(r.get("statusCode")) == "0":
                return r
            elif str(r.get("statusCode")) == "-10003":
                logger.error(f"login Err {json.dumps(r, ensure_ascii=False)}")
                continue
                # return False
            else:
                logger.error(f"Fail {json.dumps(r, ensure_ascii=False)} ")
                continue
                # return False

    def close_store(self, browser_oauth, user_info={}):
        request_id = str(uuid.uuid4())
        data = {
            "action": "stopBrowser",
            "requestId": request_id,
            "duplicate": 0,
            "browserOauth": browser_oauth
        }
        data.update(user_info)

        r = self.send_http(data, self.socket_port)
        if str(r.get("statusCode")) == "0":
            return r
        elif str(r.get("statusCode")) == "-10003":
            logger.error(f"login Err {json.dumps(r, ensure_ascii=False)}")
            exit()
        else:
            logger.error(f"Fail {json.dumps(r, ensure_ascii=False)} ")
            exit()

    def get_browser_list(self, user_info={}):
        while True:
            request_id = str(uuid.uuid4())
            data = {
                "action": "getBrowserList",
                "requestId": request_id
            }
            data.update(user_info)

            r = self.send_http(data, self.socket_port)
            if str(r.get("statusCode")) == "0":
                # logger.info(r)
                return r.get("browserList")
            elif str(r.get("statusCode")) == "-10003":
                logger.error(f"login Err {json.dumps(r, ensure_ascii=False)}")
                exit()
            else:
                logger.error(f"Fail {json.dumps(r, ensure_ascii=False)} ")
                time.sleep(2)

    def get_browser_context(self, port):
        """
        获取 drissionpage 浏览器会话
        """
        # 使用正确的方式创建 ChromiumPage 对象
        # co = ChromiumOptions()
        # co.address()
        logger.info(f"店铺端口-{port}")
        page = Chromium(port).latest_tab
        return page

    def open_ip_check(self, browser_context, ip_check_url):
        """
        打开ip检测页检测ip是否正常
        :param browser_context: drissionpage 浏览器会话
        :param ip_check_url ip检测页地址
        :return 检测结果
        """
        try:
            browser_context.get(ip_check_url)
            success_button = browser_context.ele(
                '//button[contains(@class, "styles_btn--success")]')
            success_button.wait_for_appear(timeout=60)  # 等待查找元素60秒
            logger.info("ip检测成功")
            return True
        except Exception as e:
            logger.error("ip检测异常:" + traceback.format_exc())
            return False

    def open_launcher_page(self, browser_context, launcher_page):
        browser_context.get(launcher_page)
        time.sleep(6)

    def get_exit(self, user_info={}):
        """
        关闭客户端
        :return:
        """
        data = {"action": "exit", "requestId": str(uuid.uuid4())}
        data.update(user_info)
        # logger.info('@@ get_exit...' + json.dumps(data))
        self.send_http(data, self.socket_port)

    def use_one_browser_run_task(self, browser, user_info={}):
        """
        打开一个店铺运行脚本
        :param browser: 店铺信息
        """
        # 如果要指定店铺ID, 获取方法:登录紫鸟客户端->账号管理->选择对应的店铺账号->点击"查看账号"进入账号详情页->账号名称后面的ID即为店铺ID
        store_id = browser.get('browserOauth')
        store_name = browser.get("browserName")
        # 打开店铺
        logger.info(f"=====打开店铺：{store_name}=====")
        ret_json = self.open_store(store_id, user_info=user_info)
        if not ret_json:
            return False
        # logger.info(ret_json)
        store_id = ret_json.get("browserOauth")
        if store_id is None:
            store_id = ret_json.get("browserId")
        # print(ret_json)
        # 获取 drissionpage 浏览器会话
        browser_context = self.get_browser_context(
            ret_json.get('debuggingPort'))
        if browser_context is None:
            logger.info(f"=====关闭店铺：{store_name}=====")
            self.close_store(store_id, user_info=user_info)
            return False
        logger.info(f"打开店铺主页=={ret_json.get('launcherPage')}")
    
        if "amazon" not in ret_json.get("launcherPage"):
            logger.error("店铺主页地址异常，请检查")
            return False
        time.sleep(2)
        self.open_launcher_page(browser_context, ret_json.get("launcherPage"))
        time.sleep(3)
        is_switch_account = browser_context.ele("#ap-account-switcher-container", timeout=2)
        if is_switch_account:
            logger.info("账号切换页")
            switch_account_link = browser_context.ele('//a[@data-name="switch_account_request"]', timeout=2)
            if switch_account_link:
                switch_account_link.click()
                logger.info("成功点击切换账号链接")
            else:
                logger.error("未找到切换账号链接")
        signInSubmit = browser_context.ele("#signInSubmit", timeout=2)
        if signInSubmit:
            signInSubmit.click()
        signInSubmit = browser_context.ele("#signInSubmit", timeout=2)
        if signInSubmit:
            signInSubmit.click()
            time.sleep(1)
        otp_code = browser_context.ele("#auth-mfa-otpcode", timeout=2)
        if otp_code:
            time.sleep(1)
            browser_context.ele("#auth-signin-button").click()
        otp_code = browser_context.ele("#auth-signin-button", timeout=2)
        if otp_code:
            time.sleep(1)
            browser_context.ele("#auth-signin-button").click()
        return browser_context

        # # 获取ip检测页地址
        # ip_check_url = ret_json.get("ipDetectionPage")
        # if not ip_check_url:
        #     logger.error("ip检测页地址为空，请升级紫鸟浏览器到最新版")
        #     logger.info(f"=====关闭店铺：{store_name}=====")
        #     self.close_store(store_id, user_info=user_info)
        #     exit()
        # # 执行脚本
        # try:
        #     ip_usable = self.open_ip_check(browser_context, ip_check_url)
        #     if ip_usable:
        #         logger.info("ip检测通过，打开店铺平台主页")
        #         self.open_launcher_page(browser_context, ret_json.get("launcherPage"))
        #         # 打开店铺平台主页后进行后续自动化操作
        #     else:
        #         logger.info("ip检测不通过，请检查")
        # except:
        #     logger.error("脚本运行异常:" + traceback.format_exc())
        # finally:
        #     logger.info(f"=====关闭店铺：{store_name}=====")
        #     self.close_store(store_id, user_info=user_info)

    def use_all_browser_run_task(self, browser_list, user_info={}):
        """
        循环打开所有店铺运行脚本
        :param browser_list: 店铺列表
        """
        for browser in browser_list:
            self.use_one_browser_run_task(browser, user_info)

    def get_browser_site(self, page, domain):
        try:
            self.page = page
            self.page.listen.start(
                targets="account-switcher/regional-accounts/merchantMarketplace\?globalAccountId=", method="GET", is_regex=True)

            self.page.get(
                f"https://{domain}/account-switcher/default/merchantMarketplace")

            res = self.page.listen.wait()  # 等待并获取一个数据包
            if res.response.body:
                logger.info("站点获取成功")
                return res.response.body
            else:
                logger.error("站点获取异常")
                raise Exception("站点获取异常")
                # break
        except Exception as e:
            raise Exception(e)


# 主任务类


class Task():
    def __init__(self, page, data):
        self.page = page
        self.data = data
        logger.info(self.data)
        self.result_dict = {}
        if self.data.get("task_content", "") != "":
            try:
                task_content_list = json.loads(
                    self.data.get("task_content", ""))
                for item in task_content_list:
                    print(item)
                    box_num = item["box_num"]
                    if box_num in self.result_dict:
                        logger.error(f"货件名称箱号异常-存在重复箱号")
                        raise Exception(f"货件名称箱号异常-存在重复箱号")
                    self.result_dict[str(box_num)] = item
            except json.JSONDecodeError as e:
                logger.error(f"货件名称箱号异常-{e}")
                raise Exception(f"货件名称箱号异常-{e}")
        
    def run(self):
        common_task = Common()
        domain = common_task.get_domain_of_existing_page(url=self.page.url)
        logger.info(f"当前页面的域名是: {domain}")
        fba_no = self.data.get("fba_no")
        self.page.get(f"https://{domain}/fba/inbound-shipment/summary/{fba_no}/tracking")
        error_text = ""
        while True:
            try:
                error_ele = self.page.ele(".error-message-banner-padding", timeout=0.5)
                right_ele = self.page.ele(".non-pcp-spd-container", timeout=0.5)
                if error_ele:
                    error_text = error_ele.text.replace('\\n','')
                    logger.error(f"{fba_no}-{error_text}-货件号不存在")
                    raise Exception(f"{fba_no}-{error_text}-货件号不存在")  # 抛出异常，跳出循环
                if right_ele:
                    logger.info("货件名称存在，开始填写")
                    break
            except Exception as e:
                logger.error(f"出现错误: {e}")
                raise Exception(e)  # 让外层捕获此异常

        if error_text != "":
            raise Exception(error_text)  # 抛出异常，终止执行

        element = self.page.ele('.non-pcp-spd-container')
        if element:
            while True:
                try:
                    kat_input_element = self.page.ele('.non-pcp-spd-row').ele('.non-pcp-spd-tracking-id').ele("tag:kat-input")
                    if kat_input_element:
                        logger.info("识别到输入框")
                        break
                except Exception as e:
                    logger.error(f"无法识别输入框: {e}")
                    raise Exception(e)  # 抛出异常，终止循环

            elements = self.page.eles('.non-pcp-spd-row')
            is_click = False
            if elements:
                for index, table_element in enumerate(elements):
                    kat_input_element = table_element.ele('.non-pcp-spd-tracking-id').ele("tag:kat-input")
                    if kat_input_element:
                        disabled = kat_input_element.attr('disabled')
                        if disabled is None:
                            kat_table_cell = table_element.ele('tag:kat-table-cell')
                            if kat_table_cell:
                                input_value = self.result_dict.get(str(kat_table_cell.text), "")
                                if input_value != "":
                                    tracking_num = input_value.get("tracking_num", "")
                                    if tracking_num:
                                        kat_input_element.click()
                                        kat_input_element.clear()
                                        kat_input_element.input(tracking_num)
                                        logger.info(f"箱号-{kat_table_cell.text}:{tracking_num}")
                                        is_click = True
                                    else:
                                        logger.error("箱号-{kat_table_cell.text}跟踪编号缺失")
                                        raise Exception(f"箱号-{kat_table_cell.text}跟踪编号缺失")
                                else:
                                    logger.error(f"{str(kat_table_cell.text)}-箱号不存在")
                                    raise Exception(f"{str(kat_table_cell.text)}-箱号不存在")
                            else:
                                logger.error(f"未找到 kat-table-cell 元素")
                        else:
                            logger.error("输入框已禁用")
                    else:
                        logger.error("未找到 kat-input 元素")
                if is_click:
                    click_update = self.page.ele(".tracking-id-submit").ele(".button")
                    if click_update:
                        click_update.click()
                        self.page.ele(".validation-confirmed")
                        time.sleep(2)
                # else:
                #     logger.error(f"{fba_no}不存在追踪编号")
                #     raise Exception(f"货件名称-{fba_no}不存在追踪编号")
            # else:
            #     logger.error(f"{fba_no}不存在")
            #     raise Exception(f"货件名称-{fba_no}不存在")
        # else:
        #     logger.error(f"{fba_no}不存在")
        #     raise Exception(f"货件名称-{fba_no}不存在")

def job():
    global is_job_running
    if is_job_running:
        return  # 如果任务正在执行，直接返回，不开启新任务
    # 店铺实例化
    try:
        is_job_running = True
        is_windows = platform.system() == 'Windows'
        is_mac = platform.system() == 'Darwin'

        if not is_windows and not is_mac:
            logger.error("webdriver/cdp只支持windows和mac操作系统")
            exit()

        if is_windows:
            client_path = R'C:\Users\<USER>\SuperBrowser\starter.exe'  # 客户端程序starter.exe的路径
        else:
            client_path = R'ziniao'  # 客户端程序名称
        logger.info("获取可用通信端口....")
        common_task = Common()
        while True:
            try:
                # 获取可用端口号
                socket_port = common_task.find_available_port()
                logger.info(f"可用端口--{socket_port}")

                user_info = {
                    "company": "KUIMINGKEJI",
                    "username": "yxyyd",
                    "password": "Yxyyd@"
                }

                # 实例化 superBrowser 类
                browser_manager = superBrowser(socket_port)
                # 终止紫鸟客户端已启动的进程
                browser_manager.kill_process(is_windows)
                browser_manager.kill_main_process(is_windows)
                logger.info(f"实例账号：-{user_info.get('username')}")
                logger.info("=====启动客户端=====")
                browser_manager.start_browser(is_windows, is_mac, client_path, socket_port)
                logger.info("=====更新内核=====")
                browser_manager.update_core(user_info, socket_port)
                break
            except Exception as e:
                logger.error(f"启动客户端失败: {e}")
                time.sleep(2)
                continue
               

        """获取店铺列表"""
        logger.info("=====获取店铺列表=====")
        browser_list = browser_manager.get_browser_list(user_info)
        # logger.info(browser_list)

        if browser_list is None or not browser_list:
            logger.error("店铺列表为空")
            return
        else:
            logger.info("店铺列表获取成功！")

        write_dict_list_to_xlsx_pandas(browser_list=browser_list)
        # 数据库实例化
        sql_connect = SqlConnect(host='**************',
                                user='bms_w', password='tjY4VeK7W3hZ', database='ims')
        query = "SELECT * FROM task_fba_tracking_number where status = 1 ORDER BY super_browser_name ASC, platform_site ASC"
        result = sql_connect.read_data(query)
        # 定义全局店铺名称、站点名称、全局店铺数据对象、全局站点数据对象
        gloabl_super_browser_name = ""
        global_super_browser_site = ""
        global_super_browser_data = {}
        global_site_dict = {}
        site_map_dict = {'uk': '英国', 'us': '美国', 'fr': '法国',
            'es': '西班牙', 'it': '意大利', 'de': '德国'}
        global_page = ""
        if result:
            for row in result:
                id = row.get("id")
                super_browser_name = row.get("super_browser_name")
                
                platform_site = row.get("platform_site")
                try:
                    
                    fba_no = row.get("fba_no")
                    task_content = row.get("task_content")
                    
                    if super_browser_name == "" or platform_site == "":
                        logger.error("紫鸟店铺名称或者站点有误！")
                        update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '紫鸟店铺名称或者站点有误！' WHERE id = {id}"
                        rows_affected = sql_connect.update_data(update_query)
                        continue
                    if gloabl_super_browser_name != "" and super_browser_name == gloabl_super_browser_name:
                        logger.info("店铺一致，无需切换")
                        super_browser_data = global_super_browser_data
                        site_dict = global_site_dict
                        page = global_page
                    else:
                        if gloabl_super_browser_name != "":
                            logger.info("店铺更新，关闭旧店铺")
                            browser_oauth = global_super_browser_data.get("browserOauth")
                            browser_manager.close_store(user_info=user_info,browser_oauth=browser_oauth)
                    
                        super_browser_data = common_task.find_dict_by_browser_name(
                            browser_list, super_browser_name)
                        if super_browser_data:
                            global_super_browser_data = super_browser_data
                            if "邮箱" in super_browser_data.get("browserName"):
                                gloabl_super_browser_name = ""
                                global_super_browser_site = ""
                                global_super_browser_data = {}
                                global_site_dict = {}
                                logger.info(f"该店铺只用于登录店铺邮箱，参数无效！")
                                update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '该店铺只用于登录店铺邮箱，参数无效！' WHERE id = {id}"
                                rows_affected = sql_connect.update_data(update_query)
                                continue
                            page = browser_manager.use_one_browser_run_task(
                                super_browser_data, user_info)
                            if not page:
                                update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '店铺句柄获取异常' WHERE id = {id}"
                                rows_affected = sql_connect.update_data(update_query)
                                if rows_affected is not None:
                                    logger.error(f"状态变更: {rows_affected}")
                                    continue
                                else:
                                    logger.error("更新数据失败")
                                    continue
                            global_page = page
                            domain = common_task.get_domain_of_existing_page(url=page.url)
                        
                            browser_sites = browser_manager.get_browser_site(page=page,domain=domain)
                            if browser_sites:
                                site_dict = {}
                                for i in browser_sites.get("regionalAccounts",[]):
                                    site_dict[i.get('label')] = {"mons_sel_dir_mcid":i.get("ids",{}).get("mons_sel_dir_mcid"),"mons_sel_mkid":i.get("ids",{}).get("mons_sel_mkid"),"globalAccountId":i.get("globalAccountId")}
                                global_site_dict = site_dict
                        else:
                            global_page = ""
                            gloabl_super_browser_name = ""
                            global_super_browser_site = ""
                            global_super_browser_data = {}
                            global_site_dict = {}
                            logger.error(f"{super_browser_name}-紫鸟账号内不存在改紫鸟店铺")
                            update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '紫鸟账号内不存在改紫鸟店铺' WHERE id = {id}"
                            rows_affected = sql_connect.update_data(update_query)
                            continue
                    
                    if platform_site != global_super_browser_site or super_browser_name != gloabl_super_browser_name:
                        map_site = site_map_dict.get(platform_site.lower(),"")
                        browser_site_data = site_dict.get(map_site,{})
                        if browser_site_data == {}:
                            logger.error(f"店铺不存在该站点-{super_browser_name}-{platform_site}-{map_site}")
                            update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '店铺不存在该站点-{super_browser_name}' WHERE id = {id}"
                            rows_affected = sql_connect.update_data(update_query)
                            continue
                        else:
                            site_url = f"https:{domain}/home?mons_sel_dir_mcid={browser_site_data.get('mons_sel_dir_mcid')}&mons_sel_mkid={browser_site_data.get('mons_sel_mkid')}&mons_sel_dir_paid={browser_site_data.get('globalAccountId')}&ignore_selection_changed=true"
                            page.get(site_url)
                            try:
                                is_fukuang = page.ele(".status-page-message",timeout=3)
                                if is_fukuang:
                                    error_text = is_fukuang.text.replace('\\n','')
                                    update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '{error_text}' WHERE id = {id}"
                                    rows_affected = sql_connect.update_data(update_query)
                                    continue
                            except Exception:
                                pass
                            global_super_browser_site = platform_site
                    else:
                        # if gloabl_super_browser_name != "" and super_browser_name == gloabl_super_browser_name:
                            logger.info("站点一致，无需切换")
                    gloabl_super_browser_name = super_browser_name
                    if super_browser_data:
                        try:
                            logger.info(f"店铺-{super_browser_name}-站点-{platform_site}-fba_no-{fba_no}")
                            task = Task(page=page, data={
                                "fba_no": fba_no,
                                "task_content": task_content
                            })
                            task.run()
                            update_query = f"UPDATE task_fba_tracking_number SET status = '10', response_message = '任务执行完毕' WHERE id = {id}"
                            rows_affected = sql_connect.update_data(update_query)
                        except Exception as e:
                            update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '{e}' WHERE id = {id}"
                            rows_affected = sql_connect.update_data(update_query)
                        
                        # break
                    else:
                        update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '店铺{super_browser_name}不存在！' WHERE id = {id}"
                        rows_affected = sql_connect.update_data(update_query)
                        if rows_affected is not None:
                            logger.error(f"状态变更: {rows_affected}")
                            continue
                        else:
                            logger.error("更新数据失败")
                            continue
                            
                            

                except Exception as e:
                    # update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '{e}' WHERE id = {id}"
                    # rows_affected = sql_connect.update_data(update_query)
                    logger.error(f"任务执行异常-{e}")
                    update_query = f"UPDATE task_fba_tracking_number SET status = '20', response_message = '{e}' WHERE id = {id}"
                    browser_manager.kill_process(is_windows)
                    gloabl_super_browser_name = ""
                    global_super_browser_site = ""
                    global_super_browser_data = {}
                    global_site_dict = {}
            browser_manager.get_exit(user_info)
            logger.info(f"全部任务执行完毕")
            is_job_running = False
        logger.info("暂无任务；继续轮询")
        is_job_running = False
    except Exception as e:
        logger.error(f"出错-{e}")
        is_job_running = False
def schedule_jobs():
    # 设置任务时间范围为9:00到24:00之间
    for hour in range(9, 24):
        # 每半小时执行一次
        schedule.every().day.at(f"{hour:02d}:00").do(job)
        schedule.every().day.at(f"{hour:02d}:30").do(job)



if __name__ == "__main__":
    job()
    schedule_jobs()
    while True:
    # 检查并执行任务
        schedule.run_pending()
        time.sleep(1)
