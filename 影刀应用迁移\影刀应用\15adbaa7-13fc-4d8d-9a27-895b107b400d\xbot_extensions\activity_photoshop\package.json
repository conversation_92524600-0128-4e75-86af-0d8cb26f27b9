{"uuid": "b985856d-d09f-48bd-850b-2b3b37cb98ab", "name": "Photoshop 扩展", "icon": "https://winrobot-pub-a.oss-cn-hangzhou.aliyuncs.com/images/1e/80/1e80a971e586a0883fae751a29d1d03c.png", "version": "25.1.0", "tags": "official", "software": null, "software_title": null, "package_version": 5, "feature_list": [], "description": "提供包括打开 PSD 文件、替换图层内容、导出为图片、显示和隐藏图层等等 Photoshop 功能。在 21.0.2 版本上测试通过。", "instruction": null, "use_latest_pip": false, "videoName": null, "startup": "main", "robot_type": "activity", "activity_code": "activity_photoshop", "flows": [{"name": "main", "filename": "main", "kind": "Visual", "opened": false, "groupName": null}, {"name": "ps", "filename": "ps", "kind": "Code", "opened": false, "groupName": "modules"}, {"name": "pil_api", "filename": "pil_api", "kind": "Code", "opened": false, "groupName": "modules"}, {"name": "ps_back", "filename": "ps_back", "kind": "Code", "opened": false, "groupName": "modules"}, {"name": "打开 PSD 文件", "filename": "process1", "kind": "Visual", "opened": true, "groupName": "文件操作"}, {"name": "显示或隐藏图层", "filename": "process2", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "激活图层", "filename": "process3", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "修改图层文字", "filename": "process4", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "替换图层图片", "filename": "process5", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "导出为图片", "filename": "process6", "kind": "Visual", "opened": true, "groupName": "文件操作"}, {"name": "关闭 PSD 文件", "filename": "process7", "kind": "Visual", "opened": true, "groupName": "文件操作"}, {"name": "另存为 PSD 文件", "filename": "process8", "kind": "Visual", "opened": true, "groupName": "文件操作"}, {"name": "获取所有图层名", "filename": "process9", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "获取图层文字", "filename": "process10", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "设置文字图层字体", "filename": "process11", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "获取图层所属组", "filename": "process12", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "test_invoke_ps", "filename": "test_invoke_ps", "kind": "Code", "opened": false, "groupName": "test"}, {"name": "重命名图层", "filename": "process13", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "内容识别填充", "filename": "process14", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "修改颜色填充图层", "filename": "process15", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "设置形状图层描边", "filename": "process16", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "invoke_module", "filename": "invoke_module", "kind": "Code", "opened": false, "groupName": "modules"}, {"name": "导出图层为PNG", "filename": "process17", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "添加图片图层", "filename": "process18", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "删除图层", "filename": "process19", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "test_invoke_pyd", "filename": "test_invoke_pyd", "kind": "Code", "opened": false, "groupName": "test"}, {"name": "获取图层字体信息", "filename": "process20", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "激活文档", "filename": "process21", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "替换图框", "filename": "process22", "kind": "Visual", "opened": true, "groupName": "图层操作"}, {"name": "js_code", "filename": "js_code", "kind": "Code", "opened": false, "groupName": "modules"}, {"name": "export_artboard_as_png", "filename": "export_artboard_as_png", "kind": "Code", "opened": true, "groupName": "activity"}, {"name": "test_replace_image", "filename": "test_replace_image", "kind": "Code", "opened": false, "groupName": "test"}, {"name": "replace_layer_image", "filename": "replace_layer_image", "kind": "Code", "opened": true, "groupName": "activity"}, {"name": "set_text_format", "filename": "set_text_format", "kind": "Code", "opened": true, "groupName": "activity"}, {"name": "edit_smart_object", "filename": "edit_smart_object", "kind": "Code", "opened": true, "groupName": "activity"}, {"name": "get_active_doc", "filename": "get_active_doc", "kind": "Code", "opened": true, "groupName": "activity"}], "flow_groups": [{"name": "test"}, {"name": "modules"}, {"name": "图层操作"}, {"name": "文件操作"}, {"name": "activity"}], "variables": [], "external_dependencies": [], "internaldependencies": [], "selectordependencies": [], "internalautodependencies": [], "ipaasDependencies": [], "databook_columns": [], "authority": "use", "internalautoupgrade": false, "isbrief": false, "uia_type": "PC", "persist_databook": false, "customItems": {}}