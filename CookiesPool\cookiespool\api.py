# -*- coding:UTF-8 -*-
import json
from flask import Flask, g, request
from .config import *
from .db import *

__all__ = ['app']

app = Flask(__name__)


@app.route('/')
def index():
    return '<h2>欢迎来到 Cookie Pool 系统</h2>'


def get_conn():
    """
    获取
    :return:
    """
    for website in GENERATOR_MAP:
        print(website)
        if not hasattr(g, website):
            setattr(g, website + '_cookies', eval('RedisClient' + '("cookies", "' + website + '")'))
            setattr(g, website + '_accounts', eval('RedisClient' + '("accounts", "' + website + '")'))
    return g


@app.route('/<website>/random')
def random(website):
    """
    获取随机的Cookie, 访问地址如 /weibo/random
    :return: 随机Cookie
    """
    g = get_conn()
    cookies = getattr(g, website + '_cookies').random()
    return cookies


@app.route('/<website>/add')
def add(website):
    """
    添加用户, 访问地址如 /weibo/add?u=user&p=password
    :param website: 站点
    :return: JSON 格式的响应
    """
    username = request.args.get('u')
    password = request.args.get('p', '')  # 默认密码为空字符串

    if not username:
        return json.dumps({'status': '0', 'message': '用户名为必填项'}), 400

    g = get_conn()
    print(username, password)
    getattr(g, website + '_accounts').set(username, password)
    return json.dumps({'status': '1'})


@app.route('/<website>/count')
def count(website):
    """
    获取Cookies总数
    """
    g = get_conn()
    count = getattr(g, website + '_cookies').count()
    return json.dumps({'status': '1', 'count': count})


if __name__ == '__main__':
    app.run(host='0.0.0.0')
