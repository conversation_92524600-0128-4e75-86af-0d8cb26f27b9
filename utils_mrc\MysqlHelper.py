# encoding=utf8
import time, re
import hashlib
import traceback
import pymsgbox as pg
import pymysql
from pymysql import *
from datetime import datetime
from utils_mrc.FeiShuAPI import *
from sqlalchemy import create_engine
from socket import gethostname


class MysqlHelper:
    # 定义默认数据库相关的配置项
    DB_CONFIG = {
        'host': '*************',
        'user': 'root',
        'pwd': '123456',
        # 'host': '**************',
        # 'user': 'rpa',
        # 'pwd': 'WLYJT7tXH8c',
        'db': 'rpa',
        'port': 3306,
        'charset': 'utf8mb4',
    }
    host = DB_CONFIG['host']
    user = DB_CONFIG['user']
    password = DB_CONFIG['pwd']
    port = DB_CONFIG['port']
    db = DB_CONFIG['db']
    charset = DB_CONFIG['charset']

    # todo 类的构造函数，主要用于类的初始化
    def __init__(self, host=host, user=user, password=password, db=db, charset=charset, port=port, con_msg=False):
        self.host = host
        self.port = port
        self.db = db
        self.user = user
        self.password = password
        self.charset = charset
        self.connect()
        self.err = ''
        self.engine = None
        if con_msg:
            logging(f'数据库：{self.host} {self.db} 已连接')

    # todo 建立数据库连接和打开游标
    def connect(self):
        for i in range(50):
            try:
                self.conn = connect(
                        host=self.host,
                        port=self.port,
                        db=self.db,
                        user=self.user,
                        passwd=self.password,
                        charset=self.charset,
                        connect_timeout=180)
                break
            except Exception as e:
                self.err = str(e)
                print(e)
                logging(f'第{i + 1}次连接失败，30秒后重试。。。')
                time.sleep(30)
        else:
            self.err = f'连接数据库失败超过50次：{self.host} {self.db}'
            logging(self.err)
            fsmsg.send('数据库连接异常', self.err)

        self.cursor = self.conn.cursor()

    def create_engine(self):
        # 创建数据库引擎
        engine = create_engine(f'mysql+pymysql://{self.user}:{self.password}@{self.host}:{self.port}/{self.db}?charset={self.charset}')
        self.engine = engine
        return engine

    # todo 关闭游标和关闭连接
    def close(self):
        self.cursor.close()
        self.conn.close()

    # todo 取一条数据
    def get_one(self, sql, *args):
        result = ()
        try:
            self.connect()
            self.cursor.execute(sql, *args)
            result = self.cursor.fetchone() or result
            self.close()
            self.err = ''
        except Exception as e:
            self.err = str(e)
            print(e)
        return result

    # todo 取所有数据
    def get_all(self, sql, *args):
        lst = ()
        try:
            self.connect()
            self.cursor.execute(sql, *args)
            lst = self.cursor.fetchall() or lst
            self.close()
            self.err = ''
        except Exception as e:
            traceback.print_exc()
            self.err = str(e)
            print(e)
        return lst

    # todo 取所有数据-返回字典格式
    def get_dict(self, sql, *args):
        dt = []
        try:
            self.connect()
            with self.conn.cursor(cursors.DictCursor) as cursor:
                cursor.execute(sql, *args)
                dt = cursor.fetchall() or []
                self.close()
            self.err = ''
        except Exception as e:
            self.err = str(e)
            print(e)
        return dt

    # todo 取一条数据-返回字典格式
    def get_dict_one(self, sql, *args):
        dt = {}
        try:
            self.connect()
            with self.conn.cursor(cursors.DictCursor) as cursor:
                cursor.execute(sql, *args)
                dt = cursor.fetchone() or dt
                self.close()
            self.err = ''
        except Exception as e:
            self.err = str(e)
            print(e)
        return dt

    # todo 增加数据
    def insert(self, sql, *args):
        return self.__edit(sql, *args)

    # todo 增加数据
    def insert_many(self, sql, *args):
        rowcount = 0
        full_traceback = ''
        for i in range(3):
            try:
                self.connect()
                rowcount = self.cursor.executemany(sql, *args)
                self.conn.commit()
                self.close()
                self.reset_tb_auto_id(sql, args)  # 重置自增ID
                self.err = ''
                return rowcount
            except pymysql.err.OperationalError as e:
                if e.args[0] == 1213:  # 死锁错误码
                    print(f"检测到死锁，重试 {i + 1}次...")
                    time.sleep(3)  # 等待一段时间后重试
                self.err = f'第{i + 1}次执行失败，{e}'
                rowcount = -1  # 异常则返回-1
            except Exception as e:
                full_traceback = traceback.format_exc()  # 获取完整的堆栈跟踪信息
                lines = full_traceback.splitlines()  # 将堆栈跟踪信息按行分割
                msg = lines[-1].strip()  # 获取最后一行的关键错误信息
                self.err = f'第{i + 1}次执行失败，{msg}'
                if is_test_environment():
                    print(full_traceback)
                rowcount = -1  # 异常则返回-1
                time.sleep(3)
        else:
            print(full_traceback)
        return rowcount

    # todo 修改数据
    def update(self, sql, *args):
        return self.__edit(sql, *args)

    # todo 更新数据
    def update_many(self, sql, *args):
        rowcount = 0
        try:
            self.connect()
            rowcount = self.cursor.executemany(sql, *args)
            self.conn.commit()
            self.close()
            self.err = ''
        except Exception as e:
            full_traceback = traceback.format_exc()  # 获取完整的堆栈跟踪信息
            lines = full_traceback.splitlines()  # 将堆栈跟踪信息按行分割
            msg = lines[-1].strip()  # 获取最后一行的关键错误信息
            self.err = msg
            rowcount = -1  # 异常则返回-1
            print(full_traceback)
        return rowcount

    # todo 删除数据
    def delete(self, sql, *args):
        return self.__edit(sql, *args)

    def reset_tb_auto_id(self, sql, args):
        try:
            try:
                all_sql = sql % args[0] if args else sql
            except:
                all_sql = sql
            # 正则表达式匹配表名
            pattern = r"(?i)\bINSERT\s+INTO\s+(`?\w+`?\.)?`?(\w+)`?"
            match = re.search(pattern, all_sql)
            if match:
                table_name = match.group(2)  # 捕获组2是表名
                self.connect()
                self.cursor.execute(f'ALTER TABLE {table_name} AUTO_INCREMENT = 1;')
                self.conn.commit()
                self.close()
        except:
            pass

    # todo 写数据操作具体实现，返回影响行数，增删改操作都是调用这个方法来实现，这是个私有方法，不允许类外部调用
    def __edit(self, sql, *args):
        rowcount = 0
        try:
            self.connect()
            rowcount = self.cursor.execute(sql, *args)
            self.conn.commit()
            self.close()
            self.reset_tb_auto_id(sql, args)  # 重置自增ID
            self.err = ''
        except Exception as e:
            full_traceback = traceback.format_exc()  # 获取完整的堆栈跟踪信息
            lines = full_traceback.splitlines()  # 将堆栈跟踪信息按行分割
            msg = lines[-1].strip()  # 获取最后一行的关键错误信息
            self.err = msg
            rowcount = -1  # 异常则返回-1
            print(full_traceback)
        return rowcount

    def logging(self, msg, tip='INFO'):
        """logging"""
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())} {tip}]: {msg}')


def is_test_environment():
    # 获取主机名和进程ID
    is_test = None
    test_hostname = 'MRC-YXY'
    hostname = gethostname()
    if hostname == test_hostname:
        is_test = True
    return is_test


def is_mrc_environment():
    # 获取主机名和进程ID
    is_mrc = None
    mrc_hostname = 'MRC'
    hostname = gethostname()
    if hostname == mrc_hostname:
        is_mrc = True
    return is_mrc


def logging(msg, tip='INFO', isprint=True):
    """logging"""
    info = f'[{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())} {tip}]: {msg}'
    if isprint:
        print(info)
    else:
        return info


def convert_millis_to_date(millis):
    """
    将毫秒级时间戳转换为年月日格式的日期字符串。

    参数:
    millis (int): 毫秒级时间戳

    返回:
    str: 年月日格式的日期字符串，例如 "YYYY-MM-DD"
    """
    # 将毫秒转换为秒
    timestamp_s = millis / 1000

    # 创建datetime对象
    dt_object = datetime.fromtimestamp(timestamp_s)

    # 将datetime对象格式化为年月日字符串
    date_str = dt_object.strftime('%Y-%m-%d')

    return date_str


def now_int():
    """
    获取当前时间的毫秒级戳。
    """
    return int(time.time())


def now_str():
    """
    获取当前时间的毫秒级戳。
    Returns:
        str: 表示当前时间的毫秒级戳的字符串。
    """
    return str(int(time.time() * 1000))


def get_today_zero_timestamp():
    """
    获取当前日期0点的时间戳

    Returns:
        int: 当天0点的时间戳
    """
    # 或者 int(time.mktime(date.today().timetuple()))  # 当天0点时间戳
    today = time.localtime()
    today_zero_time = time.mktime((today.tm_year, today.tm_mon, today.tm_mday, 0, 0, 0, 0, today.tm_wday, today.tm_yday))
    return int(today_zero_time)


def get_end_of_day_timestamp():
    # 获取当前日期
    current_date = datetime.now().date()

    # 构建当天的最后一个时刻
    end_of_day = datetime.combine(current_date, datetime.max.time())

    # 将 datetime 对象转换为时间戳
    end_of_day_timestamp = int(end_of_day.timestamp())

    return end_of_day_timestamp


MSMRC = None
MSXS = MysqlHelper(host='*************', port=3306, user='rpa', password='WLYJT7tXH8c', db='rpa') if not is_mrc_environment() else MSMRC
MS = MSMRC if is_mrc_environment() else MysqlHelper() if is_test_environment() else MSXS
# MS = MSXS

MS100 = MSMRC if is_mrc_environment() else MysqlHelper()

if __name__ == '__main__':
    # 定义数据库相关的配置项
    print(MSXS.get_one('SELECT 1'))
    print(get_today_zero_timestamp())
