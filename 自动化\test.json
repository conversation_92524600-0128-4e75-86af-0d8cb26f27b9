CREATE TABLE `data_lingxing_count_goods_cny` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `tenant_id` int(11) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `company_id` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '公司ID',
  `unique_id` varchar(100) NOT NULL DEFAULT '' COMMENT '唯一标识',
  `app_id` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `task_id` int(11) NOT NULL DEFAULT '0' COMMENT '任务ID',
  `datetime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所属时间',
  `task_time` int(11) NOT NULL DEFAULT '0' COMMENT '任务时间',
  `data_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态 1正常2异常3无数据',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户',
  `username` varchar(40) NOT NULL DEFAULT '' COMMENT '用户名称',
  `parent_asin` varchar(16) NOT NULL DEFAULT '' COMMENT '父ASIN',
  `asin` varchar(16) NOT NULL DEFAULT '' COMMENT 'ASIN',
  `msku` varchar(64) NOT NULL DEFAULT '' COMMENT 'MSKU',
  `local_name` varchar(64) NOT NULL DEFAULT '' COMMENT '品名',
  `model` varchar(4) NOT NULL DEFAULT '' COMMENT '型号',
  `local_sku` varchar(32) NOT NULL DEFAULT '' COMMENT 'SKU',
  `spu_name` varchar(16) NOT NULL DEFAULT '' COMMENT '款名',
  `spu` varchar(16) NOT NULL DEFAULT '' COMMENT 'SPU',
  `tags` varchar(32) NOT NULL DEFAULT '' COMMENT 'listing标签',
  `item_name` varchar(256) NOT NULL DEFAULT '' COMMENT '标题',
  `mid` varchar(4) NOT NULL DEFAULT '' COMMENT '国家',
  `sid` varchar(16) NOT NULL DEFAULT '' COMMENT '店铺',
  `bid` varchar(16) NOT NULL DEFAULT '' COMMENT '品牌',
  `cid` varchar(16) NOT NULL DEFAULT '' COMMENT '一级分类',
  `cid2` varchar(16) NOT NULL DEFAULT '' COMMENT '二级分类',
  `cid3` varchar(16) NOT NULL DEFAULT '' COMMENT '三级分类',
  `principal_names` varchar(16) NOT NULL DEFAULT '' COMMENT '负责人',
  `developer_names` varchar(16) NOT NULL DEFAULT '' COMMENT '开发人',
  `product_create_time` varchar(32) NOT NULL DEFAULT '' COMMENT '创建时间',
  `landed_price` varchar(8) NOT NULL DEFAULT '' COMMENT '售价(总价)',
  `currency` varchar(8) NOT NULL DEFAULT '' COMMENT '币种',
  `volume` int(11) NOT NULL COMMENT '销量',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '销售额',
  `order_items` int(11) NOT NULL COMMENT '订单量',
  `volume_chain_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '销量环比',
  `amount_chain_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '销量额环比',
  `order_chain_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '订单量环比',
  `volume_yoy_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '销量同比',
  `amount_yoy_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '销量额同比',
  `order_yoy_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '订单量同比',
  `net_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '净销售额',
  `b2b_volume` int(11) NOT NULL COMMENT 'B2B 销量',
  `b2b_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'B2B 销售额',
  `b2b_order_items` int(11) NOT NULL COMMENT 'B2B 订单量',
  `promotion_volume` int(11) NOT NULL COMMENT '促销销量',
  `promotion_amount` int(11) NOT NULL COMMENT '促销销售额',
  `promotion_order_items` int(11) NOT NULL COMMENT '促销订单量',
  `avg_custom_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '销售均价',
  `avg_volume` int(11) NOT NULL COMMENT '平均销量',
  `promotion_discount` int(11) NOT NULL COMMENT '促销折扣',
  `fbm_buyer_expenses` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'FBM买家运费',
  `cate_rank` varchar(64) NOT NULL DEFAULT '' COMMENT '大类排名',
  `small_cate_rank` varchar(128) NOT NULL DEFAULT '' COMMENT '小类排名',
  `return_count` int(11) NOT NULL COMMENT '退款量',
  `return_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
  `return_rate` varchar(8) NOT NULL DEFAULT '' COMMENT '退款率',
  `avg_star` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '评分',
  `reviews_count` int(11) NOT NULL COMMENT '评论数',
  `gross_profit` varchar(2) NOT NULL DEFAULT '' COMMENT '结算毛利润',
  `predict_gross_profit` varchar(4) NOT NULL DEFAULT '' COMMENT '订单毛利润',
  `gross_margin` varchar(2) NOT NULL DEFAULT '' COMMENT '结算毛利率',
  `predict_gross_margin` varchar(4) NOT NULL DEFAULT '' COMMENT '订单毛利率',
  `roi` varchar(2) NOT NULL DEFAULT '' COMMENT 'ROI',
  `return_goods_count` int(11) NOT NULL COMMENT '退货量',
  `return_goods_rate` varchar(8) NOT NULL DEFAULT '' COMMENT '退货率',
  `afn_fulfillable_quantity` int(11) NOT NULL COMMENT 'FBA-可售',
  `available_inventory` int(11) NOT NULL COMMENT '可用库存',
  `fbm_quantity` int(11) NOT NULL COMMENT 'FBM可售',
  `available_days` int(11) NOT NULL COMMENT 'FBA可售天数预估',
  `fbm_available_days` int(11) NOT NULL COMMENT 'FBM可售天数预估',
  `oversea_quantity` int(11) NOT NULL COMMENT '海外仓可用',
  `local_quantity` int(11) NOT NULL COMMENT '本地可用',
  `purchase_num` int(11) NOT NULL COMMENT '采购量',
  `month_stock_sales_ratio` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '月库销比',
  `out_stock_date` varchar(16) NOT NULL DEFAULT '' COMMENT '断货时间',
  `reserved_fc_transfers` int(11) NOT NULL COMMENT 'FBA-待调仓',
  `reserved_fc_processing` int(11) NOT NULL COMMENT 'FBA-调仓中',
  `afn_inbound_receiving_quantity` int(11) NOT NULL COMMENT 'FBA-入库中',
  `afn_total_inbound` int(11) NOT NULL COMMENT 'FBA-小计',
  `reserved_customerorders` int(11) NOT NULL COMMENT 'FBA-待发货',
  `afn_inbound_shipped_quantity` int(11) NOT NULL COMMENT 'FBA-在途',
  `afn_inbound_working_quantity` int(11) NOT NULL COMMENT 'FBA-计划入库',
  `afn_unsellable_quantity` int(11) NOT NULL COMMENT 'FBA-不可售',
  `cpc` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CPC',
  `ctr` varchar(8) NOT NULL DEFAULT '' COMMENT 'CTR',
  `spend` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '广告花费',
  `sb_spend` int(11) NOT NULL COMMENT 'SB广告费',
  `sbv_spend` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'SBV广告费',
  `sd_spend` int(11) NOT NULL COMMENT 'SD广告费',
  `sp_spend` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'SP广告费',
  `roas` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'ROAS',
  `acos` varchar(8) NOT NULL DEFAULT '' COMMENT 'ACOS',
  `acoas` varchar(8) NOT NULL DEFAULT '' COMMENT 'ACoAS',
  `asoas` varchar(8) NOT NULL DEFAULT '' COMMENT 'ASoAS',
  `cpo` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CPO',
  `cpu` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CPU',
  `cpm` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CPM',
  `ad_sales_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '广告销售额',
  `ad_order_quantity` int(11) NOT NULL COMMENT '广告订单量',
  `ad_direct_order_quantity` int(11) NOT NULL COMMENT '直接成交订单量',
  `ad_direct_sales_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '直接成交销售额',
  `adv_rate` varchar(8) NOT NULL DEFAULT '' COMMENT '广告订单占比',
  `ad_cvr` varchar(8) NOT NULL DEFAULT '' COMMENT '广告CVR',
  `impressions` int(11) NOT NULL COMMENT '展示',
  `clicks` int(11) NOT NULL COMMENT '点击',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT ' 创建时间 ',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ' 更新时间 ',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_id` (`unique_id`) USING BTREE COMMENT ' 唯一ID ',
  KEY `datetime` (`datetime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=' 领星统计数据-产品表现 ';




第一个请求网址
https://gw.lingxingerp.com/newadmin/custom/set
请求方法
POST

import requests

headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'ak-client-type': 'web',
    'ak-origin': 'https://muke.lingxing.com',
    'auth-token': '7da2JF8GBEOI1uWc2rebQyEtbOP/EjS6pSFF3IVaaot8bcCLOVmj+kdkasOi8a3BFrd49yoE8VlSJnMZkdYjMyE+BrPqjnMy72A/KR7Qs+jyXPEW/gpru0EhrMCi7k2kPLZkDuqaASmcikGu2nRaTQ',
    'content-type': 'application/json;charset=UTF-8',
    'origin': 'https://muke.lingxing.com',
    'priority': 'u=1, i',
    'referer': 'https://muke.lingxing.com/',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'cross-site',
    'sec-fetch-storage-access': 'active',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'x-ak-company-id': '901122764666708480',
    'x-ak-env-key': 'muke',
    'x-ak-language': 'zh',
    'x-ak-platform': '1',
    'x-ak-request-id': '0f02910c-ee86-4156-baf0-138ecf38f5df',
    'x-ak-request-source': 'erp',
    'x-ak-uid': '10834605',
    'x-ak-version': '*******.0.028',
    'x-ak-zid': '1',
}

json_data = {
    'info': '{"asin":{"id":"asin","title":"商品信息","isShow":true,"name":"ASIN","hide":false},"parent_asin":{"id":"parent_asin","title":"商品信息","isShow":true,"name":"父ASIN","hide":false},"msku":{"id":"msku","title":"商品信息","isShow":true,"name":"MSKU","hide":false},"sid":{"id":"sid","title":"商品信息","isShow":true,"name":"店铺","hide":false},"mid":{"id":"mid","title":"商品信息","isShow":true,"name":"国家","hide":false},"landed_price":{"id":"landed_price","title":"商品信息","isShow":true,"name":"售价（总价）","hide":false},"item_name":{"id":"item_name","title":"商品信息","isShow":true,"name":"标题","hide":false},"auto_tags":{"id":"auto_tags","title":"商品信息","isShow":true,"name":"自动标签","hide":false},"principal_names":{"id":"principal_names","title":"商品信息","isShow":true,"name":"Listing负责人","hide":false},"product_create_time":{"id":"product_create_time","title":"商品信息","isShow":true,"name":"创建时间","hide":false},"local_name":{"id":"local_name","title":"商品信息","isShow":true,"name":"品名","hide":false},"local_sku":{"id":"local_sku","title":"商品信息","isShow":true,"name":"SKU","hide":false},"spu_name":{"id":"spu_name","title":"商品信息","isShow":true,"name":"款名","hide":false},"spu":{"id":"spu","title":"商品信息","isShow":true,"name":"SPU","hide":false},"attributes":{"id":"attributes","title":"商品信息","isShow":false,"name":"属性","hide":true},"product_tags":{"id":"product_tags","title":"商品信息","isShow":false,"name":"产品标签","hide":true},"model":{"id":"model","title":"商品信息","isShow":true,"name":"型号","hide":false},"cid":{"id":"cid","title":"商品信息","isShow":true,"name":"分类","hide":false},"bid":{"id":"bid","title":"商品信息","isShow":true,"name":"品牌","hide":false},"developer_names":{"id":"developer_names","title":"商品信息","isShow":true,"name":"开发人","hide":false},"suppliers":{"id":"suppliers","title":"商品信息","isShow":false,"name":"供应商","hide":true},"cg_price":{"id":"cg_price","title":"商品信息","isShow":false,"name":"采购成本","hide":true},"volume":{"id":"volume","title":"销售数据","isShow":true,"name":"销量","hide":false},"amount":{"id":"amount","title":"销售数据","isShow":true,"name":"销售额","hide":false},"order_items":{"id":"order_items","title":"销售数据","isShow":true,"name":"订单量","hide":false},"volume_chain_ratio":{"id":"volume_chain_ratio","title":"销售数据","isShow":true,"name":"销量环比","hide":false},"amount_chain_ratio":{"id":"amount_chain_ratio","title":"销售数据","isShow":true,"name":"销售额环比","hide":false},"order_chain_ratio":{"id":"order_chain_ratio","title":"销售数据","isShow":true,"name":"订单量环比","hide":false},"volume_yoy_ratio":{"id":"volume_yoy_ratio","title":"销售数据","isShow":true,"name":"销量同比","hide":false},"amount_yoy_ratio":{"id":"amount_yoy_ratio","title":"销售数据","isShow":true,"name":"销售额同比","hide":false},"order_yoy_ratio":{"id":"order_yoy_ratio","title":"销售数据","isShow":true,"name":"订单量同比","hide":false},"net_amount":{"id":"net_amount","title":"销售数据","isShow":true,"name":"净销售额","hide":false},"b2b_volume":{"id":"b2b_volume","title":"销售数据","isShow":true,"name":"B2B销量","hide":false},"b2b_amount":{"id":"b2b_amount","title":"销售数据","isShow":true,"name":"B2B销售额","hide":false},"b2b_order_items":{"id":"b2b_order_items","title":"销售数据","isShow":true,"name":"B2B订单量","hide":false},"promotion_volume":{"id":"promotion_volume","title":"销售数据","isShow":true,"name":"促销销量","hide":false},"promotion_amount":{"id":"promotion_amount","title":"销售数据","isShow":true,"name":"促销销售额","hide":false},"promotion_order_items":{"id":"promotion_order_items","title":"销售数据","isShow":true,"name":"促销订单量","hide":false},"avg_custom_price":{"id":"avg_custom_price","title":"销售数据","isShow":true,"name":"销售均价","hide":false},"avg_volume":{"id":"avg_volume","title":"销售数据","isShow":true,"name":"平均销量","hide":false},"promotion_discount":{"id":"promotion_discount","title":"销售数据","isShow":true,"name":"促销折扣","hide":false},"fbm_buyer_expenses":{"id":"fbm_buyer_expenses","title":"销售数据","isShow":true,"name":"FBM买家运费","hide":false},"cate_rank":{"id":"cate_rank","title":"表现","isShow":true,"name":"大类排名","hide":false},"small_cate_rank":{"id":"small_cate_rank","title":"表现","isShow":true,"name":"小类排名","hide":false},"return_count":{"id":"return_count","title":"表现","isShow":true,"name":"退款量","hide":false},"return_amount":{"id":"return_amount","title":"表现","isShow":true,"name":"退款金额","hide":false},"return_rate":{"id":"return_rate","title":"表现","isShow":true,"name":"退款率","hide":false},"avg_star":{"id":"avg_star","title":"表现","isShow":true,"name":"评分","hide":false},"reviews_count":{"id":"reviews_count","title":"表现","isShow":true,"name":"评论数","hide":false},"comment_rate":{"id":"comment_rate","title":"表现","isShow":true,"name":"留评率","hide":false},"gross_profit":{"id":"gross_profit","title":"表现","isShow":true,"name":"结算毛利润","hide":false},"predict_gross_profit":{"id":"predict_gross_profit","title":"表现","isShow":true,"name":"订单毛利润","hide":false},"gross_margin":{"id":"gross_margin","title":"表现","isShow":true,"name":"结算毛利率","hide":false},"predict_gross_margin":{"id":"predict_gross_margin","title":"表现","isShow":true,"name":"订单毛利率","hide":false},"roi":{"id":"roi","title":"表现","isShow":true,"name":"ROI","hide":false},"return_goods_count":{"id":"return_goods_count","title":"表现","isShow":true,"name":"退货量","hide":false},"return_goods_rate":{"id":"return_goods_rate","title":"表现","isShow":true,"name":"退货率","hide":false},"avg_landed_price":{"id":"avg_landed_price","title":"表现","isShow":false,"name":"平均售价","hide":true},"fbm_quantity":{"id":"fbm_quantity","title":"库存","isShow":true,"name":"FBM可售","hide":false},"afn_fulfillable_quantity":{"id":"afn_fulfillable_quantity","title":"库存","isShow":true,"name":"FBA可售","hide":false},"reserved_fc_transfers":{"id":"reserved_fc_transfers","title":"库存","name":"FBA待调仓","isShow":true,"hide":false},"reserved_fc_processing":{"id":"reserved_fc_processing","title":"库存","name":"FBA调仓中","isShow":true,"hide":false},"afn_inbound_receiving_quantity":{"id":"afn_inbound_receiving_quantity","title":"库存","name":"FBA入库中","isShow":true,"hide":false},"afn_total_inbound":{"id":"afn_total_inbound","title":"库存","name":"FBA库存","isShow":true,"hide":false},"reserved_customerorders":{"id":"reserved_customerorders","title":"库存","name":"FBA待发货","isShow":true,"hide":false},"afn_inbound_shipped_quantity":{"id":"afn_inbound_shipped_quantity","title":"库存","name":"FBA在途","isShow":true,"hide":false},"afn_inbound_working_quantity":{"id":"afn_inbound_working_quantity","title":"库存","name":"FBA计划入库","isShow":true,"hide":false},"afn_unsellable_quantity":{"id":"afn_unsellable_quantity","title":"库存","name":"FBA不可售","isShow":true,"hide":false},"available_inventory":{"id":"available_inventory","title":"库存","isShow":true,"name":"可用库存","hide":false},"available_days":{"id":"available_days","title":"库存","isShow":true,"name":"FBA可售天数预估","hide":false},"fbm_available_days":{"id":"fbm_available_days","title":"库存","isShow":true,"name":"FBM可售天数预估","hide":false},"oversea_quantity":{"id":"oversea_quantity","title":"库存","isShow":true,"name":"海外仓可用","hide":false},"local_quantity":{"id":"local_quantity","title":"库存","isShow":true,"name":"本地可用","hide":false},"purchase_num":{"id":"purchase_num","title":"库存","isShow":true,"name":"采购量","hide":false},"inventory_sales_ratio":{"id":"inventory_sales_ratio","title":"库存","isShow":false,"name":"存销比","hide":true},"month_stock_sales_ratio":{"id":"month_stock_sales_ratio","title":"库存","isShow":true,"name":"月库销比","hide":false},"out_stock_date":{"id":"out_stock_date","title":"库存","isShow":true,"name":"断货时间","hide":false},"whs_value":{"id":"whs_value","title":"库存","isShow":false,"name":"可用货值","hide":true},"sessions":{"id":"sessions","title":"流量（全部）","isShow":true,"name":"Sessions-Browser","hide":false},"browser_session_percentage":{"id":"browser_session_percentage","title":"流量（全部）","isShow":true,"name":"Sessions-Browser-Percentage","hide":false},"sessions_mobile":{"id":"sessions_mobile","title":"流量（全部）","isShow":true,"name":"Sessions-Mobile","hide":false},"mobile_app_session_percentage":{"id":"mobile_app_session_percentage","title":"流量（全部）","isShow":true,"name":"Sessions-Mobile-Percentage","hide":false},"sessions_total":{"id":"sessions_total","title":"流量（全部）","isShow":true,"name":"Sessions-Total","hide":false},"sessions_percentage":{"id":"sessions_percentage","title":"流量（全部）","isShow":true,"name":"Sessions-Percentage","hide":false},"unit_sessions_percentage":{"id":"unit_sessions_percentage","title":"流量（全部）","isShow":true,"name":"Unit-Sessions-Percentage","hide":false},"page_views":{"id":"page_views","title":"流量（全部）","isShow":true,"name":"PV-Browser","hide":false},"browser_page_views_percentage":{"id":"browser_page_views_percentage","title":"流量（全部）","isShow":true,"name":"PV-Browser-Percentage","hide":false},"page_views_mobile":{"id":"page_views_mobile","title":"流量（全部）","isShow":true,"name":"PV-Mobile","hide":false},"mobile_app_page_views_percentage":{"id":"mobile_app_page_views_percentage","title":"流量（全部）","isShow":true,"name":"PV-Mobile-Percentage","hide":false},"page_views_total":{"id":"page_views_total","title":"流量（全部）","isShow":true,"name":"PV-Total","hide":false},"page_views_percentage":{"id":"page_views_percentage","title":"流量（全部）","isShow":true,"name":"PV-Percentage","hide":false},"cvr":{"id":"cvr","title":"流量（全部）","isShow":true,"name":"CVR","hide":false},"volume_cvr":{"id":"volume_cvr","title":"流量（全部）","isShow":true,"name":"销量CVR","hide":false},"buy_box_percentage":{"id":"buy_box_percentage","title":"流量（全部）","isShow":true,"name":"Buybox赢得率","hide":false},"browser_sessions_b2b":{"id":"browser_sessions_b2b","title":"流量（B2B）","isShow":true,"name":"Sessions-Browser(B2B)","hide":false},"browser_sessions_percentage_b2b":{"id":"browser_sessions_percentage_b2b","title":"流量（B2B）","isShow":true,"name":"Sessions-Browser-Percentage(B2B)","hide":false},"mobile_app_sessions_b2b":{"id":"mobile_app_sessions_b2b","title":"流量（B2B）","isShow":true,"name":"Sessions-Mobile(B2B)","hide":false},"mobile_app_sessions_percentage_b2b":{"id":"mobile_app_sessions_percentage_b2b","title":"流量（B2B）","isShow":true,"name":"Sessions-Mobile-Percentage(B2B)","hide":false},"sessions_b2b":{"id":"sessions_b2b","title":"流量（B2B）","isShow":true,"name":"Sessions-Total(B2B)","hide":false},"sessions_percentage_b2b":{"id":"sessions_percentage_b2b","title":"流量（B2B）","isShow":true,"name":"Sessions-Percentage(B2B)","hide":false},"unit_sessions_percentage_b2b":{"id":"unit_sessions_percentage_b2b","title":"流量（B2B）","isShow":true,"name":"Unit-Sessions-Percentage(B2B)","hide":false},"browser_page_views_b2b":{"id":"browser_page_views_b2b","title":"流量（B2B）","isShow":true,"name":"PV-Browser(B2B)","hide":false},"browser_page_views_percentage_b2b":{"id":"browser_page_views_percentage_b2b","title":"流量（B2B）","isShow":true,"name":"PV-Browser-Percentage(B2B)","hide":false},"mobile_app_page_views_b2b":{"id":"mobile_app_page_views_b2b","title":"流量（B2B）","isShow":true,"name":"PV-Mobile(B2B)","hide":false},"mobile_app_views_percentage_b2b":{"id":"mobile_app_views_percentage_b2b","title":"流量（B2B）","isShow":true,"name":"PV-Mobile-Percentage(B2B)","hide":false},"page_views_b2b":{"id":"page_views_b2b","title":"流量（B2B）","isShow":true,"name":"PV-Total(B2B)","hide":false},"page_views_percentage_b2b":{"id":"page_views_percentage_b2b","title":"流量（B2B）","isShow":true,"name":"PV-Percentage(B2B)","hide":false},"cvr_b2b":{"id":"cvr_b2b","title":"流量（B2B）","isShow":true,"name":"CVR(B2B)","hide":false},"volume_cvr_b2b":{"id":"volume_cvr_b2b","title":"流量（B2B）","isShow":true,"name":"销量CVR(B2B)","hide":false},"buy_box_percentage_b2b":{"id":"buy_box_percentage_b2b","title":"流量（B2B）","isShow":true,"name":"Buybox赢得率(B2B)","hide":false},"spend":{"id":"spend","title":"广告","isShow":true,"name":"广告花费","hide":false},"ads_sp_cost":{"id":"ads_sp_cost","title":"广告","isShow":true,"name":"SP广告费","hide":false},"ads_sd_cost":{"id":"ads_sd_cost","title":"广告","isShow":true,"name":"SD广告费","hide":false},"shared_ads_sb_cost":{"id":"shared_ads_sb_cost","title":"广告","isShow":true,"name":"SB广告费","hide":false},"shared_ads_sbv_cost":{"id":"shared_ads_sbv_cost","title":"广告","isShow":true,"name":"SBV广告费","hide":false},"ad_sales_amount":{"id":"ad_sales_amount","title":"广告","isShow":true,"name":"广告销售额","hide":false},"ads_sp_sales":{"id":"ads_sp_sales","title":"广告","isShow":true,"name":"SP广告销售额","hide":false},"ads_sd_sales":{"id":"ads_sd_sales","title":"广告","isShow":true,"name":"SD广告销售额","hide":false},"shared_ads_sb_sales":{"id":"shared_ads_sb_sales","title":"广告","isShow":true,"name":"SB广告销售额","hide":false},"shared_ads_sbv_sales":{"id":"shared_ads_sbv_sales","title":"广告","isShow":true,"name":"SBV广告销售额","hide":false},"ad_order_quantity":{"id":"ad_order_quantity","title":"广告","isShow":true,"name":"广告订单量","hide":false},"ad_order_quantity_sp":{"id":"ad_order_quantity_sp","title":"广告","isShow":true,"name":"SP广告订单量","hide":false},"ad_order_quantity_sd":{"id":"ad_order_quantity_sd","title":"广告","isShow":true,"name":"SD广告订单量","hide":false},"shared_ad_order_quantity_sb":{"id":"shared_ad_order_quantity_sb","title":"广告","isShow":true,"name":"SB广告订单量","hide":false},"shared_ad_order_quantity_sbv":{"id":"shared_ad_order_quantity_sbv","title":"广告","isShow":true,"name":"SBV广告订单量","hide":false},"adv_rate":{"id":"adv_rate","title":"广告","isShow":true,"name":"广告订单量占比","hide":false},"ad_direct_sales_amount":{"id":"ad_direct_sales_amount","title":"广告","isShow":true,"name":"直接成交销售额","hide":false},"ad_direct_order_quantity":{"id":"ad_direct_order_quantity","title":"广告","isShow":true,"name":"直接成交订单量","hide":false},"impressions":{"id":"impressions","title":"广告","isShow":true,"name":"展示","hide":false},"clicks":{"id":"clicks","title":"广告","isShow":true,"name":"点击","hide":false},"ctr":{"id":"ctr","title":"广告","isShow":true,"name":"CTR","hide":false},"ad_cvr":{"id":"ad_cvr","title":"广告","isShow":true,"name":"广告CVR","hide":false},"cpc":{"id":"cpc","title":"广告","isShow":true,"name":"CPC","hide":false},"cpm":{"id":"cpm","title":"广告","isShow":true,"name":"CPM","hide":false},"roas":{"id":"roas","title":"广告","isShow":true,"name":"ROAS","hide":false},"acos":{"id":"acos","title":"广告","isShow":true,"name":"ACOS","hide":false},"acoas":{"id":"acoas","title":"广告","isShow":true,"name":"ACoAS","hide":false},"asoas":{"id":"asoas","title":"广告","isShow":true,"name":"ASoAS","hide":false},"cpo":{"id":"cpo","title":"广告","isShow":true,"name":"CPO","hide":false},"cpu":{"id":"cpu","title":"广告","isShow":true,"name":"CPU","hide":false},"nature_click":{"id":"nature_click","title":"广告","isShow":true,"name":"自然点击","hide":false},"nature_order_items":{"id":"nature_order_items","title":"广告","isShow":true,"name":"自然订单量","hide":false},"nature_cvr":{"id":"nature_cvr","title":"广告","isShow":true,"name":"自然CVR","hide":false}}',
    'key': 'product-expression-export-asin-exportMemory',
    'req_time_sequence': '/newadmin/custom/set$$2',
}

response = requests.post('https://gw.lingxingerp.com/newadmin/custom/set', headers=headers, json=json_data)

# Note: json_data will not be serialized by requests
# exactly as it was in the original request.
#data = '{"info":"{\\"asin\\":{\\"id\\":\\"asin\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"ASIN\\",\\"hide\\":false},\\"parent_asin\\":{\\"id\\":\\"parent_asin\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"父ASIN\\",\\"hide\\":false},\\"msku\\":{\\"id\\":\\"msku\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"MSKU\\",\\"hide\\":false},\\"sid\\":{\\"id\\":\\"sid\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"店铺\\",\\"hide\\":false},\\"mid\\":{\\"id\\":\\"mid\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"国家\\",\\"hide\\":false},\\"landed_price\\":{\\"id\\":\\"landed_price\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"售价（总价）\\",\\"hide\\":false},\\"item_name\\":{\\"id\\":\\"item_name\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"标题\\",\\"hide\\":false},\\"auto_tags\\":{\\"id\\":\\"auto_tags\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"自动标签\\",\\"hide\\":false},\\"principal_names\\":{\\"id\\":\\"principal_names\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"Listing负责人\\",\\"hide\\":false},\\"product_create_time\\":{\\"id\\":\\"product_create_time\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"创建时间\\",\\"hide\\":false},\\"local_name\\":{\\"id\\":\\"local_name\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"品名\\",\\"hide\\":false},\\"local_sku\\":{\\"id\\":\\"local_sku\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"SKU\\",\\"hide\\":false},\\"spu_name\\":{\\"id\\":\\"spu_name\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"款名\\",\\"hide\\":false},\\"spu\\":{\\"id\\":\\"spu\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"SPU\\",\\"hide\\":false},\\"attributes\\":{\\"id\\":\\"attributes\\",\\"title\\":\\"商品信息\\",\\"isShow\\":false,\\"name\\":\\"属性\\",\\"hide\\":true},\\"product_tags\\":{\\"id\\":\\"product_tags\\",\\"title\\":\\"商品信息\\",\\"isShow\\":false,\\"name\\":\\"产品标签\\",\\"hide\\":true},\\"model\\":{\\"id\\":\\"model\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"型号\\",\\"hide\\":false},\\"cid\\":{\\"id\\":\\"cid\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"分类\\",\\"hide\\":false},\\"bid\\":{\\"id\\":\\"bid\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"品牌\\",\\"hide\\":false},\\"developer_names\\":{\\"id\\":\\"developer_names\\",\\"title\\":\\"商品信息\\",\\"isShow\\":true,\\"name\\":\\"开发人\\",\\"hide\\":false},\\"suppliers\\":{\\"id\\":\\"suppliers\\",\\"title\\":\\"商品信息\\",\\"isShow\\":false,\\"name\\":\\"供应商\\",\\"hide\\":true},\\"cg_price\\":{\\"id\\":\\"cg_price\\",\\"title\\":\\"商品信息\\",\\"isShow\\":false,\\"name\\":\\"采购成本\\",\\"hide\\":true},\\"volume\\":{\\"id\\":\\"volume\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"销量\\",\\"hide\\":false},\\"amount\\":{\\"id\\":\\"amount\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"销售额\\",\\"hide\\":false},\\"order_items\\":{\\"id\\":\\"order_items\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"订单量\\",\\"hide\\":false},\\"volume_chain_ratio\\":{\\"id\\":\\"volume_chain_ratio\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"销量环比\\",\\"hide\\":false},\\"amount_chain_ratio\\":{\\"id\\":\\"amount_chain_ratio\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"销售额环比\\",\\"hide\\":false},\\"order_chain_ratio\\":{\\"id\\":\\"order_chain_ratio\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"订单量环比\\",\\"hide\\":false},\\"volume_yoy_ratio\\":{\\"id\\":\\"volume_yoy_ratio\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"销量同比\\",\\"hide\\":false},\\"amount_yoy_ratio\\":{\\"id\\":\\"amount_yoy_ratio\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"销售额同比\\",\\"hide\\":false},\\"order_yoy_ratio\\":{\\"id\\":\\"order_yoy_ratio\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"订单量同比\\",\\"hide\\":false},\\"net_amount\\":{\\"id\\":\\"net_amount\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"净销售额\\",\\"hide\\":false},\\"b2b_volume\\":{\\"id\\":\\"b2b_volume\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"B2B销量\\",\\"hide\\":false},\\"b2b_amount\\":{\\"id\\":\\"b2b_amount\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"B2B销售额\\",\\"hide\\":false},\\"b2b_order_items\\":{\\"id\\":\\"b2b_order_items\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"B2B订单量\\",\\"hide\\":false},\\"promotion_volume\\":{\\"id\\":\\"promotion_volume\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"促销销量\\",\\"hide\\":false},\\"promotion_amount\\":{\\"id\\":\\"promotion_amount\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"促销销售额\\",\\"hide\\":false},\\"promotion_order_items\\":{\\"id\\":\\"promotion_order_items\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"促销订单量\\",\\"hide\\":false},\\"avg_custom_price\\":{\\"id\\":\\"avg_custom_price\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"销售均价\\",\\"hide\\":false},\\"avg_volume\\":{\\"id\\":\\"avg_volume\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"平均销量\\",\\"hide\\":false},\\"promotion_discount\\":{\\"id\\":\\"promotion_discount\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"促销折扣\\",\\"hide\\":false},\\"fbm_buyer_expenses\\":{\\"id\\":\\"fbm_buyer_expenses\\",\\"title\\":\\"销售数据\\",\\"isShow\\":true,\\"name\\":\\"FBM买家运费\\",\\"hide\\":false},\\"cate_rank\\":{\\"id\\":\\"cate_rank\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"大类排名\\",\\"hide\\":false},\\"small_cate_rank\\":{\\"id\\":\\"small_cate_rank\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"小类排名\\",\\"hide\\":false},\\"return_count\\":{\\"id\\":\\"return_count\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"退款量\\",\\"hide\\":false},\\"return_amount\\":{\\"id\\":\\"return_amount\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"退款金额\\",\\"hide\\":false},\\"return_rate\\":{\\"id\\":\\"return_rate\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"退款率\\",\\"hide\\":false},\\"avg_star\\":{\\"id\\":\\"avg_star\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"评分\\",\\"hide\\":false},\\"reviews_count\\":{\\"id\\":\\"reviews_count\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"评论数\\",\\"hide\\":false},\\"comment_rate\\":{\\"id\\":\\"comment_rate\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"留评率\\",\\"hide\\":false},\\"gross_profit\\":{\\"id\\":\\"gross_profit\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"结算毛利润\\",\\"hide\\":false},\\"predict_gross_profit\\":{\\"id\\":\\"predict_gross_profit\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"订单毛利润\\",\\"hide\\":false},\\"gross_margin\\":{\\"id\\":\\"gross_margin\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"结算毛利率\\",\\"hide\\":false},\\"predict_gross_margin\\":{\\"id\\":\\"predict_gross_margin\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"订单毛利率\\",\\"hide\\":false},\\"roi\\":{\\"id\\":\\"roi\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"ROI\\",\\"hide\\":false},\\"return_goods_count\\":{\\"id\\":\\"return_goods_count\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"退货量\\",\\"hide\\":false},\\"return_goods_rate\\":{\\"id\\":\\"return_goods_rate\\",\\"title\\":\\"表现\\",\\"isShow\\":true,\\"name\\":\\"退货率\\",\\"hide\\":false},\\"avg_landed_price\\":{\\"id\\":\\"avg_landed_price\\",\\"title\\":\\"表现\\",\\"isShow\\":false,\\"name\\":\\"平均售价\\",\\"hide\\":true},\\"fbm_quantity\\":{\\"id\\":\\"fbm_quantity\\",\\"title\\":\\"库存\\",\\"isShow\\":true,\\"name\\":\\"FBM可售\\",\\"hide\\":false},\\"afn_fulfillable_quantity\\":{\\"id\\":\\"afn_fulfillable_quantity\\",\\"title\\":\\"库存\\",\\"isShow\\":true,\\"name\\":\\"FBA可售\\",\\"hide\\":false},\\"reserved_fc_transfers\\":{\\"id\\":\\"reserved_fc_transfers\\",\\"title\\":\\"库存\\",\\"name\\":\\"FBA待调仓\\",\\"isShow\\":true,\\"hide\\":false},\\"reserved_fc_processing\\":{\\"id\\":\\"reserved_fc_processing\\",\\"title\\":\\"库存\\",\\"name\\":\\"FBA调仓中\\",\\"isShow\\":true,\\"hide\\":false},\\"afn_inbound_receiving_quantity\\":{\\"id\\":\\"afn_inbound_receiving_quantity\\",\\"title\\":\\"库存\\",\\"name\\":\\"FBA入库中\\",\\"isShow\\":true,\\"hide\\":false},\\"afn_total_inbound\\":{\\"id\\":\\"afn_total_inbound\\",\\"title\\":\\"库存\\",\\"name\\":\\"FBA库存\\",\\"isShow\\":true,\\"hide\\":false},\\"reserved_customerorders\\":{\\"id\\":\\"reserved_customerorders\\",\\"title\\":\\"库存\\",\\"name\\":\\"FBA待发货\\",\\"isShow\\":true,\\"hide\\":false},\\"afn_inbound_shipped_quantity\\":{\\"id\\":\\"afn_inbound_shipped_quantity\\",\\"title\\":\\"库存\\",\\"name\\":\\"FBA在途\\",\\"isShow\\":true,\\"hide\\":false},\\"afn_inbound_working_quantity\\":{\\"id\\":\\"afn_inbound_working_quantity\\",\\"title\\":\\"库存\\",\\"name\\":\\"FBA计划入库\\",\\"isShow\\":true,\\"hide\\":false},\\"afn_unsellable_quantity\\":{\\"id\\":\\"afn_unsellable_quantity\\",\\"title\\":\\"库存\\",\\"name\\":\\"FBA不可售\\",\\"isShow\\":true,\\"hide\\":false},\\"available_inventory\\":{\\"id\\":\\"available_inventory\\",\\"title\\":\\"库存\\",\\"isShow\\":true,\\"name\\":\\"可用库存\\",\\"hide\\":false},\\"available_days\\":{\\"id\\":\\"available_days\\",\\"title\\":\\"库存\\",\\"isShow\\":true,\\"name\\":\\"FBA可售天数预估\\",\\"hide\\":false},\\"fbm_available_days\\":{\\"id\\":\\"fbm_available_days\\",\\"title\\":\\"库存\\",\\"isShow\\":true,\\"name\\":\\"FBM可售天数预估\\",\\"hide\\":false},\\"oversea_quantity\\":{\\"id\\":\\"oversea_quantity\\",\\"title\\":\\"库存\\",\\"isShow\\":true,\\"name\\":\\"海外仓可用\\",\\"hide\\":false},\\"local_quantity\\":{\\"id\\":\\"local_quantity\\",\\"title\\":\\"库存\\",\\"isShow\\":true,\\"name\\":\\"本地可用\\",\\"hide\\":false},\\"purchase_num\\":{\\"id\\":\\"purchase_num\\",\\"title\\":\\"库存\\",\\"isShow\\":true,\\"name\\":\\"采购量\\",\\"hide\\":false},\\"inventory_sales_ratio\\":{\\"id\\":\\"inventory_sales_ratio\\",\\"title\\":\\"库存\\",\\"isShow\\":false,\\"name\\":\\"存销比\\",\\"hide\\":true},\\"month_stock_sales_ratio\\":{\\"id\\":\\"month_stock_sales_ratio\\",\\"title\\":\\"库存\\",\\"isShow\\":true,\\"name\\":\\"月库销比\\",\\"hide\\":false},\\"out_stock_date\\":{\\"id\\":\\"out_stock_date\\",\\"title\\":\\"库存\\",\\"isShow\\":true,\\"name\\":\\"断货时间\\",\\"hide\\":false},\\"whs_value\\":{\\"id\\":\\"whs_value\\",\\"title\\":\\"库存\\",\\"isShow\\":false,\\"name\\":\\"可用货值\\",\\"hide\\":true},\\"sessions\\":{\\"id\\":\\"sessions\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Browser\\",\\"hide\\":false},\\"browser_session_percentage\\":{\\"id\\":\\"browser_session_percentage\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Browser-Percentage\\",\\"hide\\":false},\\"sessions_mobile\\":{\\"id\\":\\"sessions_mobile\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Mobile\\",\\"hide\\":false},\\"mobile_app_session_percentage\\":{\\"id\\":\\"mobile_app_session_percentage\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Mobile-Percentage\\",\\"hide\\":false},\\"sessions_total\\":{\\"id\\":\\"sessions_total\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Total\\",\\"hide\\":false},\\"sessions_percentage\\":{\\"id\\":\\"sessions_percentage\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Percentage\\",\\"hide\\":false},\\"unit_sessions_percentage\\":{\\"id\\":\\"unit_sessions_percentage\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"Unit-Sessions-Percentage\\",\\"hide\\":false},\\"page_views\\":{\\"id\\":\\"page_views\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"PV-Browser\\",\\"hide\\":false},\\"browser_page_views_percentage\\":{\\"id\\":\\"browser_page_views_percentage\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"PV-Browser-Percentage\\",\\"hide\\":false},\\"page_views_mobile\\":{\\"id\\":\\"page_views_mobile\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"PV-Mobile\\",\\"hide\\":false},\\"mobile_app_page_views_percentage\\":{\\"id\\":\\"mobile_app_page_views_percentage\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"PV-Mobile-Percentage\\",\\"hide\\":false},\\"page_views_total\\":{\\"id\\":\\"page_views_total\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"PV-Total\\",\\"hide\\":false},\\"page_views_percentage\\":{\\"id\\":\\"page_views_percentage\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"PV-Percentage\\",\\"hide\\":false},\\"cvr\\":{\\"id\\":\\"cvr\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"CVR\\",\\"hide\\":false},\\"volume_cvr\\":{\\"id\\":\\"volume_cvr\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"销量CVR\\",\\"hide\\":false},\\"buy_box_percentage\\":{\\"id\\":\\"buy_box_percentage\\",\\"title\\":\\"流量（全部）\\",\\"isShow\\":true,\\"name\\":\\"Buybox赢得率\\",\\"hide\\":false},\\"browser_sessions_b2b\\":{\\"id\\":\\"browser_sessions_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Browser(B2B)\\",\\"hide\\":false},\\"browser_sessions_percentage_b2b\\":{\\"id\\":\\"browser_sessions_percentage_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Browser-Percentage(B2B)\\",\\"hide\\":false},\\"mobile_app_sessions_b2b\\":{\\"id\\":\\"mobile_app_sessions_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Mobile(B2B)\\",\\"hide\\":false},\\"mobile_app_sessions_percentage_b2b\\":{\\"id\\":\\"mobile_app_sessions_percentage_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Mobile-Percentage(B2B)\\",\\"hide\\":false},\\"sessions_b2b\\":{\\"id\\":\\"sessions_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Total(B2B)\\",\\"hide\\":false},\\"sessions_percentage_b2b\\":{\\"id\\":\\"sessions_percentage_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"Sessions-Percentage(B2B)\\",\\"hide\\":false},\\"unit_sessions_percentage_b2b\\":{\\"id\\":\\"unit_sessions_percentage_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"Unit-Sessions-Percentage(B2B)\\",\\"hide\\":false},\\"browser_page_views_b2b\\":{\\"id\\":\\"browser_page_views_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"PV-Browser(B2B)\\",\\"hide\\":false},\\"browser_page_views_percentage_b2b\\":{\\"id\\":\\"browser_page_views_percentage_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"PV-Browser-Percentage(B2B)\\",\\"hide\\":false},\\"mobile_app_page_views_b2b\\":{\\"id\\":\\"mobile_app_page_views_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"PV-Mobile(B2B)\\",\\"hide\\":false},\\"mobile_app_views_percentage_b2b\\":{\\"id\\":\\"mobile_app_views_percentage_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"PV-Mobile-Percentage(B2B)\\",\\"hide\\":false},\\"page_views_b2b\\":{\\"id\\":\\"page_views_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"PV-Total(B2B)\\",\\"hide\\":false},\\"page_views_percentage_b2b\\":{\\"id\\":\\"page_views_percentage_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"PV-Percentage(B2B)\\",\\"hide\\":false},\\"cvr_b2b\\":{\\"id\\":\\"cvr_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"CVR(B2B)\\",\\"hide\\":false},\\"volume_cvr_b2b\\":{\\"id\\":\\"volume_cvr_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"销量CVR(B2B)\\",\\"hide\\":false},\\"buy_box_percentage_b2b\\":{\\"id\\":\\"buy_box_percentage_b2b\\",\\"title\\":\\"流量（B2B）\\",\\"isShow\\":true,\\"name\\":\\"Buybox赢得率(B2B)\\",\\"hide\\":false},\\"spend\\":{\\"id\\":\\"spend\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"广告花费\\",\\"hide\\":false},\\"ads_sp_cost\\":{\\"id\\":\\"ads_sp_cost\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SP广告费\\",\\"hide\\":false},\\"ads_sd_cost\\":{\\"id\\":\\"ads_sd_cost\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SD广告费\\",\\"hide\\":false},\\"shared_ads_sb_cost\\":{\\"id\\":\\"shared_ads_sb_cost\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SB广告费\\",\\"hide\\":false},\\"shared_ads_sbv_cost\\":{\\"id\\":\\"shared_ads_sbv_cost\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SBV广告费\\",\\"hide\\":false},\\"ad_sales_amount\\":{\\"id\\":\\"ad_sales_amount\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"广告销售额\\",\\"hide\\":false},\\"ads_sp_sales\\":{\\"id\\":\\"ads_sp_sales\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SP广告销售额\\",\\"hide\\":false},\\"ads_sd_sales\\":{\\"id\\":\\"ads_sd_sales\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SD广告销售额\\",\\"hide\\":false},\\"shared_ads_sb_sales\\":{\\"id\\":\\"shared_ads_sb_sales\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SB广告销售额\\",\\"hide\\":false},\\"shared_ads_sbv_sales\\":{\\"id\\":\\"shared_ads_sbv_sales\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SBV广告销售额\\",\\"hide\\":false},\\"ad_order_quantity\\":{\\"id\\":\\"ad_order_quantity\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"广告订单量\\",\\"hide\\":false},\\"ad_order_quantity_sp\\":{\\"id\\":\\"ad_order_quantity_sp\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SP广告订单量\\",\\"hide\\":false},\\"ad_order_quantity_sd\\":{\\"id\\":\\"ad_order_quantity_sd\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SD广告订单量\\",\\"hide\\":false},\\"shared_ad_order_quantity_sb\\":{\\"id\\":\\"shared_ad_order_quantity_sb\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SB广告订单量\\",\\"hide\\":false},\\"shared_ad_order_quantity_sbv\\":{\\"id\\":\\"shared_ad_order_quantity_sbv\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"SBV广告订单量\\",\\"hide\\":false},\\"adv_rate\\":{\\"id\\":\\"adv_rate\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"广告订单量占比\\",\\"hide\\":false},\\"ad_direct_sales_amount\\":{\\"id\\":\\"ad_direct_sales_amount\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"直接成交销售额\\",\\"hide\\":false},\\"ad_direct_order_quantity\\":{\\"id\\":\\"ad_direct_order_quantity\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"直接成交订单量\\",\\"hide\\":false},\\"impressions\\":{\\"id\\":\\"impressions\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"展示\\",\\"hide\\":false},\\"clicks\\":{\\"id\\":\\"clicks\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"点击\\",\\"hide\\":false},\\"ctr\\":{\\"id\\":\\"ctr\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"CTR\\",\\"hide\\":false},\\"ad_cvr\\":{\\"id\\":\\"ad_cvr\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"广告CVR\\",\\"hide\\":false},\\"cpc\\":{\\"id\\":\\"cpc\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"CPC\\",\\"hide\\":false},\\"cpm\\":{\\"id\\":\\"cpm\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"CPM\\",\\"hide\\":false},\\"roas\\":{\\"id\\":\\"roas\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"ROAS\\",\\"hide\\":false},\\"acos\\":{\\"id\\":\\"acos\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"ACOS\\",\\"hide\\":false},\\"acoas\\":{\\"id\\":\\"acoas\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"ACoAS\\",\\"hide\\":false},\\"asoas\\":{\\"id\\":\\"asoas\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"ASoAS\\",\\"hide\\":false},\\"cpo\\":{\\"id\\":\\"cpo\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"CPO\\",\\"hide\\":false},\\"cpu\\":{\\"id\\":\\"cpu\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"CPU\\",\\"hide\\":false},\\"nature_click\\":{\\"id\\":\\"nature_click\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"自然点击\\",\\"hide\\":false},\\"nature_order_items\\":{\\"id\\":\\"nature_order_items\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"自然订单量\\",\\"hide\\":false},\\"nature_cvr\\":{\\"id\\":\\"nature_cvr\\",\\"title\\":\\"广告\\",\\"isShow\\":true,\\"name\\":\\"自然CVR\\",\\"hide\\":false}}","key":"product-expression-export-asin-exportMemory","req_time_sequence":"/newadmin/custom/set$$2"}'.encode()
#response = requests.post('https://gw.lingxingerp.com/newadmin/custom/set', headers=headers, data=data)


{
    "code": 1,
    "msg": "操作成功",
    "updateCode": 0,
    "reqTimeSequence": ""
}



第二个请求网址 验证
https://gw.lingxingerp.com/bd/productPerformance/downloadValidate
请求方法
POST


import requests

headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'ak-client-type': 'web',
    'ak-origin': 'https://muke.lingxing.com',
    'auth-token': '7da2JF8GBEOI1uWc2rebQyEtbOP/EjS6pSFF3IVaaot8bcCLOVmj+kdkasOi8a3BFrd49yoE8VlSJnMZkdYjMyE+BrPqjnMy72A/KR7Qs+jyXPEW/gpru0EhrMCi7k2kPLZkDuqaASmcikGu2nRaTQ',
    'content-type': 'application/json;charset=UTF-8',
    'origin': 'https://muke.lingxing.com',
    'priority': 'u=1, i',
    'referer': 'https://muke.lingxing.com/',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'cross-site',
    'sec-fetch-storage-access': 'active',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'x-ak-company-id': '901122764666708480',
    'x-ak-env-key': 'muke',
    'x-ak-language': 'zh',
    'x-ak-platform': '1',
    'x-ak-request-id': 'ece0aeb4-6edf-4c79-8117-d61001ad35bd',
    'x-ak-request-source': 'erp',
    'x-ak-uid': '10834605',
    'x-ak-version': '*******.0.028',
    'x-ak-zid': '1',
}

json_data = {
    'sort_field': 'volume',
    'sort_type': 'desc',
    'offset': 0,
    'length': 500,
    'search_field': 'asin',
    'search_value': [],
    'mids': '',
    'sids': '',
    'date_type': 'purchase',
    'start_date': '2025-07-25',
    'end_date': '2025-08-08',
    'principal_uids': [],
    'bids': [],
    'cids': [],
    'extend_search': [],
    'summary_field': 'asin',
    'purchase_status': 0,
    'currency_code': 'CNY',
    'product_states': [],
    'is_resale': '',
    'order_types': [],
    'promotions': [],
    'developers': [],
    'delivery_methods': [],
    'is_recently_enum': True,
    'ad_cost_type': '',
    'attr_value_ids': [],
    'turn_on_summary': 0,
    'summary_field_level1': '',
    'summary_field_level2': '',
    'owner_uids': [],
    'gtag_ids': [],
    'auto_tags': [],
    'regions': [],
    'date_range_type': 0,
    'only_query_today': False,
    'query_order_profit': True,
    'total': 515920,
    'download_display': 0,
    'export_fields': 'asin,parent_asin,msku,sid,mid,landed_price,item_name,auto_tags,principal_names,product_create_time,local_name,local_sku,spu_name,spu,model,cid,bid,developer_names,volume,amount,order_items,volume_chain_ratio,amount_chain_ratio,order_chain_ratio,volume_yoy_ratio,amount_yoy_ratio,order_yoy_ratio,net_amount,b2b_volume,b2b_amount,b2b_order_items,promotion_volume,promotion_amount,promotion_order_items,avg_custom_price,avg_volume,promotion_discount,fbm_buyer_expenses,cate_rank,small_cate_rank,return_count,return_amount,return_rate,avg_star,reviews_count,comment_rate,gross_profit,predict_gross_profit,gross_margin,predict_gross_margin,roi,return_goods_count,return_goods_rate,fbm_quantity,afn_fulfillable_quantity,reserved_fc_transfers,reserved_fc_processing,afn_inbound_receiving_quantity,afn_total_inbound,reserved_customerorders,afn_inbound_shipped_quantity,afn_inbound_working_quantity,afn_unsellable_quantity,available_inventory,available_days,fbm_available_days,oversea_quantity,local_quantity,purchase_num,month_stock_sales_ratio,out_stock_date,sessions,browser_session_percentage,sessions_mobile,mobile_app_session_percentage,sessions_total,sessions_percentage,unit_sessions_percentage,page_views,browser_page_views_percentage,page_views_mobile,mobile_app_page_views_percentage,page_views_total,page_views_percentage,cvr,volume_cvr,buy_box_percentage,browser_sessions_b2b,browser_sessions_percentage_b2b,mobile_app_sessions_b2b,mobile_app_sessions_percentage_b2b,sessions_b2b,sessions_percentage_b2b,unit_sessions_percentage_b2b,browser_page_views_b2b,browser_page_views_percentage_b2b,mobile_app_page_views_b2b,mobile_app_views_percentage_b2b,page_views_b2b,page_views_percentage_b2b,cvr_b2b,volume_cvr_b2b,buy_box_percentage_b2b,spend,ads_sp_cost,ads_sd_cost,shared_ads_sb_cost,shared_ads_sbv_cost,ad_sales_amount,ads_sp_sales,ads_sd_sales,shared_ads_sb_sales,shared_ads_sbv_sales,ad_order_quantity,ad_order_quantity_sp,ad_order_quantity_sd,shared_ad_order_quantity_sb,shared_ad_order_quantity_sbv,adv_rate,ad_direct_sales_amount,ad_direct_order_quantity,impressions,clicks,ctr,ad_cvr,cpc,cpm,roas,acos,acoas,asoas,cpo,cpu,nature_click,nature_order_items,nature_cvr',
    'sub_summary_type': '',
    'req_time_sequence': '/bd/productPerformance/downloadValidate$$2',
}

response = requests.post('https://gw.lingxingerp.com/bd/productPerformance/downloadValidate', headers=headers, json=json_data)

# Note: json_data will not be serialized by requests
# exactly as it was in the original request.
#data = '{"sort_field":"volume","sort_type":"desc","offset":0,"length":500,"search_field":"asin","search_value":[],"mids":"","sids":"","date_type":"purchase","start_date":"2025-07-25","end_date":"2025-08-08","principal_uids":[],"bids":[],"cids":[],"extend_search":[],"summary_field":"asin","purchase_status":0,"currency_code":"CNY","product_states":[],"is_resale":"","order_types":[],"promotions":[],"developers":[],"delivery_methods":[],"is_recently_enum":true,"ad_cost_type":"","attr_value_ids":[],"turn_on_summary":0,"summary_field_level1":"","summary_field_level2":"","owner_uids":[],"gtag_ids":[],"auto_tags":[],"regions":[],"date_range_type":0,"only_query_today":false,"query_order_profit":true,"total":515920,"download_display":0,"export_fields":"asin,parent_asin,msku,sid,mid,landed_price,item_name,auto_tags,principal_names,product_create_time,local_name,local_sku,spu_name,spu,model,cid,bid,developer_names,volume,amount,order_items,volume_chain_ratio,amount_chain_ratio,order_chain_ratio,volume_yoy_ratio,amount_yoy_ratio,order_yoy_ratio,net_amount,b2b_volume,b2b_amount,b2b_order_items,promotion_volume,promotion_amount,promotion_order_items,avg_custom_price,avg_volume,promotion_discount,fbm_buyer_expenses,cate_rank,small_cate_rank,return_count,return_amount,return_rate,avg_star,reviews_count,comment_rate,gross_profit,predict_gross_profit,gross_margin,predict_gross_margin,roi,return_goods_count,return_goods_rate,fbm_quantity,afn_fulfillable_quantity,reserved_fc_transfers,reserved_fc_processing,afn_inbound_receiving_quantity,afn_total_inbound,reserved_customerorders,afn_inbound_shipped_quantity,afn_inbound_working_quantity,afn_unsellable_quantity,available_inventory,available_days,fbm_available_days,oversea_quantity,local_quantity,purchase_num,month_stock_sales_ratio,out_stock_date,sessions,browser_session_percentage,sessions_mobile,mobile_app_session_percentage,sessions_total,sessions_percentage,unit_sessions_percentage,page_views,browser_page_views_percentage,page_views_mobile,mobile_app_page_views_percentage,page_views_total,page_views_percentage,cvr,volume_cvr,buy_box_percentage,browser_sessions_b2b,browser_sessions_percentage_b2b,mobile_app_sessions_b2b,mobile_app_sessions_percentage_b2b,sessions_b2b,sessions_percentage_b2b,unit_sessions_percentage_b2b,browser_page_views_b2b,browser_page_views_percentage_b2b,mobile_app_page_views_b2b,mobile_app_views_percentage_b2b,page_views_b2b,page_views_percentage_b2b,cvr_b2b,volume_cvr_b2b,buy_box_percentage_b2b,spend,ads_sp_cost,ads_sd_cost,shared_ads_sb_cost,shared_ads_sbv_cost,ad_sales_amount,ads_sp_sales,ads_sd_sales,shared_ads_sb_sales,shared_ads_sbv_sales,ad_order_quantity,ad_order_quantity_sp,ad_order_quantity_sd,shared_ad_order_quantity_sb,shared_ad_order_quantity_sbv,adv_rate,ad_direct_sales_amount,ad_direct_order_quantity,impressions,clicks,ctr,ad_cvr,cpc,cpm,roas,acos,acoas,asoas,cpo,cpu,nature_click,nature_order_items,nature_cvr","sub_summary_type":"","req_time_sequence":"/bd/productPerformance/downloadValidate$$2"}'
#response = requests.post('https://gw.lingxingerp.com/bd/productPerformance/downloadValidate', headers=headers, data=data)


{
  "code": 1,
  "msg": null,
  "trace_id": "4fa3dcfff4634040bd6d36737cf7de3b.152.17546427132432307",
  "data": false
}




第三个请求网址 下载请求
https://gw.lingxingerp.com/bd/productPerformance/download
请求方法
POST

import requests

headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'ak-client-type': 'web',
    'ak-origin': 'https://muke.lingxing.com',
    'auth-token': '7da2JF8GBEOI1uWc2rebQyEtbOP/EjS6pSFF3IVaaot8bcCLOVmj+kdkasOi8a3BFrd49yoE8VlSJnMZkdYjMyE+BrPqjnMy72A/KR7Qs+jyXPEW/gpru0EhrMCi7k2kPLZkDuqaASmcikGu2nRaTQ',
    'content-type': 'application/json;charset=UTF-8',
    'origin': 'https://muke.lingxing.com',
    'priority': 'u=1, i',
    'referer': 'https://muke.lingxing.com/',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'cross-site',
    'sec-fetch-storage-access': 'active',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'x-ak-company-id': '901122764666708480',
    'x-ak-env-key': 'muke',
    'x-ak-language': 'zh',
    'x-ak-platform': '1',
    'x-ak-request-id': '80462547-2e09-4e48-88d6-754c6a6892a7',
    'x-ak-request-source': 'erp',
    'x-ak-uid': '10834605',
    'x-ak-version': '*******.0.028',
    'x-ak-zid': '1',
}

json_data = {
    'sort_field': 'volume',
    'sort_type': 'desc',
    'offset': 0,
    'length': 500,
    'search_field': 'asin',
    'search_value': [],
    'mids': '',
    'sids': '',
    'date_type': 'purchase',
    'start_date': '2025-07-25',
    'end_date': '2025-08-08',
    'principal_uids': [],
    'bids': [],
    'cids': [],
    'extend_search': [],
    'summary_field': 'asin',
    'purchase_status': 0,
    'currency_code': 'CNY',
    'product_states': [],
    'is_resale': '',
    'order_types': [],
    'promotions': [],
    'developers': [],
    'delivery_methods': [],
    'is_recently_enum': True,
    'ad_cost_type': '',
    'attr_value_ids': [],
    'turn_on_summary': 0,
    'summary_field_level1': '',
    'summary_field_level2': '',
    'owner_uids': [],
    'gtag_ids': [],
    'auto_tags': [],
    'regions': [],
    'date_range_type': 0,
    'only_query_today': False,
    'query_order_profit': True,
    'total': 515920,
    'download_display': 0,
    'export_fields': 'asin,parent_asin,msku,sid,mid,landed_price,item_name,auto_tags,principal_names,product_create_time,local_name,local_sku,spu_name,spu,model,cid,bid,developer_names,volume,amount,order_items,volume_chain_ratio,amount_chain_ratio,order_chain_ratio,volume_yoy_ratio,amount_yoy_ratio,order_yoy_ratio,net_amount,b2b_volume,b2b_amount,b2b_order_items,promotion_volume,promotion_amount,promotion_order_items,avg_custom_price,avg_volume,promotion_discount,fbm_buyer_expenses,cate_rank,small_cate_rank,return_count,return_amount,return_rate,avg_star,reviews_count,comment_rate,gross_profit,predict_gross_profit,gross_margin,predict_gross_margin,roi,return_goods_count,return_goods_rate,fbm_quantity,afn_fulfillable_quantity,reserved_fc_transfers,reserved_fc_processing,afn_inbound_receiving_quantity,afn_total_inbound,reserved_customerorders,afn_inbound_shipped_quantity,afn_inbound_working_quantity,afn_unsellable_quantity,available_inventory,available_days,fbm_available_days,oversea_quantity,local_quantity,purchase_num,month_stock_sales_ratio,out_stock_date,sessions,browser_session_percentage,sessions_mobile,mobile_app_session_percentage,sessions_total,sessions_percentage,unit_sessions_percentage,page_views,browser_page_views_percentage,page_views_mobile,mobile_app_page_views_percentage,page_views_total,page_views_percentage,cvr,volume_cvr,buy_box_percentage,browser_sessions_b2b,browser_sessions_percentage_b2b,mobile_app_sessions_b2b,mobile_app_sessions_percentage_b2b,sessions_b2b,sessions_percentage_b2b,unit_sessions_percentage_b2b,browser_page_views_b2b,browser_page_views_percentage_b2b,mobile_app_page_views_b2b,mobile_app_views_percentage_b2b,page_views_b2b,page_views_percentage_b2b,cvr_b2b,volume_cvr_b2b,buy_box_percentage_b2b,spend,ads_sp_cost,ads_sd_cost,shared_ads_sb_cost,shared_ads_sbv_cost,ad_sales_amount,ads_sp_sales,ads_sd_sales,shared_ads_sb_sales,shared_ads_sbv_sales,ad_order_quantity,ad_order_quantity_sp,ad_order_quantity_sd,shared_ad_order_quantity_sb,shared_ad_order_quantity_sbv,adv_rate,ad_direct_sales_amount,ad_direct_order_quantity,impressions,clicks,ctr,ad_cvr,cpc,cpm,roas,acos,acoas,asoas,cpo,cpu,nature_click,nature_order_items,nature_cvr',
    'sub_summary_type': '',
    'req_time_sequence': '/bd/productPerformance/download$$2',
}

response = requests.post('https://gw.lingxingerp.com/bd/productPerformance/download', headers=headers, json=json_data)

# Note: json_data will not be serialized by requests
# exactly as it was in the original request.
#data = '{"sort_field":"volume","sort_type":"desc","offset":0,"length":500,"search_field":"asin","search_value":[],"mids":"","sids":"","date_type":"purchase","start_date":"2025-07-25","end_date":"2025-08-08","principal_uids":[],"bids":[],"cids":[],"extend_search":[],"summary_field":"asin","purchase_status":0,"currency_code":"CNY","product_states":[],"is_resale":"","order_types":[],"promotions":[],"developers":[],"delivery_methods":[],"is_recently_enum":true,"ad_cost_type":"","attr_value_ids":[],"turn_on_summary":0,"summary_field_level1":"","summary_field_level2":"","owner_uids":[],"gtag_ids":[],"auto_tags":[],"regions":[],"date_range_type":0,"only_query_today":false,"query_order_profit":true,"total":515920,"download_display":0,"export_fields":"asin,parent_asin,msku,sid,mid,landed_price,item_name,auto_tags,principal_names,product_create_time,local_name,local_sku,spu_name,spu,model,cid,bid,developer_names,volume,amount,order_items,volume_chain_ratio,amount_chain_ratio,order_chain_ratio,volume_yoy_ratio,amount_yoy_ratio,order_yoy_ratio,net_amount,b2b_volume,b2b_amount,b2b_order_items,promotion_volume,promotion_amount,promotion_order_items,avg_custom_price,avg_volume,promotion_discount,fbm_buyer_expenses,cate_rank,small_cate_rank,return_count,return_amount,return_rate,avg_star,reviews_count,comment_rate,gross_profit,predict_gross_profit,gross_margin,predict_gross_margin,roi,return_goods_count,return_goods_rate,fbm_quantity,afn_fulfillable_quantity,reserved_fc_transfers,reserved_fc_processing,afn_inbound_receiving_quantity,afn_total_inbound,reserved_customerorders,afn_inbound_shipped_quantity,afn_inbound_working_quantity,afn_unsellable_quantity,available_inventory,available_days,fbm_available_days,oversea_quantity,local_quantity,purchase_num,month_stock_sales_ratio,out_stock_date,sessions,browser_session_percentage,sessions_mobile,mobile_app_session_percentage,sessions_total,sessions_percentage,unit_sessions_percentage,page_views,browser_page_views_percentage,page_views_mobile,mobile_app_page_views_percentage,page_views_total,page_views_percentage,cvr,volume_cvr,buy_box_percentage,browser_sessions_b2b,browser_sessions_percentage_b2b,mobile_app_sessions_b2b,mobile_app_sessions_percentage_b2b,sessions_b2b,sessions_percentage_b2b,unit_sessions_percentage_b2b,browser_page_views_b2b,browser_page_views_percentage_b2b,mobile_app_page_views_b2b,mobile_app_views_percentage_b2b,page_views_b2b,page_views_percentage_b2b,cvr_b2b,volume_cvr_b2b,buy_box_percentage_b2b,spend,ads_sp_cost,ads_sd_cost,shared_ads_sb_cost,shared_ads_sbv_cost,ad_sales_amount,ads_sp_sales,ads_sd_sales,shared_ads_sb_sales,shared_ads_sbv_sales,ad_order_quantity,ad_order_quantity_sp,ad_order_quantity_sd,shared_ad_order_quantity_sb,shared_ad_order_quantity_sbv,adv_rate,ad_direct_sales_amount,ad_direct_order_quantity,impressions,clicks,ctr,ad_cvr,cpc,cpm,roas,acos,acoas,asoas,cpo,cpu,nature_click,nature_order_items,nature_cvr","sub_summary_type":"","req_time_sequence":"/bd/productPerformance/download$$2"}'
#response = requests.post('https://gw.lingxingerp.com/bd/productPerformance/download', headers=headers, data=data)

{
    "code": 1,
    "msg": null,
    "trace_id": "007dfcb5dfa74269bf3e09fd40357107.256.17546427232256539",
    "data": "success"
}