import random
import time
import zipfile
from DrissionPage import Chromium, ChromiumOptions
from PIL import Image
import pytesseract
import logging
import os
import requests
from tqdm import tqdm
import socket
import concurrent.futures

# 设置日志记录
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


class DowningTongtool:
    def __init__(self):
        # 初始化浏览器页面对象
        co = ChromiumOptions().set_local_port(9222)
        co.set_browser_path("C:\Program Files\ESBrowser\ESBrowser.exe")
        self.page = Chromium(co).new_tab()
        self.proxies = None
        try:
            sock = socket.create_connection(("127.0.0.1", 10809), timeout=1)
            sock.close()
            self.proxies = {
                "http": "http://127.0.0.1:10809",
                "https": "http://127.0.0.1:10809"
            }
            logger.info("检测到本地代理 127.0.0.1:10809，使用代理下载")
        except Exception:
            logger.info("未检测到本地代理，直接下载")

    def get_response_code_with_browser(self, url):
        # 创建浏览器页面对象（可视化模式）
        try:
            logger.info("正在启动浏览器并访问页面...")
            # 访问目标 URL
            self.page.get(url)

            # 打印页面标题作为额外信息
            logger.info(f"url: {self.page.url}")
            page_url = self.page.url or ""
            if "passport.tongtool.com" in page_url:
                logger.info(f"实际访问的 URL: {self.page.url}")
                return False

            return True
        except Exception as e:
            logger.info(f"访问页面时出错: {e}")
            return None

    # def download_custom_info(self, order_number):
    #     logger.info(f"正在查询订单号: {order_number}")
    #     file_urls = []
    #     page.get(
    #         f"https://erp115.tongtool.com/search/order.htm?search_text_value={order_number}&search_mark=salesRecordNumber&source=global")

    #     for ele in page.eles("tag:a@@text()=下载定制信息文件"):
    #         file_url = ele.attr("href")
    #         logger.info(f"文件链接: {file_url}")
    #         file_urls.append(file_url)
    #     if not file_urls:
    #         logger.error("未查询到定制信息文件链接，请检查订单号是否正确！")
    #         return
    #     else:
    #         file_urls = list(set(file_urls))
    #         logger.info(f"查询到 {len(file_urls)} 个定制信息文件链接")
    #     # 去重

    #     # 挨个下载并保存

    #     save_dir = "定制信息导出"
    #     os.makedirs(save_dir, exist_ok=True)

    #     for idx, file_url in enumerate(file_urls, 1):
    #         try:
    #             logger.info(f"正在下载第{idx}个文件: {file_url}")
    #             response = requests.get(file_url, stream=True)
    #             response.raise_for_status()
    #             # 从 Content-Disposition 获取文件名
    #             content_disposition = response.headers.get(
    #                 'Content-Disposition')
    #             if content_disposition and 'filename=' in content_disposition:
    #                 filename = content_disposition.split('filename=')[
    #                     1].strip('"; ')
    #             else:
    #                 # 如果没有提供文件名，则使用默认命名
    #                 filename = f"{order_number}_{idx}.zip"
    #             # filename = f"{order_number}.zip"
    #             filepath = os.path.join(save_dir, filename)
    #             with open(filepath, "wb") as f:
    #                 for chunk in response.iter_content(chunk_size=8192):
    #                     if chunk:
    #                         f.write(chunk)
    #             logger.info(f"文件下载成功: {filename}")
    #             logger.info(f"文件已保存为: {filepath}")

    #             # 解压文件到指定文件夹（每个压缩包单独一个子目录）
    #             try:
    #                 extract_dir = os.path.join(
    #                     save_dir, os.path.splitext(filename)[0])
    #                 os.makedirs(extract_dir, exist_ok=True)
    #                 with zipfile.ZipFile(filepath, 'r') as zip_ref:
    #                     zip_ref.extractall(extract_dir)
    #                 logger.info(f"文件已成功解压到文件夹 {extract_dir}")
    #                 os.remove(filepath)  # 删除原始压缩包
    #                 logger.info(f"原始压缩包 {filename} 已删除")
    #             except Exception as e:
    #                 logger.error(f"解压文件失败: {e}")
    #                 continue

    #             # 检查子目录下是否还有压缩包，递归解压
    #             for root, _, files in os.walk(extract_dir):
    #                 for file in files:
    #                     if file.endswith(".zip"):
    #                         zip_path = os.path.join(root, file)
    #                         try:
    #                             sub_extract_dir = os.path.join(
    #                                 root, os.path.splitext(file)[0])
    #                             os.makedirs(sub_extract_dir, exist_ok=True)
    #                             with zipfile.ZipFile(zip_path, 'r') as zip_ref:
    #                                 zip_ref.extractall(sub_extract_dir)
    #                             logger.info(
    #                                 f"文件 {zip_path} 已成功解压到 {sub_extract_dir}")
    #                             os.remove(zip_path)
    #                             logger.info(f"压缩包 {zip_path} 已删除")
    #                         except Exception as e:
    #                             logger.error(f"解压文件 {zip_path} 失败: {e}")

    #         except Exception as e:
    #             logger.error(f"下载文件失败: {file_url}，错误信息: {e}")
    def download_and_extract(self, args):
        order_number, file_url = args
        save_dir = f"定制信息导出/{order_number}"
        os.makedirs(save_dir, exist_ok=True)
        max_retry = 5
        try_num = 1
        last_error = ''
        while try_num <= max_retry:
            try:
                logger.info(f"正在下载文件: {file_url}（第{try_num}次尝试）")
                headers = {'User-Agent': random.choice([
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 14_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.80 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
                    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.60 Safari/537.36",
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
                    "Mozilla/5.0 (iPad; CPU OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
                ])}
                # 智能判断是否加了代理（通过能否连通127.0.0.1:10809判断）

                response = requests.get(file_url, stream=True, headers=headers, proxies=self.proxies, timeout=60)
                if response.status_code != 200:
                    last_error = f"HTTP状态码: {response.status_code}, 响应内容: {response.text[:200]}"
                    raise Exception(f"请求失败，状态码: {response.status_code}")
                content_disposition = response.headers.get('Content-Disposition')
                if content_disposition and 'filename=' in content_disposition:
                    filename = content_disposition.split('filename=')[1].strip('"; ')
                else:
                    filename = f"{order_number}_{int(time.time())}.zip"
                filepath = os.path.join(save_dir, filename)

                total = int(response.headers.get('content-length', 0))
                with open(filepath, "wb") as f, tqdm(
                        desc=filename,
                        total=total,
                        unit='B',
                        unit_scale=True,
                        unit_divisor=1024,
                        leave=False
                ) as bar:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            bar.update(len(chunk))
                logger.info(f"文件下载成功: {filename}")
                logger.info(f"文件已保存为: {filepath}")

                # 解压文件到指定文件夹（每个压缩包单独一个子目录）
                try:
                    extract_dir = os.path.join(save_dir, os.path.splitext(filename)[0])
                    os.makedirs(extract_dir, exist_ok=True)
                    with zipfile.ZipFile(filepath, 'r') as zip_ref:
                        zip_ref.extractall(extract_dir)
                    logger.info(f"文件已成功解压到文件夹 {extract_dir}")
                    os.remove(filepath)
                    logger.info(f"原始压缩包 {filename} 已删除")
                except Exception as e:
                    last_error = f"解压文件失败: {e}"
                    logger.error(last_error)
                    raise

                # 检查子目录下是否还有压缩包，递归解压
                for root, _, files in os.walk(extract_dir):
                    for file in files:
                        if file.endswith(".zip"):
                            zip_path = os.path.join(root, file)
                            try:
                                sub_extract_dir = os.path.join(root, os.path.splitext(file)[0])
                                os.makedirs(sub_extract_dir, exist_ok=True)
                                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                                    zip_ref.extractall(sub_extract_dir)
                                logger.info(f"文件 {zip_path} 已成功解压到 {sub_extract_dir}")
                                os.remove(zip_path)
                                logger.info(f"压缩包 {zip_path} 已删除")
                            except Exception as e:
                                logger.error(f"解压文件 {zip_path} 失败: {e}")
                break  # 下载和解压都成功，退出重试循环

            except requests.Timeout as e:
                last_error = f"请求超时: {e}"
                logger.error(last_error)
            except Exception as e:
                if not last_error:
                    last_error = f"下载或解压异常: {e}"
                logger.error(f"下载文件失败: {file_url}，错误信息: {e}")
            try_num += 1
            if try_num > max_retry:
                logger.error(f"重试次数已达上限（{max_retry}次），放弃下载: {file_url}")
                # 记录到失败文档
                fail_line = f"订单号: {order_number}\tURL: {file_url}\t失败原因: {last_error}\n"
                with open("download_failed.txt", "a", encoding="utf-8") as f:
                    f.write(fail_line)
                break
            sleep_time = random.randint(2, 5)
            logger.info(f"等待{sleep_time}秒后重试...")
            time.sleep(sleep_time)

    def check_login_status(self):
        self.page.get(
            "https://erp115.tongtool.com/dashboard/homepage/index.htm")
        time.sleep(2)  # 等待页面加载完成
        # 检查是否登录成功
        page_url = self.page.url or ""
        if "passport.tongtool.com" in page_url:
            logger.info("未登录，正在尝试登录...")
            return False
        else:
            logger.info("已登录")
            return True

    def run(self, orders=[], tab_count=6):
        import queue
        if not self.check_login_status():
            logger.error("未登录，请在手动在已打开的浏览器中登录通途")
            return False
        logger.info("已登录，无需重复登录")
        if not orders:
            logger.error("未提供订单号，请提供要查询的订单号列表")
            return False

        browser = self.page.browser
        tabs = [self.page] + [browser.new_tab() for _ in range(tab_count - 1)]

        order_queue = queue.Queue()
        for order in orders:
            order_queue.put(order)

        def fetch_links(tab, order_queue, max_retry=3):
            batch = []
            while True:
                try:
                    order_number = order_queue.get_nowait()
                except queue.Empty:
                    break
                retry = 0
                while retry < max_retry:
                    try:
                        logger.info(f"[Tab] 正在查询订单号: {order_number}，第{retry + 1}次尝试")
                        tab.get(
                            f"https://erp115.tongtool.com/search/order.htm?search_text_value={order_number}&search_mark=salesRecordNumber&source=global")
                        tab.wait.load_start()
                        # 判断 SRN/Items/货品 标签后面的 a 标签的 title
                        try:
                            srn_label = tab.ele('@text()=SRN/Items/货品')
                            if srn_label:
                                a_ele = srn_label.next('tag:a')
                                if a_ele:
                                    title = a_ele.attr('title')
                                    logger.info(f'SRN/Items/货品 后面的 a 标签 title: {title}')
                                    if title == '收缩':
                                        logger.info('当前为展开状态')
                                    elif title == '展开':
                                        logger.info('当前为收缩状态')
                                        srn_label.click()
                                        time.sleep(1)
                                    else:
                                        logger.info('未检测到收缩/展开 title')
                        except Exception as e:
                            logger.error(f'[Tab] 判断 SRN/Items/货品 展开收缩状态时出错: {e}')
                        urls = []
                        for ele in tab.eles("tag:a@@text()=下载定制信息文件"):
                            file_url = ele.attr("href")
                            logger.info(f"[Tab] 文件链接: {file_url}")
                            urls.append((order_number, file_url))
                        if not urls:
                            logger.error(f"[Tab] 订单号 {order_number} 未查询到定制信息文件链接，请检查订单号是否正确！")
                        else:
                            urls = list(set(urls))
                            logger.info(f"[Tab] 订单号 {order_number} 查询到 {len(urls)} 个定制信息文件链接")
                        time.sleep(random.uniform(0.5, 1.2))
                        batch.extend(urls)
                        break  # 成功就退出重试循环
                    except Exception as e:
                        retry += 1
                        logger.warning(f"[Tab] 订单号 {order_number} 抓取异常，第{retry}次重试，错误: {e}")
                        time.sleep(1 + retry)
                else:
                    logger.error(f"[Tab] 订单号 {order_number} 多次重试失败，已跳过")
                order_queue.task_done()
            return batch

        all_urls = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=tab_count) as executor:
            futures = [executor.submit(fetch_links, tab, order_queue, 3) for tab in tabs]
            for f in concurrent.futures.as_completed(futures):
                all_urls.extend(f.result())

        logger.info(f"共收集到 {len(all_urls)} 个下载链接，开始下载...")

        # 5. 批量下载（复用原有逻辑）
        batch = []
        for item in all_urls:
            batch.append(item)
            if len(batch) >= 9:
                with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                    executor.map(self.download_and_extract, batch)
                logger.info("本批次定制信息文件下载完成")
                batch = []
        if batch:
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                executor.map(self.download_and_extract, batch)
            logger.info("最后一批定制信息文件下载完成")

        # 6. 关闭多余 tab
        for tab in tabs[1:]:
            tab.close()

        return True
