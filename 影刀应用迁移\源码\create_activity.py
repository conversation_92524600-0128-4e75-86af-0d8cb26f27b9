import os
import json
import random
import string
import time
import zipfile
import os.path
import uuid

import requests

class CreateApp():
    def __init__(self):
        self.token = ""
        self.app_id = ""
        self.app_name = ""
        self.app_md5 = ""
    #1 创建app
    def create_app(self):

        url = 'https://api.winrobot360.com/api/client/app/develop/suggestAppName?appType=app&suggestType=create'
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********'
        }

        response = requests.get(url, headers=headers)
        print(response.text)

    #2 返回appid
    def getappid(self):
        url = 'https://api.winrobot360.com/api/noauth/v1/sys/tag/queryTagList?type=robot&clientCode=rpa-win&tagClass=user'
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********'
        }

        response = requests.get(url, headers=headers)
        return response.json()
    def genuuid(self):
        # 生成一个 UUID
        new_uuid = uuid.uuid4()

        # 将 UUID 转换为字符串，并打印出来

        return  str(new_uuid)    #3 获取阿里云bot文件图床链接
    def getroboturl(self,uuid):
        url = 'https://api.winrobot360.com/api/client/app/file/assignUploadUrl'
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization':self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********',
            'Content-Type': 'application/json; charset=utf-8'
        }
        data = '{"appId":"%s","appType":"activity","version":"","isBot":true}' % (uuid)
        print(data)
        response = requests.post(url, headers=headers, data=data)
        print(response.text)
        return response.json()

    # 3 获取阿里云json配置文件图床链接
    def getjsonurl(self,uuid):
        url = 'https://api.winrobot360.com/api/client/app/file/assignUploadUrl'
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********',
            'Content-Type': 'application/json; charset=utf-8'
        }
        data = '{"appId":"%s","appType":"activity","version":"","isBot":false}'% (uuid)

        response = requests.post(url, headers=headers, data=data)
        print(response.text)
        return response.json()
    #上传阿里云文件
    def upload_json_file(self,config,filepath):
        with open(filepath, "r",  encoding='utf-8') as file:
            package_info_after_write = json.load(file)

            new_uuid = package_info_after_write.get("uuid")
            print(new_uuid)

        # 上传文件
        with open(filepath, 'rb') as f:
            print(f)
            response = requests.put(config['uploadUrl'], data=f)
        print(response.status_code)
        if response.status_code == 200:
            print("上传成功")

    def upload_bot_file(self,url,file):
        # 注意：这里的 headers 需要根据具体情况设置，可能需要包含你的授权信息等
        headers = {
            'Content-Type': 'application/octet-stream',
        }

        # 以二进制模式打开要上传的文件
        with open(file, "rb") as f:
            response = requests.put(url,data=f)
            if response.status_code == 200:
                print("文件上传成功")
            else:
                print("Error uploading file:", response.text)

    def checkappid(self,appid):
        url = 'https://api.winrobot360.com/api/client/run/runningDetailByAppId'
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********',
            'Content-Type': 'application/json; charset=utf-8'
        }
        data = '{"appId":"%s","env":"dev"}' %(appid,)

        response = requests.post(url, headers=headers, data=data)
        print(response.text)
        return response.json()
    def getappdetail(self,appid):
        url = 'https://api.winrobot360.com/api/client/app/develop/app/detail'
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********'
        }
        params = {
            'appId': f'{appid}',
            'checkAppRecycle': 'True'
        }

        response = requests.get(url, headers=headers, params=params)
        print(response.text)

    def create_new_app(self,packageCode):
        print("创建uuid",self.app_id)
        url = "https://api.winrobot360.com/api/client/app/develop/create"
        payload = {
            "appId": "b46bfc72-08fb-435f-9943-b2496329685e",
            "packageMd5": "5cfeb4aac778888b4e622ea58a645d34",
            "groupId": None,
            "appPackage": {
                "name": "请34饿423423",
                "description": "223423",
                "instruction": "",
                "videoUrl": "",
                "appIcon": "",
                "enableViewSource": False,
                "uiaType": "pc",
                "appType": "activity",
                "packageCode": "activity_c40ceb1c",
                "uiTags": None,
                "statistics": {
                    "blockCount": 1,
                    "flowCount": 1,
                    "sourceLineCount": 0,
                    "magicBlockCount": 0
                },
                "externalDependencies": [],
                "internalDependencies": [],
                "activities": [
                    {
                        "name": "223423",
                        "description": "223423",
                        "helpLink": None
                    }
                ],
                "elementLibraryCodes": [],
                "customItems": {
                    "uiaType": "pc",
                    "videoUrl": "",
                    "gifUrl": "",
                    "imageUrl": "",
                    "imageName": ""
                },
                "appFlowParamList": []
            },
            "elementLibraryStatus": 0
        }
        # 将数据填入字符串
        payload["appId"] = self.app_id
        payload["packageMd5"] = self.app_md5
        payload["appPackage"]["name"] = self.app_name
        payload["appPackage"]["packageCode"] = packageCode
        payload["appPackage"]["activities"][0]["name"] = self.app_name
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': f'{self.token}',
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********',
            'Content-Type': 'application/json; charset=utf-8',
            'Connection': 'keep-alive'
        }

        print(payload)
        # 将payload转换为JSON格式字符串
        payload_json = json.dumps(payload)

        response = requests.post(url, headers=headers, data=payload_json)



        print(response.text)
    def get_app_detail(self):
        url = f'https://api.winrobot360.com/api/client/app/develop/app/detail?appId={self.app_id}&checkAppRecycle=True'
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********'
        }

        response = requests.get(url, headers=headers)
        print(response.text)

    def generate_packagecode(self):
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********',
        }

        response = requests.get('https://api.winrobot360.com/api/client/app/develop/generate/packageCode',
                                headers=headers)
        print(response.json())
        return response.json()
    def sumbit_str(self,ran_str,appid):
        url = 'https://api.winrobot360.com/api/client/app/package/rename/check'
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********',
            'Content-Type': 'application/json; charset=utf-8'
        }
        params = {
            'newPackageCode': ran_str,
            'appId': appid
        }

        response = requests.get(url, headers=headers, params=params)
        print(response.json())
        return response.json()

    def activity_create(self):
        url = "https://api.winrobot360.com/api/client/app/develop/suggestAppName?appType=activity&suggestType=create"

        payload = {}
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********',
            'Connection': 'keep-alive'
        }

        response = requests.request("GET", url, headers=headers, data=payload)
        print("获取name",response.text)

    def update_app(self):
        url = 'https://api.winrobot360.com/api/client/app/develop/update'
        headers = {
            'Host': 'api.winrobot360.com',
            'timezone': '%2b08%3a00',
            'ipv4': '*************',
            'Accept-Language': 'zh-CN,zh;q=1',
            'Authorization': self.token,
            'Accept': 'application/json, text/json, text/x-json, text/javascript, application/xml, text/xml',
            'User-Agent': 'RestSharp/*********',
            'Content-Type': 'application/json; charset=utf-8'
        }
        data = '{"appId":"ed9c2876-7be7-4f5f-8eb8-7ff3dda6eaab","packageMd5":"4bd02f2673292539f4460dccccef2544","appPackage":{"name":"新建应用123423236","description":"","instruction":"","videoUrl":"","appIcon":"","enableViewSource":false,"uiaType":"pc","appType":"app","packageCode":"","uiTags":null,"statistics":{"blockCount":0,"flowCount":1,"sourceLineCount":0,"magicBlockCount":0},"externalDependencies":[],"internalDependencies":[],"activities":[],"elementLibraryCodes":[],"customItems":{"uiaType":"pc","videoUrl":"","gifUrl":null,"imageUrl":"","imageName":""},"appFlowParamList":[]},"elementLibraryStatus":0}'

        response = requests.post(url, headers=headers, data=data)
        print(response.text)

task = CreateApp()
base_folder = r"C:\Users\<USER>\AppData\Local\ShadowBot\users\704619633170276354\新建文件夹"
task.token = "bearer 90246f23-7bca-40e0-bdc3-22c58ad4595b"

for root, dirs, files in os.walk(base_folder):
    for dir in dirs:
        if dir == "xbot_robot":
            base_config_folder = os.path.join(root, dir)
            # 在这里可以调用你需要的操作，比如对 xbot_robot_folder 执行一些操作
            print("Found xbot_robot folder:", base_config_folder)
            # task.create_app()
            # base_config_folder = r"F:\桌面\影刀迁移器\迁移应用文件夹\0ec06020-3109-4052-a794-792f918ec35f\xbot_robot"
            config_json = os.path.join(base_config_folder,"package.json")

            with open(config_json, "r", encoding="utf-8") as file:
                package_info = json.load(file)

                task.app_name  = package_info.get("name")
                robot_type = package_info.get("robot_type")
                if robot_type != "activity":
                    print("不是指令，跳过咯")
                    continue
                else:
                    task_uuid = task.genuuid()

                    new_package_code_task = task.generate_packagecode()
                    if new_package_code_task["code"] == 200:
                        new_package_code = new_package_code_task["data"]
                        activity_create = task.activity_create()
                        sumbit_str = task.sumbit_str(new_package_code, appid=task_uuid)
                        if sumbit_str["code"] != 200:
                            print(f"{new_package_code}-检查不通过")
                            continue
                        checkappid = task.checkappid(task_uuid)
                        if  checkappid["success"]:
                            print(f"{checkappid}-检查不通过")
                            continue
                        if "新建应用" in task.app_name:
                            continue
                        quuid = package_info.get("uuid")
                        qactivity_code = package_info.get("activity_code")
                        print("原始uuid:",quuid)
                        print("原始activity_code:", qactivity_code)
                        package_info["uuid"] = task_uuid
                        package_info["activity_code"] = new_package_code
                        print("name", task.app_name, "uuid", task_uuid)
                        with open(config_json, "w", encoding="utf-8") as file:
                            json.dump(package_info, file, indent=4,ensure_ascii=False)
                        with open(config_json, "r", encoding="utf-8") as file:
                            package_info_after_write = json.load(file)

                        new_uuid = package_info_after_write.get("uuid")
                        print("写入后的uuid:", new_uuid)
                        config_bot = os.path.join(base_config_folder, "package.bot")
                        if os.path.exists(config_bot):
                            os.remove(config_bot)
                            print("删除文件")
                        skip_compress_ext = [".zip", ".7z", ".rar"]  # 添加其他需要跳过的压缩文件扩展名
                        with zipfile.ZipFile(config_bot, "w") as zipf:
                            for foldername, _, filenames in os.walk(base_config_folder):
                                for filename in filenames:
                                    filepath = os.path.join(foldername, filename)
                                    if "__pycache__" in filepath:
                                        continue
                                    ext = os.path.splitext(filename)[1]  # 获取文件扩展名
                                    if ext.lower() in skip_compress_ext:
                                        print(f"Skipping compression for {filename}")
                                    else:
                                        arcname = os.path.relpath(filepath, base_config_folder)
                                        if "package.bot" in arcname:
                                            continue
                                        print("添加文件到压缩包", arcname)
                                        zipf.write(filepath, arcname)

                        # uuid = task.getappid()["data"][0]["uuid"]
                        # print(uuid)

                        task.app_id = task_uuid
                        getjsonurl = task.getjsonurl(task_uuid)
                        print(getjsonurl)
                        getboturl = task.getroboturl(task_uuid)

                        uploadfile = task.upload_json_file(getjsonurl["data"],config_json)
                        uploadfile2 = task.upload_bot_file(getboturl["data"]["uploadUrl"],config_bot)
                        task.app_md5 = getjsonurl["data"]["fileKeyMd5"]
                        print(task.app_md5)
                        create_new_app = task.create_new_app(packageCode=new_package_code)
                        getdetail = task.getappdetail(task_uuid)
                        random_time = random.randint(15, 25)
                        time.sleep(random_time)
