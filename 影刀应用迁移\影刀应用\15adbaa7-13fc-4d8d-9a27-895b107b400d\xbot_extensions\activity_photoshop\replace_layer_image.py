# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
from xbot import print, sleep
from .import package
from .package import variables as glv


import xbot
from xbot import print, sleep
from .import package
from .package import variables as glv

import xbot
from xbot import print, sleep
from .import package
from .package import variables as glv


from win32com.client import Dispatch, GetActiveObject, GetObject
import textwrap
import win32com.client
from win32com.client import Dispatch


def move(app, horizen, vertical):
    dialogMode = 3
    idmove = app.CharIDToTypeID("move")
    desc257 = win32com.client.Dispatch("Photoshop.ActionDescriptor")
    idnull = app.CharIDToTypeID("null")
    ref1 = win32com.client.Dispatch("Photoshop.ActionReference")
    idLyr = app.CharIDToTypeID("Lyr ")
    idOrdn = app.CharIDToTypeID("Ordn")
    idTrgt = app.CharIDToTypeID("Trgt")
    ref1.PutEnumerated(idLyr, idOrdn, idTrgt)
    desc257.PutReference(idnull, ref1)
    idT = app.CharIDToTypeID("T   ")
    desc258 = win32com.client.Dispatch("Photoshop.ActionDescriptor")
    idHrzn = app.CharIDToTypeID("Hrzn")
    idPxl = app.CharIDToTypeID("#Pxl")
    desc258.PutUnitDouble(idHrzn, idPxl, horizen)
    idVrtc = app.CharIDToTypeID("Vrtc")
    idPxl = app.CharIDToTypeID("#Pxl")
    desc258.PutUnitDouble(idVrtc, idPxl, vertical)
    idOfst = app.CharIDToTypeID("Ofst")
    desc257.PutObject(idT, idOfst, desc258)
    res = app.ExecuteAction(idmove, desc257, dialogMode)

def placed_layer_replace_contents(app, filename):
    """
    当前选择的替换智能对象内容为filename
    * @param filename 对象内容
    """
    desc = Dispatch("Photoshop.ActionDescriptor")
    desc.PutPath(app.CharIDToTypeID("null"), filename)
    app.ExecuteAction(app.StringIDToTypeID("placedLayerReplaceContents"), desc,
                      3)

def placed_layer_reset_transforms(app):
    desc = Dispatch("Photoshop.ActionDescriptor")
    idplacedLayerResetTransforms = app.StringIDToTypeID(
        "placedLayerResetTransforms")
    app.ExecuteAction(idplacedLayerResetTransforms, desc, 3)


def get_layer_by_name(photoshop_instance, name_of_layer):
    return photoshop_instance.layer(name_of_layer, only_visible=False)

    
def replace_image(ps_instance, layer_name, image_path):
    """
    ps_instance, ps 对象
    layer_name, 图层名称
    image_path, 图像地址
    height, 根据高度进行缩放大小
    """
    
    # active_layer = ps_instance.app.ActiveDocument.ArtLayers(layer_name)
    # active_layer = ps_instance.app.ActiveDocument.LayerSets("组名").LayerSets("组名").ArtLayers("图层名")

    if ">" not in layer_name:
        try:
            active_layer = ps_instance.app.ActiveDocument.ArtLayers(layer_name)
        except:
            active_layer = get_layer_by_name(ps_instance, layer_name)
    else:
        *layersets, layer_name = [path.strip() for path in layer_name.split(">")]

        current_layerset = ps_instance.doc
        for layerset_name in layersets:
            current_layerset = current_layerset.LayerSets(layerset_name)

        active_layer = current_layerset.Layers(layer_name)
    # 
    ps_instance.doc.ActiveLayer = active_layer
    bounds = active_layer.bounds
    # print(f"current layer {active_layer.name}: {bounds}")
    
    center_x = (bounds[2] + bounds[0]) / 2
    center_y = (bounds[3] + bounds[1]) / 2

    width = bounds[2] - bounds[0]
    height = bounds[3] - bounds[1]

    placed_layer_replace_contents(ps_instance.app, image_path)

    active_layer.Name = layer_name
    # placed_layer_reset_transforms(ps_instance.app)

    new_bounds = active_layer.bounds

    new_center_x = (new_bounds[2] + new_bounds[0]) / 2
    new_center_y = (new_bounds[3] + new_bounds[1]) / 2

    x_offset = center_x - new_center_x
    y_offset = center_y - new_center_y

    # move(ps_instance.app, x_offset, y_offset)

    # print(current_bounds)

    new_width = new_bounds[2] - new_bounds[0]
    new_height = new_bounds[3] - new_bounds[1]

    # 调整图片大小的逻辑

    if new_height > new_width:
        new_size = height / new_height * 100
        active_layer = ps_instance.layer(layer_name)
        ps_instance.doc.ActiveLayer = active_layer
        active_layer.Resize(new_size, new_size, 5)
    else:
        new_size = width / new_width * 100
        active_layer = ps_instance.layer(layer_name)
        ps_instance.doc.ActiveLayer = active_layer
        active_layer.Resize(new_size, new_size, 5)

# def test_change_content_of_layer_by_name_from_file():
#     path = r"C:\Users\<USER>\Desktop\勿动！影刀RPA所有文件 2024.11.19\丽得姿美蒂优氨基酸保湿面膜(艾斯)10P.psd"
#     replace_path = r"C:\Users\<USER>\Desktop\勿动！影刀RPA所有文件 2024.11.19\已下载图片\壹年5季0脂肪蒸煮西梅168g.png"
#     ps_instance = ps_open(path)

#     replace_image(ps_instance, "商品主图", replace_path)

def main(args):
    ps_instance = args.get("ps_instance")
    layer_name = args.get("layer_name")
    image_path = args.get("image_path")


    replace_image(ps_instance, layer_name, image_path)

    # test_change_content_of_layer_by_name_from_file()
    # Dispatch("Photoshop.ActionDescriptor")
    # win32com.client.GetActiveObject("Photoshop.Application")
    pass
