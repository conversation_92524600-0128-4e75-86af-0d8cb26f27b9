import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    photoshop_instance = ""
    if args is None:
        ps_file = ""
    else:
        ps_file = args.get("ps_file", "")
    try:
        photoshop_instance = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="open", params={
            "ps_file": ps_file,
        }, _block=("打开 PSD 文件", 1, "调用模块"))
    finally:
        args["photoshop_instance"] = photoshop_instance
