import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        ps_instance = None
    else:
        ps_instance = args.get("ps_instance", None)
    try:
        invoke_result = xbot_visual.process.invoke_module(module="invoke_module", package=__name__, function="active_document", params={
            "ps_instance": ps_instance,
        }, _block=("激活文档", 1, "调用模块"))
    finally:
        pass
