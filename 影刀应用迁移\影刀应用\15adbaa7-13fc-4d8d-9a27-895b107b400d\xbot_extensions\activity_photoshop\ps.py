import xbot
from xbot import print, sleep
from .import package
from .resources.photoshop_api import Photoshop
# from .photoshop_api  import Photoshop
from . import pil_api
import uuid
import os
import datetime

def open(ps_file):
    if not os.path.exists(ps_file):
        raise ValueError(f'PS模板文件 {ps_file}不存在')
    #生成备份文件，打开备份文件，以防运行过程中，破坏图层
    
    photoshop_instance = Photoshop(ps_file)
    return photoshop_instance

def check_dataset(photoshop_instance, dataset_titles):
    '''
    校验数据集:图层必须存在，且只能有一个
    '''
    layers = photoshop_instance.all_layers(only_visible=True)
    #存储非唯一的
    no_layer_items = []
    multiply_layer_items = []
    for title in dataset_titles:
        if title is not None and title != '':
            count = _layer_count(layers, title)
            if count == 0:
                no_layer_items.append(title)
            elif count > 1:
                multiply_layer_items.append(title)
    
    error_msg = '' 
    if len(no_layer_items) > 0:
        names = ", ".join(no_layer_items)
        error_msg += f"未找到图层【{names}】，"
    if len(multiply_layer_items) > 0:
        names = ", ".join(multiply_layer_items)
        error_msg += f"图层名【{names}】存在多个，"
    
    if error_msg != '':
        raise ValueError(error_msg+"请检查PS模板文件")

def _layer_count(layers, name) -> int:
    i = 0
    for layer in layers:
        if layer.Name == name:
            i += 1
        if i > 1:
            break
    return i

def _find_layer_by_name(layers, name):
    for layer in layers:
        if layer.Name == name:
            return layer
    raise ValueError(f"未找到名称为【{name}】的图层，请检查PS文件！")

def _is_image(name):
    '''
    给定一个值，判断是否为图片
    '''
    return os.path.basename(name) != name

def make_image(photoshop_instance, title_list, data_list, first_column_is_image_name, save_path, save_format, save_psd):
    '''
    生成图片
    * @param photoshop_instance, ps文件对象
    * @param title_list
    * @param data_list
    * @pram first_column_is_image_name, 第一列是否为图片名称
    * @param save_path, 图片保存路径
    * @param save_format, 图片保存格式
    * @param save_psd, 是否保留psd文件
    '''
    layers = photoshop_instance.all_layers(only_visible=True)
    filename = None
    if first_column_is_image_name:
        filename = data_list[0]
        data_list = data_list[1:]

    #逐一处理data中的数据
    for i in range(len(title_list)):
        title = title_list[i]
        data = data_list[i] if i < len(data_list) else None
        #find layer
        layer = _find_layer_by_name(layers, title)
        #check 文字 or 图片，只要是文件就是图片
        is_image = _is_image(data)
        if is_image:
            if not os.path.exists(data):
                raise ValueError(f"图片【{data}】不存在，请检查！") 
            #暂时只支持 智能对象与psNormalLayer，其它类型暂时不懂
            if layer.Kind not in(1,17):
                raise ValueError(f"暂不支持图层【{title}】替换图片，请检查图层类型！")
            
            (layer_width, layer_height) = photoshop_instance.size(layer)
            if layer.Kind == 1:
                #1-替换成智能对象(测试三次) 测试时发现这里经常已经成功了，还是会报异常
                for i in range(3):
                    try:
                        photoshop_instance.to_smart_object(layer)
                        break
                    except:
                        pass
                    raise ValueError(f"图层【{title}】转成智能对象失败，请尝试手工操作")

            success_replace = False        
            try:
                #2-替换内容
                photoshop_instance.active_layer(layer)
                photoshop_instance.replace_smart_object_content(data)
                #修改图像大小
                # pil = pil_api.PIL_API(file_name=data)
                # (width, height) = pil.size()
                # if abs(width - layer_width) > 1 or abs(height - layer_height) > 1:
                #     #72不缩放
                #     photoshop_instance.resize_layer_image(layer_width, layer_height, dpi=72)
                #end 修改图像大小
                success_replace = True
                if layer.Kind == 1:
                    #3-栅格化
                    try:
                        #删格化失败了会怎么样？
                        photoshop_instance.rasterize_layer()
                    except:
                        pass
            finally:
                #4-改回图层名
                #diff出新图层,因为转成智能对象本质上是新产生了一个图层，替换了原图层
                if success_replace:
                    newest_layers = photoshop_instance.all_layers(only_visible=True)
                    #不能直接转化成set再用diff
                    newers = [item for item in newest_layers if item not in layers]
                    if len(newers) == 1:
                        newers[0].Name = title
                        #replace layer using newest_layers
                        layers = newest_layers
                    elif len(newers)  > 1:
                        #从目前来看，不会出现这种情况
                        raise ValueError(f"替换图层【{title}】的图片失败，新增多个图层")  

        else:
            if layer.Kind == 2:
                photoshop_instance.set_text(layer, data)
            else:
                raise ValueError(f"图层【{title}】必须为文字图层，请检查PS文件！")

    #保存逻辑
    if filename is None or filename == '':
        filename = str(uuid.uuid1())
    if not os.path.exists(save_path):
        os.makedirs(save_path)

    save_name = os.path.join(save_path, filename + "." + save_format)
    photoshop_instance.save_as(save_name, save_format)

    #save psd
    if save_psd:
        save_name = os.path.join(save_path, filename + ".psd")
        photoshop_instance.save_as(save_name, 'psd')
    #返回生成的文件名
    return  filename + "." + save_format


def close(photoshop_instance):
    '''
    关闭photoshop对象
    '''
    photoshop_instance.close()
