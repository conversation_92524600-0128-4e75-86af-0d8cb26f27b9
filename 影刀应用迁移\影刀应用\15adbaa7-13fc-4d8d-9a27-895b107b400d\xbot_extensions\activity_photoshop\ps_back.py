import xbot
from xbot import print, sleep
from . import package
from .resources.photoshop_api import Photoshop
# from .photoshop_api import Photoshop
from . import pil_api
import uuid
import os
import datetime

from win32com.client import Dispatch
import pywintypes 


def solidcolor(hex_value):
    """返回颜色"""
    color = Dispatch("Photoshop.SolidColor")
    color.RGB.HexValue = hex_value
    return color 


def open(ps_file):
    """
    指令名：打开 PSD 文件
    打开指定路径的 PSD 文件，返回自定义的 Photoshop 对象 photoshop_api.Photoshop

    :param ps_file: PSD 文件的路径
    :type ps_file: str

    :return: Photoshop 对象
    :rtype: photoshop_api.Photoshop
    """

    if not os.path.exists(ps_file):
        raise ValueError(f'PS文件 {ps_file}不存在')
    #生成备份文件，打开备份文件，以防运行过程中，破坏图层

    photoshop_instance = Photoshop(ps_file)
    return photoshop_instance


# 获取图层对象（不呈现指令）


def get_layer_by_name(photoshop_instance, name_of_layer):
    return photoshop_instance.layer(name_of_layer, only_visible=False)


def hide_or_show_layer_by_name(photoshop_instance, name_of_layer, is_visible):
    """
    指令名：显示/隐藏图层
    在指定的 PS 对象中根据图层名确定某个图层，隐藏或显示这个图层

    :param photoshop_instance: Photoshop 对象
    :type photoshop_instance: photoshop_api.Photoshop
    :param name_of_layer: 图层名
    :type name_of_layer: str
    :param is_visible: 设置为可见还是不可见。"显示" 为可见 "隐藏" 为不可见
    :type is_visible: str
    """

    _is_visible = True if is_visible == "显示" else False

    if ">" not in name_of_layer:
        layer = get_layer_by_name(photoshop_instance, name_of_layer)
    else:
        *layersets, layer_name = [path.strip() for path in name_of_layer.split(">")]

        current_layerset = photoshop_instance.doc
        for layerset_name in layersets:
            current_layerset = current_layerset.LayerSets(layerset_name)
        layer = current_layerset.Layers(layer_name)
        
    photoshop_instance.set_visible(layer, is_visible=_is_visible)


# 激活图层


def activate_layer_by_name(photoshop_instance, name_of_layer):
    """
    指令名：激活图层
    在指定的 PS 对象中根据图层名确定某个图层，激活这个图层

    :param photoshop_instance: Photoshop 对象
    :type photoshop_instance: photoshop_api.Photoshop
    :param name_of_layer: 图层名
    :type name_of_layer: str
    """
    if ">" not in name_of_layer:
        layer = get_layer_by_name(photoshop_instance, name_of_layer)
    else:
        *layersets, layer_name = [path.strip() for path in name_of_layer.split(">")]

        current_layerset = photoshop_instance.doc
        for layerset_name in layersets:
            current_layerset = current_layerset.LayerSets(layerset_name)

        layer = current_layerset.Layers(layer_name)
    photoshop_instance.active_layer(layer)




def set_text_of_layer_by_name(photoshop_instance, name_of_layer,
                              text_to_be_set):
    """指令名：修改图层文字
    :param photoshop_instance: Photoshop 对象
    :type photoshop_instance: photoshop_api.Photoshop
    :param name_of_layer: 图层名
    :type name_of_layer: str
    :param text_to_be_set: 要更改为的文字内容
    :type text_to_be_set: str
    """
    if ">" not in name_of_layer:
        layer = get_layer_by_name(photoshop_instance, name_of_layer)
        
    else: 
        *layersets, layer_name = [path.strip() for path in name_of_layer.split(">")]
        # print(layersets)

        current_layerset = photoshop_instance.doc
        for layerset_name in layersets:
            current_layerset = current_layerset.LayerSets(layerset_name)

        layer = current_layerset.Layers(layer_name)

    photoshop_instance.set_text(layer, text_to_be_set)


# 替换图层图片
def change_content_of_layer_by_name_from_file(
    photoshop_instance,
    name_of_layer,
    path_to_image_to_replace,
    keep_original_size_of_image_file=True):
    """
    指令名：替换图层图片
    替换指定 Photoshop 对象 中指定的图层的内容为另一张图片，可选是否保留这个图片的原始大小或者按原图层大小调整新图片大小

    :param photoshop_instance: Photoshop 对象
    :type photoshop_instance: photoshop_api.Photoshop
    :param name_of_layer: 需要被替换的图层名
    :type name_of_layer: str
    :param path_to_image_to_replace: 需要被替换上的图片路径
    :type path_to_image_to_replace: str
    :param keep_original_size_of_image_file: 是否保留要替换上的图片文件的原始大小，默认保留
    :type keep_original_size_of_image_file: bool
    """
    
    # layer = get_layer_by_name(photoshop_instance, name_of_layer)

    if ">" not in name_of_layer:
        layer = get_layer_by_name(photoshop_instance, name_of_layer)
        
    else: 
        *layersets, layer_name = [path.strip() for path in name_of_layer.split(">")]
        # print(layersets)

        current_layerset = photoshop_instance.doc
        for layerset_name in layersets:
            current_layerset = current_layerset.LayerSets(layerset_name)

        layer = current_layerset.Layers(layer_name)

    layers = photoshop_instance.all_layers(only_visible=False)

    # 判断路径是否存在
    if not os.path.exists(path_to_image_to_replace):
        raise ValueError(f"图片【{path_to_image_to_replace}】不存在，请检查！")

    # 判断图层类型
    if layer.Kind not in (1, 17):
        raise ValueError(f"暂不支持图层【{name_of_layer}】替换图片，请检查图层类型！")

    (layer_width, layer_height) = photoshop_instance.size(layer)

    if layer.Kind == 1:
        # 替换成智能对象 最多尝试三次
        for i in range(3):
            try:
                photoshop_instance.to_smart_object(layer)
                break
            except Exception as err:
                error = err
        else:
            raise error

    success_replace = False

    ###-> test
    

    photoshop_instance.active_layer(layer)
    photoshop_instance.replace_smart_object_content(
        path_to_image_to_replace)

    ###-< test

    try:
        # 替换内容
        photoshop_instance.active_layer(layer)
        photoshop_instance.replace_smart_object_content(
            path_to_image_to_replace)

        # 修改图像大小
        if not keep_original_size_of_image_file:

            pil = pil_api.PIL_API(file_name=path_to_image_to_replace)
            (width, height) = pil.size()
            if abs(width - layer_width) > 1 or abs(height - layer_height) > 1:
                # 72不缩放
                photoshop_instance.resize_layer_image(layer_width,
                                                      layer_height,
                                                      dpi=72)
        # end 修改图像大小

        success_replace = True

        if layer.Kind == 1:
            # 栅格化
            try:
                #删格化失败了会怎么样？
                photoshop_instance.rasterize_layer()
            except Exception as err:
                raise (ValueError(
                    f"删格化失败！函数：change_content_of_layer_by_name_from_file, Line 161. {err}"
                ))
    except Exception as err:
        raise (ValueError(
            f"替换内容失败！函数：change_content_of_layer_by_name_from_file, Line 163. {err}"
        ))
    finally:
        # 改回图层名
        # diff出新图层,因为转成智能对象本质上是新产生了一个图层，替换了原图层
        if success_replace:
            newest_layers = photoshop_instance.all_layers(only_visible=False)
            # 不能直接转化成set再用diff
            newers = [item for item in newest_layers if item not in layers]
            if len(newers) == 1:
                newers[0].Name = name_of_layer
                #replace layer using newest_layers
                layers = newest_layers
            elif len(newers) > 1:
                # 从目前来看，不会出现这种情况
                raise ValueError(f"替换图层【{name_of_layer}】的图片失败，新增多个图层")


# 关闭 Photoshop 文件


def close(photoshop_instance, save_change=False, quit=True):
    '''
    指令名：关闭 PSD 文件
    关闭 PSD 文件，如果只打开了一个文件，则关闭文件后关闭 PS，可选是否保存现有更改

    :param photoshop_instance: Photoshop 对象
    :type photoshop_instance: photoshop_api.Photoshop
    :param save_change: 是否保存变更，默认False
    :type save_change: bool
    '''
    photoshop_instance.close(save_change=save_change, quit=quit)


# 另存为 PSD


def save_as_psd(photoshop_instance, save_path):
    """
    指令名：另存为 PSD 文件
    将 Photoshop 对象 另存为一个 PSD 文件

    :param photoshop_instance: Photoshop 对象
    :type photoshop_instance: photoshop_api.Photoshop
    :param save_name: 保存文件全路径包括.psd
    :type save_name: str
    """

    _, file_format = os.path.splitext(save_path)
    # 去掉 .psd 前面的 .
    file_format = file_format[1:] if "." in file_format else file_format
    file_format = file_format.lower()

    if file_format == 'psd':
        options = _create_instance("Photoshop.PhotoshopSaveOptions")
    else:
        raise ValueError(
            f"仅支持另存为【PSD】文件，不支持另存为【{file_format}】文件，若要导出为图片，请用【导出为图片】指令")
    photoshop_instance.doc.SaveAs(save_path, options, True)


def export_as_image(photoshop_instance, save_path, quality):
    """
    指令名：导出为图片
    将指定的 PS 对象导出为图片，存放在指定路径（图片全路径，非文件夹路径）中
    :param photoshop_instance: Photoshop 对象
    :type photoshop_instance: photoshop_api.Photoshop
    :param save_path: 保存路径（全路径）
    :type save_path: str
    :param quality: 图片质量, 参参数仅适用于 jpg 格式图片, png 无效
    """

    dir_path, _ = os.path.split(save_path)
    _, file_format = os.path.splitext(save_path)
    assert os.path.exists(dir_path), "请检查保存文件的路径是否存在"
    # 去掉 .jpg 等前面的 .
    file_format = file_format[1:]
    file_format = file_format.lower()

    if quality: assert str(quality).isdigit(), "quality 指令比应为数字"

    if file_format == 'png':
        options = _create_instance("Photoshop.PNGSaveOptions")
    elif file_format in ('jpg', 'jpeg'):
        options = _create_instance("Photoshop.JPEGSaveOptions")
        try:
            if quality: options.Quality = int(quality)
        except:
            print("Quality 超过最大值了")
    elif file_format == 'bmp':
        options = _create_instance("Photoshop.BMPSaveOptions")
    else:
        raise ValueError(f"暂不支持另存为【{file_format}】格式")
    photoshop_instance.doc.SaveAs(save_path, options, True)


def get_names_of_all_layers(photoshop_instance, only_text_layer):
    """
    指令名：获取所有图层名
    不包含 set 的名字，包含不可见图层

    :param photoshop_instance: Photoshop 对象
    :type photoshop_instance: photoshop_api.Photoshop

    :param only_text_layer: bool, 仅文字图层

    :return: 所有图层名的列表
    :rtype: list of str
    """
    if only_text_layer:
        artlayers_name = []
        layers = photoshop_instance.all_layers(only_visible=False,
                                               contain_set=False)
        for layer in layers:
            if layer.Kind == 2:
                artlayers_name.append(layer.Name)
        return artlayers_name

    return [
        layer.Name
        for layer in photoshop_instance.all_layers(only_visible=False,
                                                   contain_set=False)
    ]


def _create_instance(com_name):
    try:
        com_obj = Dispatch(com_name)
    except pywintypes.com_error:
        raise ValueError(f'请检查您的电脑是否已安装了Photoshop，或是否处于编辑状态')
    return com_obj


def get_artlayer_text(ps_instance, layer_name, only_visible):
    """获取文字图层文本
    :param ps_instance: object, Ps 对象
    :param layer_name: str, 图层名称
    :param only_visible: bool, 是否为隐藏图层
    """

    if ">" not in layer_name:
        try:
            layer = ps_instance.app.ActiveDocument.ArtLayers(layer_name)
        except:
            layer = get_layer_by_name(ps_instance, layer_name)
        
    else: 
        *layersets, layer_name = [path.strip() for path in layer_name.split(">")]
        # print(layersets)

        current_layerset = ps_instance.doc
        for layerset_name in layersets:
            current_layerset = current_layerset.LayerSets(layerset_name)

        layer = current_layerset.Layers(layer_name)
    
    return layer.TextItem.Contents


def set_artlayer_font(ps_instance, layer_name, font_size, font, hex_value, only_visible):
    """设置文字图层的字体大小和字体样式
    :param ps_instance: object, Ps 对象
    :param layer_name: str, 图层名称
    :param font_size: str, 字体大小
    :param font: str, 字体
    :param only_visible: bool, 是否为隐藏图层
    """
    layer = ps_instance.layer(layer_name, only_visible=only_visible)
    if font_size:
        layer.TextItem.Size = int(font_size)
    if font:
        layer.TextItem.Font = font
    if hex_value:
        layer.TextItem.Color = solidcolor(hex_value)

def get_artlayer_font_info(ps_instance, layer_name, only_visible):
    """获取图层字体相关信息
    :param ps_instance: object, Ps 对象
    :param layer_name: str, 图层名称
    :param only_visible: bool, 是否为隐藏图层
    """
    layer = ps_instance.layer(layer_name, only_visible=only_visible)
    font_size = layer.TextItem.Size
    font = layer.TextItem.Font
    hex_value = layer.TextItem.Color.RGB.HexValue

    return font_size, font, hex_value


def get_layer_parent_name(ps_instance, layer_name):
    """获取组名
    :param ps_instance:
    :param layer_name:
    :return: list
    """
    layer = ps_instance.layer(layer_name, only_visible=False)
    doc_name = ps_instance.doc.Name

    layer_parent_groups = []
    while doc_name != layer.Parent.Name:
        layer_parent_groups.append(layer.Parent.Name)
        layer = layer.Parent

    if len(layer_parent_groups) == 0:
        layer_parent_group = None
    else:
        layer_parent_group = layer_parent_groups[0]

    return layer_parent_group, layer_parent_groups


def make_point(point_position):
    """创建路径点"""
    point = Dispatch("Photoshop.PathPointInfo")
    point.Anchor = point_position
    point.Kind = 2
    point.leftDirection = point.Anchor
    point.rightDirection = point.Anchor
    return point


def fill_content_aware(ps_instance, layer_name, path):
    """
    用内容识别工具来填充指定路径内的区域
    :param ps_instance: Photoshop 实例对象
    :param layer_name: 图层名称
    :param path: 填充区域的路径，格式为 [[x1, y1], [x2, y2], ...]
    """
    app = ps_instance.app
    doc = ps_instance.doc

    # 创建路径
    subpath = Dispatch("Photoshop.SubPathInfo")
    subpath.Closed = True
    subpath.Operation = 2
    subpath.EntireSubPath = list(map(make_point, path))
    path_item = doc.PathItems.Add(layer_name, [subpath])

    # 选中路径并进行内容识别填充
    try:
        path_item.MakeSelection()
        idFl = app.CharIDToTypeID("Fl  ")
        desc = Dispatch("Photoshop.ActionDescriptor")
        idUsng = app.CharIDToTypeID("Usng")
        idFlCn = app.CharIDToTypeID("FlCn")
        idcontentAware = app.StringIDToTypeID("contentAware")
        desc.PutEnumerated(idUsng, idFlCn, idcontentAware)

        idOpct = app.CharIDToTypeID("Opct")
        idPrc = app.CharIDToTypeID("#Prc")
        desc.PutUnitDouble(idOpct, idPrc, 100)

        idMd = app.CharIDToTypeID("Md  ")
        idBlnM = app.CharIDToTypeID("BlnM")
        idNrml = app.CharIDToTypeID("Nrml")
        desc.PutEnumerated(idMd, idBlnM, idNrml)

        app.ExecuteAction(idFl, desc, 3)
    finally:
        path_item.Delete()
        doc.Selection.Deselect()


def rename_layer_name(ps_instance, layer_name, new_layer_name):
    """重命名图层名"""
    ps_instance.layer(layer_name).Name = new_layer_name


def hex_to_rgb(hex_value):
    hex_value = hex_value.lstrip("#")
    return tuple(int(hex_value[i:i+2], 16) for i in (0, 2, 4))


def set_rectangle_tool_fill_color(ps_instance, layer_name , hex_value):
    """矩形工具, 调整填充颜色"""
    
    # 激活图层
    fill_layer = ps_instance.layer(layer_name)
    ps_instance.doc.ActiveLayer = fill_layer

    # 转换颜色
    r, g, b = hex_to_rgb(hex_value)
    objApp = Dispatch("Photoshop.Application")
    dialogMode = 3
    idsetd = objApp.CharIDToTypeID("setd")
    desc236 = Dispatch("Photoshop.ActionDescriptor")
    idnull = objApp.CharIDToTypeID("null")
    ref2 = Dispatch("Photoshop.ActionReference")
    idcontentLayer = objApp.StringIDToTypeID("contentLayer")
    idOrdn = objApp.CharIDToTypeID("Ordn")
    idTrgt = objApp.CharIDToTypeID("Trgt")
    ref2.PutEnumerated(idcontentLayer, idOrdn, idTrgt)
    desc236.PutReference(idnull, ref2)
    idT = objApp.CharIDToTypeID("T   ")
    desc237 = Dispatch("Photoshop.ActionDescriptor")
    idClr = objApp.CharIDToTypeID("Clr ")
    desc238 = Dispatch("Photoshop.ActionDescriptor")
    idRd = objApp.CharIDToTypeID("Rd  ")
    desc238.PutDouble(idRd, r)
    idGrn = objApp.CharIDToTypeID("Grn ")
    desc238.PutDouble(idGrn, g)
    idBl = objApp.CharIDToTypeID("Bl  ")
    desc238.PutDouble(idBl, b)
    idRGBC = objApp.CharIDToTypeID("RGBC")
    desc237.PutObject(idClr, idRGBC, desc238)
    idcontentLayer = objApp.StringIDToTypeID("contentLayer")
    idsolidColorLayer = objApp.StringIDToTypeID("solidColorLayer")
    desc236.PutObject(idT, idsolidColorLayer, desc237)
    objApp.ExecuteAction(idsetd, desc236, dialogMode)


def set_rectangle_tool_stroke_color(ps_instance, layer_name, hex_value):
    """矩形工具, 调整描边颜色"""
    fill_layer = ps_instance.layer(layer_name)
    ps_instance.doc.ActiveLayer = fill_layer

    r, g, b =  hex_to_rgb(hex_value)

    objApp = Dispatch("Photoshop.Application")
    dialogMode = 3
    idsetd = objApp.CharIDToTypeID( "setd" )
    desc530 = Dispatch( "Photoshop.ActionDescriptor" )
    idnull = objApp.CharIDToTypeID( "null" )
    ref65 = Dispatch( "Photoshop.ActionReference" )
    idcontentLayer = objApp.StringIDToTypeID( "contentLayer" )
    idOrdn = objApp.CharIDToTypeID( "Ordn" )
    idTrgt = objApp.CharIDToTypeID( "Trgt" )
    ref65.PutEnumerated( idcontentLayer, idOrdn, idTrgt )
    desc530.PutReference( idnull, ref65 )
    idT = objApp.CharIDToTypeID( "T   " )
    desc531 = Dispatch( "Photoshop.ActionDescriptor" )
    idstrokeStyle = objApp.StringIDToTypeID( "strokeStyle" )
    desc532 = Dispatch( "Photoshop.ActionDescriptor" )
    idstrokeStyleContent = objApp.StringIDToTypeID( "strokeStyleContent" )
    desc533 = Dispatch( "Photoshop.ActionDescriptor" )
    idClr = objApp.CharIDToTypeID( "Clr " )
    desc534 = Dispatch( "Photoshop.ActionDescriptor" )
    idRd = objApp.CharIDToTypeID("Rd  ")
    desc534.PutDouble(idRd, r)
    idGrn = objApp.CharIDToTypeID("Grn ")
    desc534.PutDouble(idGrn, g)
    idBl = objApp.CharIDToTypeID("Bl  ")
    desc534.PutDouble(idBl, b)
    idRGBC = objApp.CharIDToTypeID("RGBC")
    desc533.PutObject(idClr, idRGBC, desc534)
    idsolidColorLayer = objApp.StringIDToTypeID( "solidColorLayer" )
    desc532.PutObject( idstrokeStyleContent, idsolidColorLayer, desc533 )
    idstrokeStyleVersion = objApp.StringIDToTypeID( "strokeStyleVersion" )
    desc532.PutInteger( idstrokeStyleVersion, 2 )
    idstrokeEnabled = objApp.StringIDToTypeID( "strokeEnabled" )
    desc532.PutBoolean( idstrokeEnabled, True )
    idfillEnabled = objApp.StringIDToTypeID( "fillEnabled" )
    desc532.PutBoolean( idfillEnabled, False )
    idstrokeStyle = objApp.StringIDToTypeID( "strokeStyle" )
    desc531.PutObject( idstrokeStyle, idstrokeStyle, desc532 )
    idshapeStyle = objApp.StringIDToTypeID( "shapeStyle" )
    desc530.PutObject( idT, idshapeStyle, desc531 )
    objApp.ExecuteAction( idsetd, desc530, dialogMode )


def main(args):
    pass
