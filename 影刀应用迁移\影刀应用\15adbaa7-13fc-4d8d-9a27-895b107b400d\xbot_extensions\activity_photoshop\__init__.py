from .import package
import xbot_visual
from . import export_artboard_as_png
from . import replace_layer_image
from . import set_text_format
from . import edit_smart_object
from . import get_active_doc

def process1(ps_file):
    """
    打开 PSD 文件
    打开 PSD 文件
    * @param ps_file，
    * @return photoshop_instance，
    """
    outputs = ["photoshop_instance"]
    inputs = {"ps_file":ps_file}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process1", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process1",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process1", extension_module, activity_func)

def process2(photoshop_instance,name_of_layer,is_visible):
    """
    显示/隐藏图层
    在 PSD 文件中显示或隐藏一个图层
    * @param photoshop_instance，
    * @param name_of_layer，
    * @param is_visible，
    """
    outputs = []
    inputs = {"photoshop_instance":photoshop_instance,"name_of_layer":name_of_layer,"is_visible":is_visible}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process2", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process2",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process2", extension_module, activity_func)

def process3(photoshop_instance,name_of_layer):
    """
    激活图层
    激活一个 PSD 文件的图层
    * @param photoshop_instance，
    * @param name_of_layer，
    """
    outputs = []
    inputs = {"photoshop_instance":photoshop_instance,"name_of_layer":name_of_layer}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process3", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process3",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process3", extension_module, activity_func)

def process4(photoshop_instance,name_of_layer,text_to_be_set):
    """
    修改图层文字
    在 PSD 文件中修改文字图层的文字
    * @param photoshop_instance，
    * @param name_of_layer，
    * @param text_to_be_set，
    """
    outputs = []
    inputs = {"photoshop_instance":photoshop_instance,"name_of_layer":name_of_layer,"text_to_be_set":text_to_be_set}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process4", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process4",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process4", extension_module, activity_func)

def process5(photoshop_instance,name_of_layer,path_to_image_to_replace,keep_original_size_of_image_file):
    """
    替换图层图片
    在 PSD 文件中将图片图层的内容替换为另一张图片
    * @param photoshop_instance，
    * @param name_of_layer，
    * @param path_to_image_to_replace，
    * @param keep_original_size_of_image_file，
    """
    outputs = []
    inputs = {"photoshop_instance":photoshop_instance,"name_of_layer":name_of_layer,"path_to_image_to_replace":path_to_image_to_replace,"keep_original_size_of_image_file":keep_original_size_of_image_file}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process5", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process5",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process5", extension_module, activity_func)

def process6(photoshop_instance,save_path,quality):
    """
    导出为图片
    将打开的 PSD 文件导出为图片
    * @param photoshop_instance，
    * @param save_path，
    * @param quality，导出图片的质量, 仅支持jpg, jepg 格式
    """
    outputs = []
    inputs = {"photoshop_instance":photoshop_instance,"save_path":save_path,"quality":quality}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process6", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process6",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process6", extension_module, activity_func)

def process7(photoshop_instance,save_change,quit):
    """
    关闭 PSD 文件
    关闭 PSD 文件
    * @param photoshop_instance，
    * @param save_change，
    * @param quit，
    """
    outputs = []
    inputs = {"photoshop_instance":photoshop_instance,"save_change":save_change,"quit":quit}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process7", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process7",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process7", extension_module, activity_func)

def process8(photoshop_instance,save_path):
    """
    另存为 PSD 文件
    将 PSD 文件另存为一份
    * @param photoshop_instance，
    * @param save_path，
    """
    outputs = []
    inputs = {"photoshop_instance":photoshop_instance,"save_path":save_path}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process8", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process8",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process8", extension_module, activity_func)

def process9(photoshop_instance,only_text_layer):
    """
    获取所有图层名
    获取 PDF 文件中所有图层名
    * @param photoshop_instance，
    * @param only_text_layer，
    * @return name_of_all_layers，
    """
    outputs = ["name_of_all_layers"]
    inputs = {"photoshop_instance":photoshop_instance,"only_text_layer":only_text_layer}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process9", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process9",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process9", extension_module, activity_func)

def process10(ps_instance,layer_name,only_visible):
    """
    获取图层文字
    该指令实现获取文字图层的文本
    * @param ps_instance，
    * @param layer_name，
    * @param only_visible，
    * @return layer_text，
    """
    outputs = ["layer_text"]
    inputs = {"ps_instance":ps_instance,"layer_name":layer_name,"only_visible":only_visible}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process10", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process10",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process10", extension_module, activity_func)

def process11(ps_instance,layer_name,font_size,font,only_visible,hex_value):
    """
    设置文字图层字体
    该指令设置文字图层的字体样式和大小
    * @param ps_instance，
    * @param layer_name，
    * @param font_size，
    * @param font，
    * @param only_visible，
    * @param hex_value，
    """
    outputs = []
    inputs = {"ps_instance":ps_instance,"layer_name":layer_name,"font_size":font_size,"font":font,"only_visible":only_visible,"hex_value":hex_value}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process11", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process11",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process11", extension_module, activity_func)

def process12(ps_instance,layer_name):
    """
    获取图层所属组
    该指令实现获取指定图层所属组名
    * @param ps_instance，
    * @param layer_name，
    * @return parent_group，
    * @return parent_groups，
    """
    outputs = ["parent_group","parent_groups"]
    inputs = {"ps_instance":ps_instance,"layer_name":layer_name}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process12", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process12",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process12", extension_module, activity_func)

def process13(ps_instance,layer_name,new_layer_name):
    """
    重命名图层
    该指令实现了重命名 PS 图层名称的功能
    * @param ps_instance，待处理的PS对象实例
    * @param layer_name，原图层名称
    * @param new_layer_name，新图层的名称
    """
    outputs = []
    inputs = {"ps_instance":ps_instance,"layer_name":layer_name,"new_layer_name":new_layer_name}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process13", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process13",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process13", extension_module, activity_func)

def process14(ps_instance,layer_name,path):
    """
    内容识别填充
    该指令实现了在指令路径区域内进行内容识别填充
    * @param ps_instance，待处理的PS对象实例
    * @param layer_name，待操作的图层名称
    * @param path，待选中的路径坐标
    """
    outputs = []
    inputs = {"ps_instance":ps_instance,"layer_name":layer_name,"path":path}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process14", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process14",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process14", extension_module, activity_func)

def process15(ps_instance,layer_name,hex_value):
    """
    修改颜色填充图层
    该指令实现了在 PS 中修改颜色填充图层的功能
    * @param ps_instance，待处理的PS对象实例
    * @param layer_name，待操作的图层名称
    * @param hex_value，图层填充RGB颜色HEX字符串
    """
    outputs = []
    inputs = {"ps_instance":ps_instance,"layer_name":layer_name,"hex_value":hex_value}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process15", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process15",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process15", extension_module, activity_func)

def process16(ps_instance,layer_name,hex_value):
    """
    设置形状图层描边
    该指令实现了 PS 设置形态图层描边颜色的功能
    * @param ps_instance，待处理的PS对象实例
    * @param layer_name，待操作的图层名称
    * @param hex_value，图层描边RGB颜色HEX字符串
    """
    outputs = []
    inputs = {"ps_instance":ps_instance,"layer_name":layer_name,"hex_value":hex_value}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process16", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process16",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process16", extension_module, activity_func)

def process17(ps_instance,mode,name,save_path):
    """
    导出图层为PNG
    该指令将指定图层或图层组导出为 PNG 图片
    * @param ps_instance，
    * @param mode，
    * @param name，
    * @param save_path，
    """
    outputs = []
    inputs = {"ps_instance":ps_instance,"mode":mode,"name":name,"save_path":save_path}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process17", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process17",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process17", extension_module, activity_func)

def process18(ps_instance,file_path,layer_name):
    """
    添加图片图层
    该指令实现了在 Photoshop 中添加文字图层的能力
    * @param ps_instance，
    * @param file_path，
    * @param layer_name，
    * @return new_layer_name，
    """
    outputs = ["new_layer_name"]
    inputs = {"ps_instance":ps_instance,"file_path":file_path,"layer_name":layer_name}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process18", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process18",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process18", extension_module, activity_func)

def process19(ps_instance,layer_name):
    """
    删除图层
    改指令实现了删除 Photoshop 图层的功能
    * @param ps_instance，
    * @param layer_name，
    """
    outputs = []
    inputs = {"ps_instance":ps_instance,"layer_name":layer_name}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process19", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process19",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process19", extension_module, activity_func)

def process20(ps_instance,layer_name,only_visible):
    """
    获取图层字体信息
    该指令获取指定图层的字体信息
    * @param ps_instance，待处理的ps_instance
    * @param layer_name，待操作的图层名称
    * @param only_visible，是否仅可见的图层
    * @return font_size，字体的大小
    * @return font_name，字体的名称
    * @return hex_value，字体RGB的HexValue
    """
    outputs = ["font_size","font_name","hex_value"]
    inputs = {"ps_instance":ps_instance,"layer_name":layer_name,"only_visible":only_visible}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process20", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process20",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process20", extension_module, activity_func)

def process21(ps_instance):
    """
    激活文档
    该指令实现了将指定 ps_instance 设置为激活的文档
    * @param ps_instance，
    """
    outputs = []
    inputs = {"ps_instance":ps_instance}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process21", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process21",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process21", extension_module, activity_func)

def process22(ps_instance,layer_name,image_path):
    """
    替换图框
    该指令实现在 Photoshop 替换图框中图片的功能
    * @param ps_instance，待操作的 ps_instance
    * @param layer_name，待操作的图层名称, 支持组1>组2>组3>图层1
    * @param image_path，替换图片的路径
    """
    outputs = []
    inputs = {"ps_instance":ps_instance,"layer_name":layer_name,"image_path":image_path}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_photoshop.process22", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_photoshop.process22",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_photoshop.process22", extension_module, activity_func)

