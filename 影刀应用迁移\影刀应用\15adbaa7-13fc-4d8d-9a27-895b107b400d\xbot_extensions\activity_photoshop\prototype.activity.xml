<root>
  <item name="xbot_extensions.activity_photoshop" display="activity_photoshop" kind="module">
    <item.method />
    <item.desc>Photoshop 扩展
提供包括打开 PSD 文件、替换图层内容、导出为图片、显示和隐藏图层等等 Photoshop 功能。在 21.0.2 版本上测试通过。</item.desc>
    <item name="xbot_extensions.activity_photoshop.process1" display="process1" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/打开psd文件.html">
      <item.method>process1(ps_file)</item.method>
      <item.desc>打开 PSD 文件
打开 PSD 文件
* @param ps_file，
* @return photoshop_instance，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process2" display="process2" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/显示（隐藏）图层.html">
      <item.method>process2(photoshop_instance,name_of_layer,is_visible)</item.method>
      <item.desc>显示/隐藏图层
在 PSD 文件中显示或隐藏一个图层
* @param photoshop_instance，
* @param name_of_layer，
* @param is_visible，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process3" display="process3" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/激活图层.html">
      <item.method>process3(photoshop_instance,name_of_layer)</item.method>
      <item.desc>激活图层
激活一个 PSD 文件的图层
* @param photoshop_instance，
* @param name_of_layer，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process4" display="process4" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/修改图层文字.html">
      <item.method>process4(photoshop_instance,name_of_layer,text_to_be_set)</item.method>
      <item.desc>修改图层文字
在 PSD 文件中修改文字图层的文字
* @param photoshop_instance，
* @param name_of_layer，
* @param text_to_be_set，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process5" display="process5" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/替换图层图片.html">
      <item.method>process5(photoshop_instance,name_of_layer,path_to_image_to_replace,keep_original_size_of_image_file)</item.method>
      <item.desc>替换图层图片
在 PSD 文件中将图片图层的内容替换为另一张图片
* @param photoshop_instance，
* @param name_of_layer，
* @param path_to_image_to_replace，
* @param keep_original_size_of_image_file，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process6" display="process6" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/导出为图片.html">
      <item.method>process6(photoshop_instance,save_path,quality)</item.method>
      <item.desc>导出为图片
将打开的 PSD 文件导出为图片
* @param photoshop_instance，
* @param save_path，
* @param quality，导出图片的质量, 仅支持jpg, jepg 格式
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process7" display="process7" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/关闭psd文件.html">
      <item.method>process7(photoshop_instance,save_change,quit)</item.method>
      <item.desc>关闭 PSD 文件
关闭 PSD 文件
* @param photoshop_instance，
* @param save_change，
* @param quit，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process8" display="process8" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/另存为psd文件.html">
      <item.method>process8(photoshop_instance,save_path)</item.method>
      <item.desc>另存为 PSD 文件
将 PSD 文件另存为一份
* @param photoshop_instance，
* @param save_path，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process9" display="process9" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/获取所有图层名.html">
      <item.method>process9(photoshop_instance,only_text_layer)</item.method>
      <item.desc>获取所有图层名
获取 PDF 文件中所有图层名
* @param photoshop_instance，
* @param only_text_layer，
* @return name_of_all_layers，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process10" display="process10" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/获取图层文字.html?">
      <item.method>process10(ps_instance,layer_name,only_visible)</item.method>
      <item.desc>获取图层文字
该指令实现获取文字图层的文本
* @param ps_instance，
* @param layer_name，
* @param only_visible，
* @return layer_text，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process11" display="process11" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/设置文字图层字体.html?">
      <item.method>process11(ps_instance,layer_name,font_size,font,only_visible,hex_value)</item.method>
      <item.desc>设置文字图层字体
该指令设置文字图层的字体样式和大小
* @param ps_instance，
* @param layer_name，
* @param font_size，
* @param font，
* @param only_visible，
* @param hex_value，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process12" display="process12" kind="function" help="yddoc/language/zh-cn/指令文档/自定义指令使用说明/photoshop指令集/获取图层所属组.html?">
      <item.method>process12(ps_instance,layer_name)</item.method>
      <item.desc>获取图层所属组
该指令实现获取指定图层所属组名
* @param ps_instance，
* @param layer_name，
* @return parent_group，
* @return parent_groups，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process13" display="process13" kind="function" help="/yddoc/rpa/712598633364492288">
      <item.method>process13(ps_instance,layer_name,new_layer_name)</item.method>
      <item.desc>重命名图层
该指令实现了重命名 PS 图层名称的功能
* @param ps_instance，待处理的PS对象实例
* @param layer_name，原图层名称
* @param new_layer_name，新图层的名称
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process14" display="process14" kind="function" help="/yddoc/rpa/712598704502784000">
      <item.method>process14(ps_instance,layer_name,path)</item.method>
      <item.desc>内容识别填充
该指令实现了在指令路径区域内进行内容识别填充
* @param ps_instance，待处理的PS对象实例
* @param layer_name，待操作的图层名称
* @param path，待选中的路径坐标
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process15" display="process15" kind="function" help="/yddoc/rpa/712598794688708608">
      <item.method>process15(ps_instance,layer_name,hex_value)</item.method>
      <item.desc>修改颜色填充图层
该指令实现了在 PS 中修改颜色填充图层的功能
* @param ps_instance，待处理的PS对象实例
* @param layer_name，待操作的图层名称
* @param hex_value，图层填充RGB颜色HEX字符串
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process16" display="process16" kind="function" help="/yddoc/rpa/712598877912088576">
      <item.method>process16(ps_instance,layer_name,hex_value)</item.method>
      <item.desc>设置形状图层描边
该指令实现了 PS 设置形态图层描边颜色的功能
* @param ps_instance，待处理的PS对象实例
* @param layer_name，待操作的图层名称
* @param hex_value，图层描边RGB颜色HEX字符串
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process17" display="process17" kind="function" help="/yddoc/rpa/712598921859035136">
      <item.method>process17(ps_instance,mode,name,save_path)</item.method>
      <item.desc>导出图层为PNG
该指令将指定图层或图层组导出为 PNG 图片
* @param ps_instance，
* @param mode，
* @param name，
* @param save_path，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process18" display="process18" kind="function" help="/yddoc/rpa/712598963015815168">
      <item.method>process18(ps_instance,file_path,layer_name)</item.method>
      <item.desc>添加图片图层
该指令实现了在 Photoshop 中添加文字图层的能力
* @param ps_instance，
* @param file_path，
* @param layer_name，
* @return new_layer_name，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process19" display="process19" kind="function" help="/yddoc/rpa/712599007972712448">
      <item.method>process19(ps_instance,layer_name)</item.method>
      <item.desc>删除图层
改指令实现了删除 Photoshop 图层的功能
* @param ps_instance，
* @param layer_name，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process20" display="process20" kind="function" help="/yddoc/rpa/712599049102057472">
      <item.method>process20(ps_instance,layer_name,only_visible)</item.method>
      <item.desc>获取图层字体信息
该指令获取指定图层的字体信息
* @param ps_instance，待处理的ps_instance
* @param layer_name，待操作的图层名称
* @param only_visible，是否仅可见的图层
* @return font_size，字体的大小
* @return font_name，字体的名称
* @return hex_value，字体RGB的HexValue
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process21" display="process21" kind="function" help="/yddoc/rpa/750524152562864128?">
      <item.method>process21(ps_instance)</item.method>
      <item.desc>激活文档
该指令实现了将指定 ps_instance 设置为激活的文档
* @param ps_instance，
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.process22" display="process22" kind="function" help="/yddoc/rpa/750524230149099520">
      <item.method>process22(ps_instance,layer_name,image_path)</item.method>
      <item.desc>替换图框
该指令实现在 Photoshop 替换图框中图片的功能
* @param ps_instance，待操作的 ps_instance
* @param layer_name，待操作的图层名称, 支持组1&gt;组2&gt;组3&gt;图层1
* @param image_path，替换图片的路径
</item.desc>
    </item>
    <item name="xbot_extensions.activity_photoshop.export_artboard_as_png" display="export_artboard_as_png" kind="function" help="/yddoc/rpa/750524289745813504">
      <item.method />
      <item.desc />
      <item name="xbot_extensions.activity_photoshop.export_artboard_as_png.remove_invalid_chars" display="remove_invalid_chars" kind="function" help="/yddoc/rpa/750524289745813504">
        <item.method />
        <item.desc />
      </item>
      <item name="xbot_extensions.activity_photoshop.export_artboard_as_png.export_artboard_to_png" display="export_artboard_to_png" kind="function" help="/yddoc/rpa/750524289745813504">
        <item.method />
        <item.desc />
      </item>
      <item name="xbot_extensions.activity_photoshop.export_artboard_as_png.main" display="main" kind="function" help="/yddoc/rpa/750524289745813504">
        <item.method>main(args)</item.method>
        <item.desc>画板切图
该指令实现了将画板导出PNG格式至指定目录
* @param ps_instance，待处理的ps_instance
* @param save_dir，导出图片保存的目录
* @param file_type，保存文件的类型
* @return files_path，保存文件的所有路径
</item.desc>
      </item>
    </item>
    <item name="xbot_extensions.activity_photoshop.replace_layer_image" display="replace_layer_image" kind="function" help="/yddoc/rpa/770888169164795904">
      <item.method />
      <item.desc />
      <item name="xbot_extensions.activity_photoshop.replace_layer_image.move" display="move" kind="function" help="/yddoc/rpa/770888169164795904">
        <item.method />
        <item.desc />
      </item>
      <item name="xbot_extensions.activity_photoshop.replace_layer_image.placed_layer_replace_contents" display="placed_layer_replace_contents" kind="function" help="/yddoc/rpa/770888169164795904">
        <item.method />
        <item.desc />
      </item>
      <item name="xbot_extensions.activity_photoshop.replace_layer_image.placed_layer_reset_transforms" display="placed_layer_reset_transforms" kind="function" help="/yddoc/rpa/770888169164795904">
        <item.method />
        <item.desc />
      </item>
      <item name="xbot_extensions.activity_photoshop.replace_layer_image.get_layer_by_name" display="get_layer_by_name" kind="function" help="/yddoc/rpa/770888169164795904">
        <item.method />
        <item.desc />
      </item>
      <item name="xbot_extensions.activity_photoshop.replace_layer_image.replace_image" display="replace_image" kind="function" help="/yddoc/rpa/770888169164795904">
        <item.method />
        <item.desc />
      </item>
      <item name="xbot_extensions.activity_photoshop.replace_layer_image.main" display="main" kind="function" help="/yddoc/rpa/770888169164795904">
        <item.method>main(args)</item.method>
        <item.desc>替换图层图片(自动缩放)
该指令实现了在PS中替换图层图片并自动缩放大小
* @param ps_instance，待处理的 ps_instance对象
* @param layer_name，待操作的图层名称
* @param image_path，替换图片的路径
</item.desc>
      </item>
    </item>
    <item name="xbot_extensions.activity_photoshop.set_text_format" display="set_text_format" kind="function" help="/yddoc/rpa/770888082639388672">
      <item.method />
      <item.desc />
      <item name="xbot_extensions.activity_photoshop.set_text_format.set_text_format" display="set_text_format" kind="function" help="/yddoc/rpa/770888082639388672">
        <item.method />
        <item.desc />
      </item>
      <item name="xbot_extensions.activity_photoshop.set_text_format.main" display="main" kind="function" help="/yddoc/rpa/770888082639388672">
        <item.method>main(args)</item.method>
        <item.desc>设置文字图层格式
该指令实现了设置文字图层的文字格式的功能
* @param ps_instance，
* @param layer_name，
* @param text_styles，
</item.desc>
      </item>
    </item>
    <item name="xbot_extensions.activity_photoshop.edit_smart_object" display="edit_smart_object" kind="function" help="/yddoc/rpa/770887980479209472">
      <item.method />
      <item.desc />
      <item name="xbot_extensions.activity_photoshop.edit_smart_object.get_layer_by_name" display="get_layer_by_name" kind="function" help="/yddoc/rpa/770887980479209472">
        <item.method />
        <item.desc />
      </item>
      <item name="xbot_extensions.activity_photoshop.edit_smart_object.edit_smart_object" display="edit_smart_object" kind="function" help="/yddoc/rpa/770887980479209472">
        <item.method />
        <item.desc />
      </item>
      <item name="xbot_extensions.activity_photoshop.edit_smart_object.main" display="main" kind="function" help="/yddoc/rpa/770887980479209472">
        <item.method>main(args)</item.method>
        <item.desc>编辑智能对象
该指令实现了双击编辑智能对象图层的功能
* @param ps_instance，
* @param layer_name，
</item.desc>
      </item>
    </item>
    <item name="xbot_extensions.activity_photoshop.get_active_doc" display="get_active_doc" kind="function" help="/yddoc/rpa/770887805809700864">
      <item.method />
      <item.desc />
      <item name="xbot_extensions.activity_photoshop.get_active_doc.main" display="main" kind="function" help="/yddoc/rpa/770887805809700864">
        <item.method>main(args)</item.method>
        <item.desc>获取激活的 PS 对象
该指令实现了获取当前激活的PS文档对象
* @return ps_instance，
</item.desc>
      </item>
    </item>
  </item>
</root>