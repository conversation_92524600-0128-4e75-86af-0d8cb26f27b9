import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        ps_instance = None
        layer_name = ""
        path = []
    else:
        ps_instance = args.get("ps_instance", None)
        layer_name = args.get("layer_name", "")
        path = args.get("path", [])
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="fill_content_aware", params={
            "ps_instance": ps_instance,
            "layer_name": layer_name,
            "path": path,
        }, _block=("内容识别填充", 1, "调用模块"))
        cookie_list = xbot_visual.web.get_cookies_v2(web_type="chrome", url=None, name=None, domain=None, path=None, partition_key=None, filter_secure=False, secure=False, filter_session=False, session=False, _block=("内容识别填充", 2, "获取筛选所有Cookie"))
    finally:
        pass
