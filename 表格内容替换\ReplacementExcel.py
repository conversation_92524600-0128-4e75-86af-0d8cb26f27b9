import copy
import os
import shutil
import sys
import pandas as pd
import xlwings as xw
from datetime import datetime


class ReplacementExcel:
    def __init__(self, file_name):
        self.file_name = file_name
        self.app = None  # 新增：xlwings App对象
        self.workbook = None
        self.sheet = None
        self.range_to_copy = None

    def open_sheet(self):
        try:
            # 创建App对象并设置为不可见
            self.app = xw.App(visible=False, add_book=False)
            self.app.display_alerts = False  # 警告关闭
            self.app.screen_updating = False  # 屏幕更新关闭

            # 使用App实例打开工作簿，优化资源管理
            self.workbook = self.app.books.open(self.file_name)
            self.sheet = self.workbook.sheets.active
        except Exception as e:
            print(f"打开工作簿时发生错误：{e}")

    def close_sheet(self):
        try:
            if self.workbook is not None:
                self.workbook.save()
                self.workbook.close()

            # 关闭App实例
            if self.app is not None:
                self.app.quit()
        except Exception as e:
            print(f"关闭工作簿或应用程序时发生错误：{e}")

    def column_number_to_name(self, column_number):
        column_name = ""
        while column_number > 0:
            column_number -= 1
            column_name = chr(column_number % 26 + ord('A')) + column_name
            column_number //= 26
        return column_name

    # def get_copy_range(self, start_range, end_range):
    #     try:
    #         self.range_to_copy = self.sheet.range(f"{start_range}:{end_range}")
    #         self.range_to_copy_values = self.range_to_copy.value



    #         # self.range_to_copy_df = pd.DataFrame(self.range_to_copy_values)

    #         # # 在 DataFrame 末尾插入一行空数据
    #         # empty_row = pd.DataFrame([[None] * len(self.range_to_copy_df.columns)], columns=self.range_to_copy_df.columns)
    #         # self.range_to_copy_df = pd.concat([self.range_to_copy_df, empty_row])

    #         # self.range_to_copy_values = self.range_to_copy_df.values.tolist()

    #         # 将所有值转为字符串，并避免 nan 出现
    #         df = pd.DataFrame(self.range_to_copy_values)

    #         # 转换为字符串并替换 nan
    #         df = df.astype(str).replace({'nan': '', 'NaN': ''})

    #         # 插入一行空行
    #         empty_row = pd.DataFrame([[''] * df.shape[1]], columns=df.columns)
    #         df = pd.concat([df, empty_row], ignore_index=True)

    #         # 转回列表
    #         self.range_to_copy_df = df
    #         self.range_to_copy_values = df.values.tolist()



    #         self.len_copy = len(self.range_to_copy_values)

    #     except Exception as e:
    #         pass
    #     return self.range_to_copy_values

    def get_copy_range(self, start_range, end_range):
        try:
            self.range_to_copy = self.sheet.range(f"{start_range}:{end_range}")
            self.range_to_copy_values = self.range_to_copy.value

            # 步骤1：构造 DataFrame
            df = pd.DataFrame(self.range_to_copy_values)

            # 步骤2：统一空白处理，防止 nan/None 传播
            df = df.astype(str).replace({'nan': '', 'NaN': '', 'None': ''}).fillna('')

            # 步骤3：拼接空行
            empty_row = pd.DataFrame([[''] * df.shape[1]], columns=df.columns)
            df = pd.concat([df, empty_row], ignore_index=True)

            self.range_to_copy_df = df
            self.range_to_copy_values = df.values.tolist()
            self.len_copy = len(self.range_to_copy_values)

        except Exception as e:
            print(f"读取范围出错：{e}")

        return self.range_to_copy_values


    def read_replacements_from_excel(self, rpl_file='型号替换数据.xlsx'):
        # 读取Excel文件
        df = pd.read_excel(rpl_file, header=None)
        # 获取第一行作为表头
        header = df.iloc[0]
        # 去掉第一行，重新设置DataFrame的列名
        df = df[1:]
        df.columns = header
        # 将DataFrame转换为replacement_list格式
        replacement_list = []
        for _, row in df.iterrows():
            replacements = [(str(col), str(row[col])) for col in df.columns]
            replacement_list.append(replacements)
        return replacement_list

    def replace_values(self, replacement_list):
        try:
            start_row = 4
            used_range = self.sheet.used_range
            max_col = used_range.last_cell.column
            max_row = self.sheet.range('A:A').end('down').row
            mcn = self.column_number_to_name(max_col)
            start_range = f'A{start_row}'
            end_range = f'{mcn}{max_row}'

            self.get_copy_range(start_range, end_range)

            # 整合范围并删除，优化前后范围的一致性处理
            self.sheet.range(f'{start_range}:{end_range}').api.EntireRow.Delete()

            for idx, li in enumerate(replacement_list):
                values_data = copy.deepcopy(self.range_to_copy_values)  # 深拷贝，视情况可能需要优化
                print(f"[调试] 第{idx+1}组替换规则: {li}")

                for search_value, replacement_value in li:
                    if not values_data:
                        continue
                    for i, row in enumerate(values_data):
                        if not row:
                            continue
                        for j, cell_value in enumerate(row):
                            cell_value = str(cell_value) if cell_value is not None else ""
                            if search_value in cell_value:
                                values_data[i][j] = cell_value.replace(search_value, replacement_value)

                # 一次性更新值，减少Excel操作
                # 打印列名
                col_count = len(values_data[0]) if values_data and values_data[0] else 0
                col_names = [self.column_number_to_name(i+1) for i in range(col_count)]
                # 写入前全部转为字符串，空单元格用空字符串，保证写入和打印一致
                def is_blank(cell):
                    return (
                        cell is None
                        or (isinstance(cell, float) and pd.isna(cell))
                        or (isinstance(cell, str) and cell.strip().lower() == 'nan')
                    )

                values_data_str = [
                    [cell if isinstance(cell, str) else str(cell) if cell is not None else '' for cell in row]
                    for row in values_data if row
                ]
                # 统一每一行的长度，保证写入区域和数据严格一致
                if values_data and any(values_data):
                    max_col_count = max(len(row) for row in values_data if row)
                else:
                    max_col_count = 0
                values_data_str = [
                    [str(cell) if cell is not None else '' for cell in (row + [''] * (max_col_count - len(row)))]
                    for row in values_data if row
                ]
                # 计算写入区域
                row_count = len(values_data_str)
                col_count = max_col_count
                start_col_name = 'A'
                start_cell = f'{start_col_name}{start_row}'
                end_col_name = self.column_number_to_name(col_count)
                end_row_num = start_row + row_count - 1
                end_cell = f'{end_col_name}{end_row_num}'
                write_range = f'{start_cell}:{end_cell}'
                # 只打印第一次的第一行内容
                if idx == 0 and values_data_str:
                    row = values_data_str[0]
                    row_str = ', '.join([
                        f"{col_names[col_idx]}='{cell}'" for col_idx, cell in enumerate(row)
                    ])
                    # print(f"[调试] 替换后准备写入的第1行: {row_str}")



                # 写入
                # self.sheet.range(write_range).value = values_data_str
                # 第一步：写入前清空内容并取消隐藏列
                self.sheet.range(write_range).clear_contents()
                self.sheet.range(write_range).api.EntireColumn.Hidden = False

                # 第二步：写入
                self.sheet.range(write_range).value = values_data_str

                # 第三步：读取并强制转字符串调试
                written_back = self.sheet.range(write_range).options(ndim=2, dtype=str).value






                # 写入后读取并打印
                written_back = self.sheet.range(write_range).value
                if idx == 0 and written_back and written_back[0]:
                    row = written_back[0]
                    row_str = ', '.join([
                        f"{col_names[col_idx]}='{cell if cell is not None else ''}'" for col_idx, cell in enumerate(row)
                    ])
                    # print(f"[调试] 实际写入后Excel中的第1行: {row_str}")

                # 更新start_row和max_row的逻辑应基于实际替换的情况，这里简化处理
                start_row += self.len_copy
                max_row += self.len_copy
                start_range = f'A{start_row}'
                end_range = f'{mcn}{max_row}'
        except Exception as e:
            print(f"替换值时发生错误：{e}")


def find_files():
    """
    在当前目录中查找所有不包含“副本”字样且名称不是“型号替换数据.xlsx”的xlsx和xlsm文件。
    返回:
    - file_list: 包含所有符合条件的文件路径的列表。
    """
    current_dir = os.getcwd()
    file_list = None
    try:
        # 使用列表推导式重构文件搜索逻辑，提高效率
        file_list = [filename
                     for filename in os.listdir(current_dir)
                     if (filename.endswith('.xlsx') or filename.endswith('.xlsm')) and
                     "副本" not in filename and filename != "型号替换数据.xlsx"]
        return file_list
    except Exception as e:
        sys_exit()
    return file_list


def sys_exit():
    input("按回车键退出...")
    sys.exit(0)


# 使用示例，包括异常处理等
if __name__ == "__main__":
    now = datetime.now()
    date_str = now.strftime('%m%d')
    file_replace = '型号替换数据.xlsx'
    files = find_files()  # file = 'EO-彩底挂绳.xlsm'

    for file in files:
        print(f"正在处理文件：{file}")
        base_name, ext = os.path.splitext(file)  # 分离原文件名和扩展名
        file_copy = f"{date_str}-{base_name}-副本{ext}"  # 在文件名前后添加指定字符串
        shutil.copy(file, file_copy)  # 复制文件
        excel_sheet = ReplacementExcel(file_copy)  # 创建 ExcelSheet 实例
        try:
            # 从“型号替换数据.xlsx”中读取替换列表
            replacement_list = excel_sheet.read_replacements_from_excel(file_replace)
            excel_sheet.open_sheet()
            excel_sheet.replace_values(replacement_list)
            print(f'{file} 替换完成')
        except Exception as e:
            print(f"{file} 替换失败，处理过程中遇到错误：{e}")
        finally:
            excel_sheet.close_sheet()
