import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    font_size = 0
    font_name = ""
    hex_value = ""
    if args is None:
        ps_instance = ""
        layer_name = ""
        only_visible = False
    else:
        ps_instance = args.get("ps_instance", "")
        layer_name = args.get("layer_name", "")
        only_visible = args.get("only_visible", False)
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="get_artlayer_font_info", params={
            "ps_instance": ps_instance,
            "layer_name": layer_name,
            "only_visible": only_visible,
        }, _block=("获取图层字体信息", 1, "调用模块"))
        font_size = xbot_visual.programing.variable(value_type="str", value=lambda: invoke_result[0], _block=("获取图层字体信息", 2, "设置变量"))
        font_name = xbot_visual.programing.variable(value_type="str", value=lambda: invoke_result[1], _block=("获取图层字体信息", 3, "设置变量"))
        hex_value = xbot_visual.programing.variable(value_type="str", value=lambda: invoke_result[2], _block=("获取图层字体信息", 4, "设置变量"))
    finally:
        args["font_size"] = font_size
        args["font_name"] = font_name
        args["hex_value"] = hex_value
