import atexit
import random
import socket
import time
from utils_mrc.MysqlHelper import *


class AccountManager:
    def __init__(self, source, port=None):
        atexit.register(self.release_account)
        self.source = source
        self.hostname = socket.gethostname()
        self.port = port
        self.account = {}
        self.acquire_account()

    def acquire_account(self):
        # 检查当前实例是否已经持有账户，如果有则先释放
        if self.account:
            self.release_account()

        # 尝试获取一个未使用的账户，并锁定
        sql = """
            SELECT * FROM rpa_info 
            WHERE source LIKE %s AND host_name = %s AND port = %s AND is_used = 0
            UNION ALL
            SELECT * FROM rpa_info 
            WHERE source LIKE %s AND host_name = %s AND is_used = 0
            UNION ALL
            SELECT * FROM rpa_info 
            WHERE source LIKE %s AND is_used = 0
            LIMIT 1
        """
        time.sleep(random.uniform(1, 3))
        account = MS100.get_dict_one(sql, (
            self.source, self.hostname, self.port,
            self.source, self.hostname,
            self.source,))
        if account:
            self.account = account
            self.port = self.port or account['port']  # 如果不指定端口，则使用账户的端口
            # 更新账户状态为已使用，并记录主机名和进程ID
            update_sql = """
                UPDATE rpa_info SET is_used = 1, host_name = %s, port = %s
                WHERE id = %s and is_used = 0
            """
            MS100.update(update_sql, (self.hostname, self.port, account['id'],))
            self.account['port'] = self.port
            logging(f"锁定的帐户和详细信息:{self.account}")
            return True
        else:
            exit(f'{self.source} 当前无可用账号！')
        return False

    def release_account(self):
        if self.account:
            # 更新账户状态为未使用
            update_sql = """
                UPDATE rpa_info SET is_used = 0 WHERE id = %s
            """
            MS100.update(update_sql, (self.account['id'],))


# 使用 AccountManager 类
if __name__ == "__main__":
    account = AccountManager('seller_sprite_user_cj%').account
    # print("锁定的帐户和详细信息:", account)
    input("按任意键继续...")
