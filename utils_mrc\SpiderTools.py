import threading
import time
import requests
from itertools import cycle
from DrissionPage import SessionPage, SessionOptions
from utils_mrc.MysqlHelper import *
import types


class SessionPageManager:
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36 Edg/132.0.0.0',
    }

    def __init__(self, proxy_type=0):
        self.proxy_type = int(proxy_type)
        so = SessionOptions()
        so.set_headers(SessionPageManager.headers)
        self.so = so
        self.page = SessionPage(session_or_options=self.so)
        self.open_proxy(self.proxy_type)

    def open_proxy(self, enable=1, check=False):
        """
        设置或检查代理服务器。

        参数:
        - enable: 控制使用哪种代理服务器的标志。1 使用第一种代理，2 使用第二种代理，其他值 不使用代理。
        - check: 布尔值，指示是否在设置代理后进行代理有效性检查。

        此函数根据 enable 参数的选择，设置相应的代理服务器。如果 check 参数为 True，则在设置完代理后调用检查代理的函数。
        """

        # 根据 enable 参数选择并设置代理服务器
        if enable == 1:
            # 获取并设置第一种代理服务器
            proxies_http = IPPool.proxies['http']
            self.page.set.proxies(proxies_http, proxies_http)
            logging('切换隧道IP代理！')
        elif enable == 2:
            # 获取并设置第二种代理服务器
            proxies_http = IPPool.proxies2['http']
            self.page.set.proxies(proxies_http, proxies_http)
            logging('切换固定IP代理！')
        else:
            # 不使用代理
            self.page.set.proxies()
            logging('已关闭代理')

        # 如果需要，进行代理有效性检查
        if check:
            logging('正在检测代理有效性...')
            self.check_proxy()

    def check_proxy(self):
        n = self.page.session
        r = n.get('https://whois.pconline.com.cn/ip.jsp')
        print(r.text.strip())

    def get_html(self):
        try:
            return self.page.html or self.page.response.text
        except:
            return ''

    def set_cookies(self, cookies):
        if isinstance(cookies, (dict, list)):
            self.page.set.cookies(cookies)
        elif isinstance(cookies, str):
            cookies = json.loads(cookies)
            self.page.set.cookies(cookies)


class IPPool:
    cycle_list = [0, 1, 2]  # 代理类型 0不使用；1隧道；2固定ip
    cycle_proxy_type = cycle(cycle_list)  # 切换代理类型循环器

    # 隧道域名:端口号
    tunnel = "y406.kdltps.com:15818"
    # 用户名密码方式
    username = "t14832880762817"
    password = "pts6bl3l"
    proxies = {
        "http": "http://%(user)s:%(pwd)s@%(proxy)s/" % {"user": username, "pwd": password, "proxy": tunnel},
        "https": "http://%(user)s:%(pwd)s@%(proxy)s/" % {"user": username, "pwd": password, "proxy": tunnel}
    }

    ip_address = "*************"
    ip_port = "26868"
    proxy_username = "czc01"
    proxy_password = "WEsadby@1asddasdom@12234232423"

    proxies2 = {
        "http": "http://%(user)s:%(pwd)s@%(ip)s:%(port)s/" % {"user": proxy_username, "pwd": proxy_password, "ip": ip_address,
                                                              "port": ip_port},
        "https": "http://%(user)s:%(pwd)s@%(ip)s:%(port)s/" % {"user": proxy_username, "pwd": proxy_password, "ip": ip_address,
                                                               "port": ip_port}
    }

    headers = SessionPageManager.headers

    session2 = requests.session()
    session2.headers.update(SessionPageManager.headers)
    session2.proxies = proxies2

    session = requests.session()
    session.proxies = proxies
    session.headers.update(SessionPageManager.headers)

    def __init__(self):
        self.proxies = self.get_proxies()
        pass

    @classmethod
    def get_proxies(cls):
        proxies = {
            "http": "http://%(user)s:%(pwd)s@%(proxy)s/" % {"user": cls.username, "pwd": cls.password, "proxy": cls.tunnel},
            "https": "http://%(user)s:%(pwd)s@%(proxy)s/" % {"user": cls.username, "pwd": cls.password, "proxy": cls.tunnel}
        }
        return proxies

    @staticmethod
    def test_proxy(t=1):
        if t ==1:
            rs = IPPool.session.get('https://whois.pconline.com.cn/ipJson.jsp')
        else:
            rs = IPPool.session2.get('https://whois.pconline.com.cn/ipJson.jsp')
        print(rs.text.strip())


class IPGetter_old:
    def __init__(self):
        self.proxies = ''
        self.authKey = "366B3BA3"
        self.password = "B163308B370C"
        self.session = requests.Session()
        self.session.headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
        self.url = f'https://exclusive.proxy.qg.net/replace?key={self.authKey}&num=1&area=&isp=0&format=json&distinct=true&keep_alive=1440'
        self.stauts = self.get_ip()

    def get_ip(self):
        return self._get_ip()

    def _get_ip(self):
        ips = MS.get_dict_one("SELECT * FROM rpa.`ip_info` where status = 1 ORDER BY id desc limit 1")
        if ips:
            self._update_proxies(ips)
            return self.proxies

        return self._attempt_to_get_new_ip()

    def _attempt_to_get_new_ip(self):
        for _ in range(5):
            try:
                res = self.session.get(self.url)
                json_data = res.json()
                if json_data.get('code') == 'SUCCESS' and json_data.get('data', {}).get('ips'):
                    self._process_response(json_data)
                    return self.proxies
                elif self._handle_error(json_data) is False:
                    return None
            except Exception as e:
                print(e)
                time.sleep(2)
        logging('获取代理IP失败，重试5次后放弃')
        return None

    def _update_proxies(self, ips):
        self.proxies = ips['server'] if ips['server'] else f"{ips['ip']}:{ips['port']}"
        # logging(f"代理IP已更新为：{self.proxies}")

    def _process_response(self, json_data):
        ip_ports = json_data.get('data', {}).get('ips', [])
        if not ip_ports:
            self.proxies = None
            logging('获取代理IP失败，没有可用的IP')
            return
        self.proxies = ip_ports[0]['server']
        self._insert_into_database(ip_ports[0])

    def _insert_into_database(self, proxy):
        sql = """
            INSERT INTO rpa.ip_info (server, ip, port, expire_time, city, isp)
            select %s, %s, %s, %s, %s, %s FROM DUAL
            WHERE (SELECT COUNT(1) FROM `rpa`.`ip_info` WHERE status = 1) = 0;
            """
        # 执行SQL语句
        MS.insert(sql, (
            proxy['server'],
            proxy['server'].split(':')[0],
            proxy['server'].split(':')[1],
            proxy['deadline'] or '',
            proxy['area'] or '',
            proxy['isp'] or '',
        ))
        # if MS.err:
        #     print(MS.err)

    def _handle_error(self, json_data):
        if json_data.get('code') == 'DELETE_LIMIT_EXCEEDED':
            logging('释放频率超出限制，等待3秒')
            time.sleep(3)
        elif json_data.get('code') == 'UNAVAILABLE_KEY':
            logging('Key不可用，已过期或被封禁！即将使用本地IP!')
            return False
        else:
            logging(f'获取代理IP失败：{json_data.get("message")}')

    def close_ip(self, server=None):
        if not server:
            server = self.proxies
        if isinstance(server, dict):
            http = server['http']
            server = http.split('@')[1]
        MS.update(f'update rpa.ip_info set status = 0,content="失效" where status = 1 and server = "{server}"')

    def get_proxies(self):
        # 账密模式
        proxyUrl = "http://%(user)s:%(password)s@%(server)s" % {
            "user": self.authKey,
            "password": self.password,
            "server": self.proxies,
        }
        proxies = {
            "http": proxyUrl,
            "https": proxyUrl,
        } if self.stauts else None
        return proxies


import types

def log_request(self, method, url, **kwargs):
    print(f"[代理请求] method={method}, url={url}, proxies={self.proxies}")
    try:
        resp = self._orig_request(method, url, **kwargs)
        print(f"[代理响应] 状态码: {resp.status_code}, 内容: {resp.text[:200]}")
        return resp
    except Exception as e:
        print(f"[代理异常] {e}")
        raise

for s in [IPPool.session, IPPool.session2]:
    s._orig_request = s.request
    s.request = types.MethodType(log_request, s)
# === END ===


if __name__ == '__main__':
    proxies = IPPool.proxies
    print(proxies)
    IPPool.test_proxy()
    sp = SessionPageManager()
    page = sp.page
    page.get('https://whois.pconline.com.cn/ip.jsp')
    print(page.html)
