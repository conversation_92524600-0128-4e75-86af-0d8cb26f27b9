import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        ps_instance = None
        mode = ""
        name = ""
        save_path = ""
    else:
        ps_instance = args.get("ps_instance", None)
        mode = args.get("mode", "")
        name = args.get("name", "")
        save_path = args.get("save_path", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="invoke_module", package=__name__, function="export_as_png", params={
            "ps_instance": ps_instance,
            "mode": mode,
            "name": name,
            "save_path": save_path,
        }, _block=("导出图层为PNG", 1, "调用模块"))
    finally:
        pass
