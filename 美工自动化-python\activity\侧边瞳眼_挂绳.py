import os
from task.base_activity import <PERSON><PERSON><PERSON><PERSON><PERSON>
from task.base_activity import <PERSON><PERSON><PERSON><PERSON>
from task.base_activity import <PERSON><PERSON>eader
from task.base_activity import <PERSON>FileHandler
from task.feishu_notify import send_feishu_notify
import win32com.client as win

def execute_jsx(script):
    photoshop = win.Dispatch("Photoshop.Application")
    result = photoshop.DoJavaScript(script)
    return result

def get_layer_group_names(layer_set):
    names = []
    for sub_layer_set in layer_set.layerSets:
        sub_layer_name = sub_layer_set.name
        names.append(sub_layer_name)
        names.extend(get_layer_group_names(sub_layer_set))
    return names

def generate_jsx_script(substring_to_match, hidden_layer_names, shown_layer_names):
    return f"""
    var doc = app.activeDocument;

    function get_layer_group_names(layer_set) {{
        var names = [];
        for (var i = 0; i < layer_set.layerSets.length; i++) {{
            var sub_layer_set = layer_set.layerSets[i];
            var sub_layer_name = sub_layer_set.name.replace(/\\d+/g, '');  // 去掉数字部分

            // 检查图层名称是否应该隐藏
            if ({' || '.join(f'sub_layer_name === "{name}"' for name in hidden_layer_names)}) {{
                sub_layer_set.visible = false;  // 设置图层组为隐藏
            }}

            // 检查图层名称是否应该显示
            if ({' || '.join(f'sub_layer_name === "{name}"' for name in shown_layer_names)}) {{
                sub_layer_set.visible = true;  // 设置图层组为显示
            }}

            names.push(sub_layer_name);
            names = names.concat(get_layer_group_names(sub_layer_set));
        }}
        return names;
    }}

    var layerGroupNames = [];

    for (var i = 0; i < doc.layerSets.length; i++) {{
        var layerSet = doc.layerSets[i];
        var groupName = layerSet.name.replace(/\\d+/g, '');  // 去掉数字部分

        // 检查图层组名称是否应该隐藏
        if ({' || '.join(f'groupName === "{name}"' for name in hidden_layer_names)}) {{
            layerSet.visible = false;  // 设置图层组为隐藏
        }}

        // 检查图层组名称是否应该显示
        if ({' || '.join(f'groupName === "{name}"' for name in shown_layer_names)}) {{
            layerSet.visible = true;  // 设置图层组为显示
        }}

        layerGroupNames.push(groupName);
        layerGroupNames = layerGroupNames.concat(get_layer_group_names(layerSet));
    }}

    layerGroupNames.join(',');
    """

def run(base_folder,excel_file):
    img_list = ['黑色+绳黑金', '草紫+绳紫彩虹', '米白+绳白灰', '白色+绳白灰', '粉色+绳粉黑', '草绿+绳草绿', '米白+绳米白', '草紫+绳草紫', '浅紫+绳浅紫', '黑色+绳黑', '红色+绳红', '粉色+绳玫瑰金', '粉色+绳浅粉']

    # 初始化文件操作对象
    file_handle = DirectoryReader()
    # 初始化日志对象
    logger = BaseLogger()

    # 初始化psd文件处理对象
    ps_file_handle = PSFileHandler()
    # 初始化表格对象
    # excel_handler = ExcelHandler(r"F:\美工自动化\0508-百事队-彩底磨砂挂绳彩绘 A+页面-自动化需求-翁嘉琪\\彩底磨砂挂绳彩绘 A+页面.xlsx")
    excel_handler = ExcelHandler(excel_file)
    
    ps_handler = ()

    # 打开表格
    excel_obj = excel_handler.open_excel()
    
    # 读取表格文件路径
    psd_config = excel_handler.read_sheet_data(excel_obj, "文件路径")

    print("psd_config内容：", psd_config)
    # 健壮性判断：必须有3行，每行至少2列
    if len(psd_config) < 3 or any(len(row) < 2 for row in psd_config[:3]):
        print("字段不全，psd_config内容：", psd_config)
        return

    # 检查 psd_config 是否有缺失
    field_names = ["psd文件夹", "tif文件夹", "成品文件夹"]
    missing_fields = []
    for i, field in enumerate(field_names):
        if len(psd_config) <= i or not psd_config[i][1]:
            missing_fields.append(field)
    if missing_fields:
        msg = f"以下字段在表格【文件路径】页签中不存在或为空: {', '.join(missing_fields)}"
        logger.log_error(msg)
        send_feishu_notify("存在缺失文件路径", msg)
        return


    psd_conf = {
        "psd_folder":psd_config[0][1],
        "tif_folder":psd_config[1][1],
        "result_folder":psd_config[2][1]
    }
    # # 读取任务表格数据
    psd_data = excel_handler.read_sheet_data(excel_obj, "图案型号文件名")
    
    logger.log_info(psd_conf)
    # 当前表格行
    # # 循环遍历数据
    for index, row in enumerate(psd_data):
        if index == 0:
            continue
        current_num = index + 1
        logger.log_info(f"行数: {current_num}, 数据: {row}")
        # 判断路径是否存在
        result_folder = os.path.join(psd_conf["result_folder"], row[2] + row[3])

        if not file_handle.ensure_folder_exists(result_folder):
            logger.log_error(f"路径不存在: {result_folder}")
            continue
        else:
            logger.log_info(f"成品路径正常: {result_folder}")
        psd_file = os.path.join(psd_conf["psd_folder"], row[1])
        logger.log_info(f"psd文件路径: {psd_file}")
        # 打开psd文件

        ps_file_handle.open_ps_file(psd_file)

        tif_file = os.path.join(psd_conf["tif_folder"], row[0])
        
        color_config = row[4]

        logger.log_info(f"tif文件路径: {tif_file}")

        replace_one = "无内容"
        

        logger.log_info(f"颜色配置: {color_config}")
        

        # 定义要执行的 JSX 脚本
        substring_to_match = "替换内容1"  # Replace with the substring you want to match

        jsx_script = f"""
        var doc = app.activeDocument;
        var matchedLayerNames = [];

        function check_and_add_name(layerName) {{
            if (layerName.indexOf("{substring_to_match}") !== -1) {{
                matchedLayerNames.push(layerName);
            }}
        }}

        function get_layer_group_names(layer_set) {{
            for (var i = 0; i < layer_set.layers.length; i++) {{
                check_and_add_name(layer_set.layers[i].name);
            }}

            for (var i = 0; i < layer_set.layerSets.length; i++) {{
                var sub_layer_set = layer_set.layerSets[i];
                check_and_add_name(sub_layer_set.name);
                get_layer_group_names(sub_layer_set);
            }}
        }}

        for (var i = 0; i < doc.layers.length; i++) {{
            check_and_add_name(doc.layers[i].name);
        }}

        for (var i = 0; i < doc.layerSets.length; i++) {{
            check_and_add_name(doc.layerSets[i].name);
            get_layer_group_names(doc.layerSets[i]);
        }}

        matchedLayerNames.join(',');
        """

        # 执行 JSX 脚本
        result = execute_jsx(jsx_script)

        # 将结果字符串转换为 Python 列表
        matched_layer_names = result.split(',')
        print(matched_layer_names)

        # 打印匹配的图层名称
        for name in matched_layer_names:
            if "替换内容1" == name:
                replace_one = name
        
        if replace_one == "无内容":
            logger.log_error(f"没有找到匹配的图层: {replace_one}")
            continue
        else:   
            logger.log_info(f"找到匹配的图层: {replace_one}")
            # 替换图层名称
            ps_file_handle.replace_img(replace_one, tif_file)
            logger.log_info(f"替换图层名称: {replace_one} -> {row[3]}")

        # 从 img_list 中移除与 color_config 一致的项
        hidden_layer_names = img_list
        if color_config in hidden_layer_names:
            hidden_layer_names.remove(color_config)
        shown_layer_names = [color_config]

        # 定义 JSX 脚本
        jsx_script = generate_jsx_script(substring_to_match, hidden_layer_names, shown_layer_names)

        # 执行 JSX 脚本
        result = execute_jsx(jsx_script)

        file_list = excel_handler.read_sheet_data(excel_obj, "文件夹图层名")
        for file_item in file_list:
            export_file_path = os.path.join(result_folder, row[2] + row[3] + "-" + file_item[0] + ".jpg")
            logger.log_info(f"导出图层={file_item[0]} ==> {export_file_path}")
            ps_file_handle.export_psd_to_jpg_with_quality(app=win.Dispatch("Photoshop.Application"), export_file_path=export_file_path, layer_name=file_item[0])
        break
