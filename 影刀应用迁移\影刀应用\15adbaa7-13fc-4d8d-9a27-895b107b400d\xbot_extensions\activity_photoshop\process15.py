import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        ps_instance = None
        layer_name = ""
        hex_value = ""
    else:
        ps_instance = args.get("ps_instance", None)
        layer_name = args.get("layer_name", "")
        hex_value = args.get("hex_value", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="set_rectangle_tool_fill_color", params={
            "ps_instance": ps_instance,
            "layer_name": layer_name,
            "hex_value": hex_value,
        }, _block=("修改颜色填充图层", 1, "调用模块"))
    finally:
        pass
