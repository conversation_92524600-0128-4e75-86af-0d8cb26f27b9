from .import package
import xbot_visual

def main(输出路径):
    """
    保存小于5m图片-JPG-高质量
    
    * @param 输出路径，
    """
    outputs = []
    inputs = {"输出路径":输出路径}
    extension_module, activity_func = xbot_visual.process.activity_entry("xbot_extensions.activity_1ece7d61.main", __name__)
    try:
        return xbot_visual.process.run(process="xbot_extensions.activity_1ece7d61.main",package=__name__,inputs=inputs, outputs=outputs)
    finally:
        xbot_visual.process.replace_activity_module_to_entry_method("xbot_extensions.activity_1ece7d61.main", extension_module, activity_func)

