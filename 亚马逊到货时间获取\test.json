
CREATE TABLE `task_listing` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `app_id` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '父任务ID',
  `cid` int(11) NOT NULL DEFAULT '0' COMMENT '定时任务表ID',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '任务类型 1:账单 2:广告费',
  `platform` varchar(60) NOT NULL DEFAULT '' COMMENT '平台',
  `site` varchar(16) NOT NULL DEFAULT '' COMMENT '站点',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT '任务名称',
  `asins` text COMMENT '任务页面链接/内容',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户',
  `username` varchar(40) NOT NULL DEFAULT '' COMMENT '用户名称',
  `priority` int(11) NOT NULL DEFAULT '0' COMMENT '优先级，数值越大，越优先执行',
  `page` int(11) NOT NULL DEFAULT '1' COMMENT '抓取页数',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 1待执行 2执行中 10已完成 20异常 99已取消',
  `status_attr` tinyint(1) NOT NULL DEFAULT '0' COMMENT '子状态-选品数据清洗状态 0待清洗 1已清洗',
  `result` varchar(1024) NOT NULL DEFAULT '' COMMENT '执行结果',
  `platform_num` int(11) NOT NULL DEFAULT '0' COMMENT '平台总数',
  `task_num` int(11) NOT NULL DEFAULT '0' COMMENT '任务总数',
  `run_num` tinyint(1) NOT NULL DEFAULT '0' COMMENT '执行次数',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `datetime` int(11) NOT NULL DEFAULT '0' COMMENT '归属时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `execute_time` int(11) NOT NULL DEFAULT '0' COMMENT '执行开始时间',
  `done_time` int(11) NOT NULL DEFAULT '0' COMMENT '执行完成时间',
  `uptime_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `PLATFORM` (`platform`) USING BTREE,
  KEY `START_END_TIME` (`start_time`,`end_time`) USING BTREE,
  KEY `datetime` (`datetime`) USING BTREE,
  KEY `idx_type` (`type`) USING BTREE,
  KEY `app_id` (`app_id`) USING BTREE,
  KEY `user_id` (`user_id`,`site`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13352 DEFAULT CHARSET=utf8mb4 COMMENT='爬虫任务-listing相关任务';
89	13	0	0	1	amazon	DE	recuOxiIUkaW68	B0FCBBQ92C	0		0	1	0	0		0	0	0	1753718400	1753804799	1753718400	1753771510	0	0	2025-07-29 14:45:10
90	13	0	0	1	amazon	DE	recuOxiIUkph0F	B0FCBHN8Q2	0		0	1	0	0		0	0	0	1753718400	1753804799	1753718400	1753771510	0	0	2025-07-29 14:45:10



CREATE TABLE `data_amazon_asin_attr` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `tenant_id` int(11) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `company_id` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '公司ID',
  `unique_id` varchar(100) NOT NULL DEFAULT '' COMMENT '唯一标识',
  `app_id` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `task_id` int(11) NOT NULL DEFAULT '0' COMMENT '任务ID',
  `datetime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所属时间',
  `task_time` int(11) NOT NULL DEFAULT '0' COMMENT '任务时间',
  `data_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态 1正常2异常3无数据',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户',
  `username` varchar(40) NOT NULL DEFAULT '' COMMENT '用户名称',
  `platform_account` varchar(256) NOT NULL DEFAULT '' COMMENT '店铺/卖家名称',
  `site` varchar(10) NOT NULL DEFAULT '' COMMENT '站点',
  `asin` varchar(10) NOT NULL DEFAULT '' COMMENT '亚马逊asin',
  `status` tinyint(1) NOT NULL DEFAULT '10' COMMENT 'asin状态 可售10,下架99,不可售20',
  `has_cart` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有购物车',
  `title` varchar(1024) NOT NULL DEFAULT '' COMMENT '标题',
  `keywords` varchar(1024) NOT NULL DEFAULT '' COMMENT '关键词(逗号隔开)',
  `short_desc1` varchar(1024) NOT NULL DEFAULT '' COMMENT '卖点1',
  `short_desc2` varchar(1024) NOT NULL DEFAULT '' COMMENT '卖点2',
  `short_desc3` varchar(1024) NOT NULL DEFAULT '' COMMENT '卖点3',
  `short_desc4` varchar(1024) NOT NULL DEFAULT '' COMMENT '卖点4',
  `short_desc5` varchar(1024) NOT NULL DEFAULT '' COMMENT '卖点5',
  `detail` text COMMENT '详情',
  `tech_spec` json NOT NULL COMMENT '技术规格',
  `product_desc` text COMMENT '产品说明',
  `detail_seller_info` text COMMENT '卖家信息',
  `img_url` varchar(1024) NOT NULL COMMENT '主图链接',
  `url` varchar(256) NOT NULL DEFAULT '' COMMENT '链接',
  `primary_delivery_message` varchar(256) NOT NULL DEFAULT '' COMMENT '主要配送信息',
  `primary_delivery_message_cleaned` varchar(256) NOT NULL DEFAULT '' COMMENT '清洗后的主要配送信息',
  `secondary_delivery_message` varchar(256) NOT NULL DEFAULT '' COMMENT '次要配送信息',
  `secondary_delivery_message_cleaned` varchar(256) NOT NULL DEFAULT '' COMMENT '清洗后的次要配送信息',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_id` (`unique_id`) USING BTREE COMMENT '唯一ID',
  KEY `task_id` (`task_id`) USING BTREE,
  KEY `datetime` (`datetime`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=971463 DEFAULT CHARSET=utf8mb4 COMMENT='亚马逊基础属性数据';
6670	0	1	B0B3LR5NVT_de	5	3	1723478400	1723600436	1	18	zc		de	B0B3LR5NVT	10	1									null				https://www.amazon.de/dp/B0B3LR5NVT?psc=1					1723600436	2024-08-14 09:53:52
6671	0	1	B0C6JK33QR_de	5	3	1723478400	1723600436	1	18	zc		de	B0C6JK33QR	10	1									null				https://www.amazon.de/dp/B0C6JK33QR?psc=1					1723600436	2024-08-14 09:53:52