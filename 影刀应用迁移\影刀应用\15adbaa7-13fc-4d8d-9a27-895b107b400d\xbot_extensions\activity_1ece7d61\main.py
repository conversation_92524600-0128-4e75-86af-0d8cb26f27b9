import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        输出路径 = ""
    else:
        输出路径 = args.get("输出路径", "")
    try:
        _ = xbot_visual.process.run(process="ps_auto", package=__name__, inputs={
            "output_path": 输出路径,
            }, outputs=[
        ], _block=("main", 1, "调用流程"))
    finally:
        pass
