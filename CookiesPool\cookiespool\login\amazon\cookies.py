import json
import random
import re
import time
import traceback
from urllib.parse import urlparse

import amazoncaptcha
from DrissionPage import ChromiumOptions, WebPage, SessionOptions, SessionPage
from DrissionPage.errors import PageDisconnectedError, ContextLostError
from utils_mrc.pub_fun import clear_plugin_cache_port, AmazonConfig
from utils_mrc.RedisClient import RedisClient

db_cookies = RedisClient('cookies', 'amazon')


# us = RedisClient('cookies', 'amazonUS').random()
# us = json.loads(us)


class AmazonCookies:
    home_url = 'https://www.amazon.de/'

    def __init__(self, port):
        self.port = port
        co = ChromiumOptions()
        co.remove_extensions()
        clear_plugin_cache_port(self.port)
        co.set_local_port(port)
        # co.headless()  # 无头模式
        co.set_browser_path(f'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe')
        self.co = co
        self.browser = WebPage(chromium_options=self.co)
        self.browser.set.auto_handle_alert()
        cookies = db_cookies.get(self.port)
        if cookies:
            self.browser.get(self.home_url)
            self.browser.set.cookies(cookies)
        # self.browser.set.window.mini()
        # self.browser.set.window.hide()
        self.page = self.browser
        self.err_site = set()
        self.cur_site = ''

    def open(self, site=''):
        """
        打开网页输入用户名密码并点击
        :return: None
        """
        if not site:
            for site in AmazonConfig.COMMON_SITE:
                self.cur_site = site
                self.cur_url = f'https://www.amazon.{site}/'
                print(f'正在切换站点：{site}', end=' ')
                self.page.get(self.cur_url)
                if not self.check_page():
                    print(f'站点：{site} 切换异常')
                    self.err_site.add(site)
        else:
            self.page.set.cookies.clear()
            self.cur_site = site
            self.cur_url = f'https://www.amazon.{AmazonConfig.get_amz_site(site)}/'
            print(f'正在切换站点：{site}', end=' ')
            self.page.get(self.cur_url)
            if not self.check_page():
                print(f'站点：{site} 切换异常')
                self.err_site.add(site)

    def check_addr_code(self):
        if '.amazon.' in self.page.url and 'glow-ingress-block' in self.page.html:
            ingress = self.page('#glow-ingress-line2') or self.page('#glow-ingress-block')
            element_text = ingress.text
            isin = any(item in element_text for item in AmazonConfig.ADDRESS_CODE)
            if not isin:
                print(f'当前地址：{element_text}与目标地址不匹配,需要修改！')
            return isin

    def change_zipcode(self):
        try:
            if '.amazon.' in self.page.url:
                ingress = self.page('#glow-ingress-block', timeout=1)
                if ingress:
                    element_text = ingress.text
                    if any(item in element_text for item in AmazonConfig.ADDRESS_CODE):
                        return True
                    js_code = """
                         var zipcodedict = {
                        'co.uk': ['W1S 3PR', 'W1S 3'],
                        'es': '28035',
                        'it': '00185',
                        'fr': '75020',
                        'de': '14199',
                        'com': ['90001', '10001'] // 以数组形式存储多个匹配值
                    };
            
                    var domain = window.location.hostname; // 获取当前网页的域名
                    let zipcodeText = document.querySelector('#nav-global-location-popover-link').innerText;
                    console.log('zipcodeText:', zipcodeText);
                    console.log('domain:', domain);
            
                    let matchFound = false;
                    var zipcode = '90001';
            
                    // 遍历字典的键
                    var domain_code = domain.split("amazon.")[1]
                    const value = zipcodedict[domain_code];
            
                    // 判断是否为数组
                    if (Array.isArray(value)) {
                        // 如果值是数组，判断元素值是否包含在数组中的任何一个值
                        if (!value.some(item => zipcodeText.includes(item))) {
                            matchFound = true;
                            zipcode = value[0];
                        }
                    } else {
                        // 如果值不是数组，直接比较元素值和键的值
                        if (zipcodeText.indexOf(value) == -1) {
                            zipcode = value;
                            matchFound = true;
                        }
                    }
            
                    console.log('zipcode:', zipcode, 'matchFound', matchFound);
            
                    // 包含10001或者90001
                    if (matchFound) {
                        const get_data = document.querySelector("#nav-global-location-data-modal-action").getAttribute('data-a-modal');
                        console.log(get_data);
                        console.log(JSON.parse(get_data)["url"], JSON.parse(get_data)['ajaxHeaders']["anti-csrftoken-a2z"])
                        async function fetchData() {
                            const response = await fetch("https://" + domain + JSON.parse(get_data)["url"], {
                                headers: {
                                    "accept": "text/html,*/*",
                                    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                                    "anti-csrftoken-a2z": JSON.parse(get_data)['ajaxHeaders']["anti-csrftoken-a2z"],
                                    "device-memory": "8",
                                    "downlink": "10",
                                    "dpr": "1",
                                    "ect": "4g",
                                    "rtt": "200",
                                    "sec-ch-device-memory": "8",
                                    "sec-ch-dpr": "1",
                                    "sec-ch-ua": '"Chromium";v="116", "Not)A;Brand";v="24", "Microsoft Edge";v="116"',
                                    "sec-ch-ua-mobile": "?0",
                                    "sec-ch-ua-platform": '"Windows"',
                                    "sec-ch-viewport-width": "881",
                                    "sec-fetch-dest": "empty",
                                    "sec-fetch-mode": "cors",
                                    "sec-fetch-site": "same-origin",
                                    "viewport-width": "881",
                                    "x-requested-with": "XMLHttpRequest"
                                },
                                referrer: location.href,
                                referrerPolicy: "strict-origin-when-cross-origin",
                                credentials: "include"
                            });
            
                            const html = await response.text();
                            const parser = new DOMParser();
                            const doc = parser.parseFromString(html, "text/html");
                            const scriptElement = doc.querySelector("div script");
            
                            if (scriptElement) {
                                const scriptContent = scriptElement.textContent;
                                console.log(scriptContent)
            
                                // 判断标签内容是否包含 CSRF_TOKEN
                                if (scriptContent.indexOf('CSRF_TOKEN') !== -1) {
                                    // 提取 CSRF_TOKEN 的值
                                    console.log("匹配到安全sign")
                                    var regex1 = /CSRF_TOKEN\s*:\s*"([^"]+)"/;
                                    var match = scriptContent.match(regex1);
                                    var csrfToken = match[1];
                                    console.log('Extracted CSRF_TOKEN:', csrfToken);
                                    const url = JSON.parse(get_data)["url"];
                                    const regex = /pageType=([^&]+)/;
                                    const match_url = url.match(regex);
            
                                    const pageType = match_url[1];
            
                                    console.log("pageType =", pageType);
                                    var pageurl = location.href;
                                    var storecontext = "generic"
                                    if (pageurl.indexOf("/dp") != -1) {
                                        storecontext = "wireless"
                                    }
                                    change_zipcode(zipcode, storecontext, pageType, csrfToken)
            
                                    // 在这里可以根据需要处理 CSRF_TOKEN 的值
                                }
                            }
                        }
                        fetchData();
            
                    }
                     async function change_zipcode(zipcode, storecontext, pageType, csrftoken) {
                        const url = "https://" + domain + "/portal-migration/hz/glow/address-change?actionSource=glow";
            
                        const data = {
                            "locationType": "LOCATION_INPUT",
                            "zipCode": zipcode,
                            "storeContext": storecontext,
                            "deviceType": "web",
                            "pageType": pageType,
                            "actionSource": "glow"
                        };
            
                        const response = await fetch(url, {
                            method: "POST",
                            headers: {
                                "accept": "text/html,*/*",
                                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                                "anti-csrftoken-a2z": csrftoken,
                                "content-type": "application/json",
                                "sec-ch-ua": '"Chromium";v="116", "Not)A;Brand";v="24", "Microsoft Edge";v="116"',
                                "sec-ch-ua-mobile": "?0",
                                "sec-ch-ua-platform": '"Windows"',
                                "sec-ch-ua-platform-version": "10.0.0",
                                "sec-fetch-dest": "empty",
                                "sec-fetch-mode": "cors",
                                "sec-fetch-site": "same-origin",
                                "x-requested-with": "XMLHttpRequest"
                            },
                            referrerPolicy: "strict-origin-when-cross-origin",
                            body: JSON.stringify(data),
                            credentials: "include"
                        });
            
                        const result = await response.json();
                        console.log(result); // 可根据需要处理返回结果
                        location.reload(); //刷新页面
                    }
                        """
                    for _ in range(3):
                        self.page.run_js_loaded(js_code)
                        time.sleep(2)
                        if self.check_addr_code():
                            return True
                    print('us地址修改失败，请重试')
        except:
            traceback.print_exc()
            pass

    def check_page(self):
        try:
            for i in range(3):  # 验证码最大重试3次
                if 'id="captchacharacters"' in self.page.html:  # 验证码
                    # https://www.amazon.com/errors/validateCaptcha
                    print('检测到验证码页面...', end=' ')
                    img_url = self.page('c=[class="a-row a-text-center"] img').attr('src')
                    # print(img_url)
                    print(f'验证码第{i + 1}次识别中...', end=' ')
                    captcha_result = amazoncaptcha.AmazonCaptcha.fromlink(img_url).solve()
                    print('本次识别结果为：', captcha_result)
                    self.page('#captchacharacters').input(captcha_result + '\n')
                    continue

                while 'ref=cs_503_link' in self.page.html:
                    e_503 = self.page('@href:ref=cs_503_link', timeout=2)
                    if e_503:
                        e_503.click()
                        self.page.wait(1)
                    self.page.refresh()
                    self.page.wait.load_start(raise_err=False)
                    self.page.get(self.cur_url)

                self.change_zipcode()  # 检查并切换地址
                return True  # 已检测到可能的异常全处理完则跳出页面页面检测
        except PageDisconnectedError:
            self.page = WebPage(chromium_options=self.co)
            return self.check_page()
        except ContextLostError:
            self.page = WebPage(chromium_options=self.co)
            self.page.refresh()
            self.page.wait.load_start(raise_err=False)
            return self.check_page()

    def login_successfully(self):
        """
        判断是否登录成功
        :return:
        """
        try:
            if '.amazon.' in self.page.url:
                time.sleep(3)
                ingress = self.page('#glow-ingress-block', timeout=1)
                if ingress:
                    element_text = ingress.text
                    return any(item in element_text for item in AmazonConfig.ADDRESS_CODE)
            return True
        except Exception:
            return False

    def get_cookies(self):
        """
        获取Cookies
        :return:
        """
        return self.page.cookies()

    def main(self, site=''):
        """
        破解入口
        :return:
        """
        self.open(site)
        cookies = self.get_cookies()
        self.browser.quit()
        if not self.err_site:
            return {
                'status': 1,
                'content': cookies
            }
        else:
            print(f'部分地址切换失败！{self.err_site}')
            return {
                'status': 3,
                'content': '登录失败'
            }


class AmazonCookiesSession:
    home_url = 'https://www.amazon.de/'

    def __init__(self, port=None):
        self.cur_country = None
        self.cur_address = None

        so = SessionOptions()
        so.set_headers({
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "connection": "keep-alive", "accept-charset": "GB2312,utf-8;q=0.7,*;q=0.7"}
        )
        self.so = so
        self.page = SessionPage(session_or_options=self.so)
        self.err_site = set()
        self.cur_site = ''

    def open(self):
        """
        打开网页输入用户名密码并点击
        :return: None
        """
        # self.page.set.cookies.clear()
        random.shuffle(AmazonConfig.COMMON_SITE)
        for site in AmazonConfig.COMMON_SITE:
            self.cur_site = site
            self.cur_url = f'https://www.amazon.{site}/'
            print(f'正在切换站点：{site}', end=' ')
            self.page.get(self.cur_url)
            self.check_page()

            try:
                if not self.check_page():
                    self.err_site.add(self.cur_site)
                    print(f'站点：{site} 切换异常')
            except:
                self.err_site.add(self.cur_site)
                print(f'站点：{site} 切换异常')

    def change_address(self):
        skip = self.check_addr_code()
        if skip:
            return True
        self.url = url = self.page.url
        html = self.page.html
        if 'glow-ingress-line2' not in html:
            print('当前页面未检测到地址元素！')
            return
        address = self.page('#glow-ingress-line2')
        addressText = address.text.strip()
        domain = urlparse(url).netloc
        matchFound = False
        zipcode = '90001'
        domain_code = domain.split("amazon.")[1]
        value = AmazonConfig.SITE_CODE[domain_code]
        self.cur_country = AmazonConfig.COUNTRY_SITE_SPRITE[domain_code]
        self.cur_address = addressText

        if isinstance(value, list):  # 判断是否为列表（Python 中的数组）
            if not any(item in addressText for item in value):  # 如果值是列表，检查列表中的任何元素是否包含在 addressText 中
                matchFound = True
                zipcode = value[0]
        else:  # 如果值不是列表
            if value not in addressText:  # 直接比较值是否在 addressText 中
                zipcode = value
                matchFound = True

        if matchFound:
            print(f'当前站点：{self.cur_country},当前地址为：{addressText},与目标地址:{zipcode}不匹配。正在修改地址信息...', end=' ')
            # 获取数据
            get_data = self.page("#nav-global-location-data-modal-action").attr('data-a-modal')
            modal_data = json.loads(get_data)
            url2 = modal_data["url"]
            anti_csrftoken_a2z = modal_data['ajaxHeaders']["anti-csrftoken-a2z"]

            headers = {
                'accept': 'text/html,*/*',
                'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'anti-csrftoken-a2z': anti_csrftoken_a2z,
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                'viewport-width': '1912',
                'x-requested-with': 'XMLHttpRequest',
            }
            base_url = f"https://{domain}{url2}"
            self.page.get(base_url, headers=headers)
            csrf_token_match = re.search(r'CSRF_TOKEN\s*:\s*"([^"]+)"', self.page.html)
            if not csrf_token_match:
                print('页面信息不完整，可能是TLS指纹被识别！')
                return
            csrf_token = csrf_token_match.group(1)
            page_type_match = re.search(r"pageType=([^&]+)", base_url)
            if not page_type_match:
                print('页面信息不完整，可能是TLS指纹被识别！')
                return
            page_type = page_type_match.group(1)
            pageurl = base_url
            storecontext = "generic" if "/dp" in pageurl else "wireless"
            return self.change_zipcode(zipcode, storecontext, page_type, csrf_token, domain)

    def change_zipcode(self, zipcode: str, storecontext: str, page_type: str, csrf_token: str, domain: str):
        url = f"https://{domain}/portal-migration/hz/glow/address-change?actionSource=glow"

        data = {
            "locationType": "LOCATION_INPUT",
            "zipCode": zipcode,
            "storeContext": storecontext,
            "deviceType": "web",
            "pageType": page_type,
            "actionSource": "glow"
        }

        headers = {
            "accept": "text/html,*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "anti-csrftoken-a2z": csrf_token,
            "content-type": "application/json",
            "x-requested-with": "XMLHttpRequest"
        }

        self.page.post(url, headers=headers, data=json.dumps(data), allow_redirects=True)
        '''
        {"isValidAddress":1,"isAddressUpdated":1,"sembuUpdated":1,"isTransitOutOfAis":1,"successful":1,"address":{"countryCode":"DE","zipCode":"14199","state":"","city":"Berlin","district":"","addressId":null,"obfuscatedId":null,"locationType":"LOCATION_INPUT","addressLabel":null,"addressLine1":"","isDefaultShippingAddress":0,"isAccountAddress":0}}
        '''

        result = self.page.json
        if result.get('successful'):
            self.page.get(self.url)
            self.check_addr_code()
            return True
        print(f'{self.cur_country}地址切换失败！')

    def check_addr_code(self):
        if '.amazon.' in self.page.url and 'glow-ingress-block' in self.page.html:
            ingress = self.page('#glow-ingress-line2') or self.page('#glow-ingress-block')
            element_text = ingress.text
            print(f'当前地址：{element_text}')
            return any(item in element_text for item in AmazonConfig.ADDRESS_CODE)

    def check_page(self):
        for i1 in range(3):  # 验证码最大重试3次
            cur_url = self.page.url
            domain = urlparse(cur_url).netloc
            if not self.page.html:
                self.page.get(cur_url)
                continue

            if 'id="captchacharacters"' in self.page.html:  # 验证码
                print('检测到验证码页面...', end=' ')
                img_url = self.page('c=[class="a-row a-text-center"] img').attr('src')
                # print(img_url)
                print(f'验证码第{i1 + 1}次识别中...', end=' ')
                captcha_result = amazoncaptcha.AmazonCaptcha.fromlink(img_url).solve()
                print('本次识别结果为：', captcha_result)

                amzn = self.page('c=input[name="amzn"]').attr('value')
                amznr = self.page('c=input[name="amzn-r"]').attr('value')
                # print(amzn, amznr)
                params = {
                    "amzn": amzn,
                    "amzn-r": amznr,
                    "field-keywords": captcha_result
                }
                self.page.get(f"https://{domain}/errors/validateCaptcha", params=params)
                continue

            while 'ref=cs_503_link' in self.page.html:
                print('503页面')
                time.sleep(2)
                self.page('@href:ref=cs_503_link', timeout=2)
                self.page.get(cur_url)

            if 'ref=cs_404_link' in self.page.html:
                return False

            if not self.change_address():
                self.page.get(cur_url)
                continue

            return True  # 已检测到可能的异常全处理完则跳出页面页面检测
        else:
            raise Exception("页面异常处理失败！")

    def login_successfully(self):
        """
        判断是否登录成功
        :return:
        """
        return self.check_addr_code()

    def get_cookies(self):
        """
        获取Cookies
        :return:
        """
        return self.page.cookies().as_dict()

    def main(self):
        """
        破解入口
        :return:
        """
        self.open()
        # 如果不需要验证码直接登录成功
        cookies = self.get_cookies()
        # cookies2 = self.page.cookies()
        if not self.err_site:
            return {
                'status': 1,
                'content': cookies
            }
        else:
            print(f'部分地址切换失败！{self.err_site}')
            return {
                'status': 3,
                'content': '登录失败'
            }


if __name__ == '__main__':
    # s = 9222
    # e = s + 10
    # for i in range(s, e):
    #     s1 = time.time()
    #     result = AmazonCookies(9222).main()
    #     s2 = time.time()
    #     print(result)
    #     print(f'耗时: {s2 - s1:.6f}秒')
    cookies = AmazonCookies(9555).main()
    print(cookies)
