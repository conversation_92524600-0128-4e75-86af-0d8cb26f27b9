import os
from string import Template
from .SpiderTools import IPPool


class ProxyExtensionGenerator:
    scheme = 'http'
    host = IPPool.tunnel.split(':')[0]
    port = IPPool.tunnel.split(':')[1]
    username = IPPool.username
    password = IPPool.password
    plugin_folder = 'C:\\代理插件\\kdl_Chromium_Proxy'

    ip_address = IPPool.ip_address
    ip_port = IPPool.ip_port
    proxy_username = IPPool.proxy_username
    proxy_password = IPPool.proxy_password
    plugin_folder2 = 'C:\\代理插件\\kdl_Chromium_Proxy2'

    @classmethod
    def generate_proxy_extension(cls, proxy_type=1):
        """Generate the proxy extension files."""
        if proxy_type == 1:  # 隧道动态代理
            plugin_folder = cls.create_plugin_folder(cls.plugin_folder)
            cls.write_manifest(plugin_folder)
            cls.write_background_script(plugin_folder, cls.scheme, cls.host, cls.port, cls.username, cls.password)
        else:  # 海外静态代理
            plugin_folder = cls.create_plugin_folder(cls.plugin_folder2)
            cls.write_manifest(plugin_folder)
            cls.write_background_script(plugin_folder, cls.scheme, cls.ip_address, cls.ip_port, cls.proxy_username, cls.proxy_password)

        return plugin_folder

    @staticmethod
    def create_plugin_folder(plugin_folder):
        """Ensure the plugin folder exists."""
        plugin_folder = os.path.abspath(plugin_folder)
        if not os.path.exists(plugin_folder):
            os.makedirs(plugin_folder)
        return plugin_folder

    @staticmethod
    def write_manifest(plugin_folder):
        """Write the manifest.json file."""
        manifest_json = """
            {
                "version": "1.0.0",
                "manifest_version": 2,
                "name": "kdl_Chromium_Proxy",
                "permissions": [
                    "proxy",
                    "tabs",
                    "unlimitedStorage",
                    "storage",
                    "<all_urls>",
                    "webRequest",
                    "webRequestBlocking",
                    "browsingData"
                ],
                "background": {
                    "scripts": ["background.js"]
                },
                "minimum_chrome_version":"22.0.0"
            }
        """
        manifest_path = os.path.join(plugin_folder, "manifest.json")
        with open(manifest_path, "w") as manifest_file:
            manifest_file.write(manifest_json)

    @staticmethod
    def write_background_script(plugin_folder, scheme, host, port, username, password):
        """Write the background.js file with substituted values."""
        background_js_template = Template("""
            var config = {
                mode: "fixed_servers",
                rules: {
                singleProxy: {
                    scheme: "${scheme}",
                    host: "${host}",
                    port: parseInt(${port})
                },
                bypassList: []
                }
            };

            chrome.proxy.settings.set({value: config, scope: "regular"}, function() {});

            function callbackFn(details) {
                return {
                    authCredentials: {
                        username: "${username}",
                        password: "${password}"
                    }
                };
            }

            chrome.webRequest.onAuthRequired.addListener(
                callbackFn,
                {urls: ["<all_urls>"]},
                ['blocking']
            );
        """)
        background_js = background_js_template.substitute(
                host=host,
                port=port,
                username=username,
                password=password,
                scheme=scheme,
        )
        background_path = os.path.join(plugin_folder, "background.js")
        with open(background_path, "w") as background_file:
            background_file.write(background_js)


if __name__ == "__main__":
    # 使用示例
    plugin_folder = ProxyExtensionGenerator.generate_proxy_extension()
    print(f"生成的代理扩展所在路径： {plugin_folder}")
