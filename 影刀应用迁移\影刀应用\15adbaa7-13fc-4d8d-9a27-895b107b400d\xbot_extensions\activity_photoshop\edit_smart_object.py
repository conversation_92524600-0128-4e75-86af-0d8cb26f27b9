# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
from xbot import print, sleep
from .import package
from .package import variables as glv


def get_layer_by_name(photoshop_instance, name_of_layer):
    return photoshop_instance.layer(name_of_layer, only_visible=False)


def edit_smart_object(ps_instance, layer_name):
    if ">" not in layer_name:
        try:
            active_layer = ps_instance.app.ActiveDocument.ArtLayers(layer_name)
        except:
            active_layer = get_layer_by_name(ps_instance, layer_name)
    else: 
        *layersets, layer_name = [path.strip() for path in layer_name.split(">")]

        current_layerset = ps_instance.doc
        for layerset_name in layersets:
            current_layerset = current_layerset.LayerSets(layerset_name)

        active_layer = current_layerset.Layers(layer_name)
    # 
    ps_instance.doc.ActiveLayer = active_layer
    ps_instance.app.DoJavaScript("runMenuItem(stringIDToTypeID('placedLayerEditContents'))")


def main(args):
    ps_instance = layer_name = args.get("ps_instance")
    layer_name = args.get("layer_name")

    edit_smart_object(ps_instance, layer_name)
    pass
