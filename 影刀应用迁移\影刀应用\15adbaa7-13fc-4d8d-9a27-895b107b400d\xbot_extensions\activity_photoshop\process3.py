import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        photoshop_instance = ""
        name_of_layer = ""
    else:
        photoshop_instance = args.get("photoshop_instance", "")
        name_of_layer = args.get("name_of_layer", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="activate_layer_by_name", params={
            "photoshop_instance": photoshop_instance,
            "name_of_layer": name_of_layer,
        }, _block=("激活图层", 1, "调用模块"))
    finally:
        pass
