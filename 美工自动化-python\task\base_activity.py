#coding=utf-8
"""
    这里是基础指令集合
"""

import win32api
import win32con
import os
import win32com.client
from win32com.client import constants
from PIL import Image
import logging


class BaseLogger:
    def __init__(self, log_file="application.log", log_level=logging.INFO):
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(log_level)

        # Create a file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)

        # Create a console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)

        # Create a formatter and set it for both handlers
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Add handlers to the logger
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def log_info(self, message):
        self.logger.info(message)

    def log_warning(self, message):
        self.logger.warning(message)

    def log_error(self, message):
        self.logger.error(message)

    def log_debug(self, message):
        self.logger.debug(message)



#文件操作类
class DirectoryReader:
    def __init__(self):
        pass

    def get_filtered_folders(self, directory, filter_string=""):
        if not os.path.exists(directory):
            raise FileNotFoundError(f"Directory {directory} does not exist.")

        folders = []
        for item in os.listdir(directory):
            item_path = os.path.join(directory, item)
            if os.path.isdir(item_path) and filter_string in item:
                folders.append(item_path)
        return folders

    def ensure_folder_exists(self, folder_path):
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)
        return folder_path

    def check_network_path_exists(self, path):
        try:
            win32api.GetFileAttributes(path)
            return True
        except Exception as e:
            print(f"检查路径时出错: {e}")
            return False

    def reliable_check_path_exists(self, path, retries=3, delay=1):
        import time
        for _ in range(retries):
            try:
                if self.check_network_path_exists(path):
                    return True
            except Exception as e:
                print(f"检查路径失败: {e}")
            time.sleep(delay)
        return False

#表格操作类
class ExcelHandler:
    def __init__(self, file_path):
        self.file_path = file_path
        self.app = win32com.client.Dispatch("Excel.Application")
        self.app.Visible = False  # 不显示 Excel 窗口

    def open_excel(self):
        if not os.path.exists(self.file_path):
            raise FileNotFoundError(f"File {self.file_path} does not exist.")
        
        workbook = self.app.Workbooks.Open(self.file_path)
        return workbook

    def close_excel(self, workbook):
        workbook.Close(SaveChanges=False)
        self.app.Quit()

    def write_to_cell(self, workbook, sheet_name, row, col, value):
        try:
            print(f"sheet_name,row,col,value", sheet_name,row,col,value)
            sheet = workbook.Worksheets(sheet_name)
            sheet.Cells(row, col).Value = value
            workbook.Save()
        except Exception as e:
            raise ValueError(f"Error writing to cell: {e}")
        
    def read_sheet_data(self, workbook, sheet_name):
        try:
            sheet = workbook.Worksheets(sheet_name)
            data = []
            row = 1
            while True:
                row_data = []
                col = 1
                while True:
                    cell_value = sheet.Cells(row, col).Value
                    if cell_value is None:
                        break
                    row_data.append(cell_value)
                    col += 1
                if not row_data:
                    break
                data.append(row_data)
                row += 1
            return data
        except Exception as e:
            raise ValueError(f"Error reading sheet {sheet_name}: {e}")


#ps类    
class PSFileHandler:
    def __init__(self):
        
        self.app = win32com.client.Dispatch("Photoshop.Application")
    #·获取文件夹下的psd文件
    def open_ps_file(self, file_path):
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File {file_path} does not exist.")
        # if not file_path.lower().endswith('.psd'):
        #     raise ValueError(f"Only .psd files can be opened. Provided file: {file_path}")
        
        document = self.app.Open(file_path)
        return document
    #关闭ps文件
    def close_ps_file(self):
        try:
            # Initialize Photoshop application
            psApp = win32com.client.Dispatch("Photoshop.Application")

            # Close all remaining documents without saving
            while psApp.Documents.Count > 0:
                psApp.ActiveDocument.Close(2)  # 2 = psDoNotSaveChanges
        except Exception as e:
            print(f"关闭 Photoshop 文档时出错: {e}")

    def hide_all_result_layers(self,app,layer_names):
        try:
            # 获取当前活动的文档
            doc = app.ActiveDocument

            # 检查是否有打开的文档
            if not doc:
                print("没有打开的 Photoshop 文档")
                return

            # 遍历所有图层
            for layer in doc.Layers:
                if layer.Name in layer_names:
                    layer.Visible = False
                if layer.typename == "LayerSet":
                    for sub_layer in layer.Layers:
                        if sub_layer.Name in layer_names:
                            sub_layer.Visible = False

        except Exception as e:
            print(f"隐藏图层时出错: {e}")
        
    #导出图片
    def export_psd_to_jpg_with_quality(self, app, export_file_path, layer_name):
        try:
            # 设置 Photoshop 可见
            app.Visible = True

            # 获取当前活动的文档（如果有的话）
            doc = app.ActiveDocument

            # 检查是否有打开的文档
            if not doc:
                print("没有打开的 Photoshop 文档")
                return

            # 获取指定图层
            layer = None
            for lyr in doc.Layers:
                if lyr.Name == layer_name:
                    layer = lyr
                    break
                if lyr.typename == "LayerSet":
                    for sub_lyr in lyr.Layers:
                        if sub_lyr.Name == layer_name:
                            layer = sub_lyr
                            break

            if not layer:
                print(f"未找到名为 '{layer_name}' 的图层")
                return

            # 显示指定图层
            layer.Visible = True

            # 设置导出文件的选项
            options = win32com.client.Dispatch("Photoshop.JPEGSaveOptions")

            # 配置保存选项，设置为高质量
            options.quality = 12  # 质量 12 (高)
            options.formatOptions = 1  # 标准 JPG 格式
            options.scans = 3  # 使用 3 扫描来提高质量
            options.matte = 1  # 背景色填充透明部分

            # 导出文件为 JPG 格式
            doc.SaveAs(export_file_path, options, True)
            file_size = os.path.getsize(export_file_path)  # 获取文件大小，单位是字节

            # 如果文件大小大于 2.5MB（2.5MB = 2,500,000 字节），则压缩
            if file_size > 2.5 * 1024 * 1024:
                print(f"图片大小 {file_size} bytes, 超过 2.5MB, 执行压缩")
                self.compress_image(export_file_path, export_file_path)  # 调用压缩函数

            print(f"导出成功，图片保存在: {export_file_path}")

            # 隐藏指定图层
            layer.Visible = False

        except Exception as e:
            print(f"导出图片时出错: {e}")



    def recover_init(self):
        # 启动 Photoshop 应用
        psApp = win32com.client.Dispatch("Photoshop.Application")

        # 获取当前文档
        doc = psApp.Application.ActiveDocument

        # 获取历史记录状态
        history_states = doc.HistoryStates

        # 将当前文档状态设置为初始状态（第一个历史记录）
        doc.ActiveHistoryState = history_states[0]

        print("已撤回所有操作，恢复到初始状态。")



    def compress_image(self,input_path, output_path):
        # 打开图片
        with Image.open(input_path) as img:
            # 保存图片时，指定压缩质量和优化选项
            img.save(output_path, quality=95, optimize=True)


    # 让 Photoshop 执行 JSX 脚本
    def replace_img(self,layer_name, file_path):
        """执行替换内容的操作"""
        
        # 确保文件路径有效
        if not os.path.exists(file_path):
            print(f"错误: 文件 {file_path} 不存在。")
            return

        # 将文件路径转换为 JSX 兼容格式（正斜杠）
        jsx_file_path = file_path.replace("\\", "/")

        jsx_code = f"""
        var idslct = charIDToTypeID( "slct" );
        var desc468 = new ActionDescriptor();
        var idnull = charIDToTypeID( "null" );
            var ref8 = new ActionReference();
            var idLyr = charIDToTypeID( "Lyr " );
            ref8.putName( idLyr, "{layer_name}" );
        desc468.putReference( idnull, ref8 );
        var idMkVs = charIDToTypeID( "MkVs" );
        desc468.putBoolean( idMkVs, false );
        var idLyrI = charIDToTypeID( "LyrI" );
            var list6 = new ActionList();
            list6.putInteger( 2059 );
        desc468.putList( idLyrI, list6 );
        executeAction( idslct, desc468, DialogModes.NO );
        
        var idplacedLayerReplaceContents = stringIDToTypeID( "placedLayerReplaceContents" );
        var desc473 = new ActionDescriptor();
        var idnull = charIDToTypeID( "null" );
        desc473.putPath( idnull, new File( "{jsx_file_path}" ) );
        var idLyrI = charIDToTypeID( "LyrI" );
        desc473.putInteger( idLyrI, 2059 );
        executeAction( idplacedLayerReplaceContents, desc473, DialogModes.NO );  // 关键修正：禁用对话框
        """
        
        self.app.DoJavaScript(jsx_code)
