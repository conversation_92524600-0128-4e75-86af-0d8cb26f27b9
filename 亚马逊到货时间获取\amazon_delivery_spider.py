import feapder
import pymysql
from playwright.sync_api import sync_playwright
import logging
import time
import random
import re
from typing import Dict, List, Optional, Tuple
from utils_mrc import SpiderTools

amazon_sites = {
    "US": {"url": "amazon.com", "country": "美国", "postcode": "10001"},
    "CA": {"url": "amazon.ca", "country": "加拿大", "postcode": "M5V 3L9"},
    "UK": {"url": "amazon.co.uk", "country": "英国", "postcode": "EC1A 1BB"},
    "DE": {"url": "amazon.de", "country": "德国", "postcode": "10117"},
    "FR": {"url": "amazon.fr", "country": "法国", "postcode": "75001"},
    "IT": {"url": "amazon.it", "country": "意大利", "postcode": "00144"},
    "ES": {"url": "amazon.es", "country": "西班牙", "postcode": "28013"},
    "JP": {"url": "amazon.co.jp", "country": "日本", "postcode": "100-0005"},
    "AU": {"url": "amazon.com.au", "country": "澳大利亚", "postcode": "2000"},
    "IN": {"url": "amazon.in", "country": "印度", "postcode": "400001"},
    "MX": {"url": "amazon.com.mx", "country": "墨西哥", "postcode": "06140"},
    "BR": {"url": "amazon.com.br", "country": "巴西", "postcode": "20031-000"}
}


class AmazonDeliverySpider(feapder.AirSpider):
    """
    Amazon配送信息爬虫
    使用Playwright抓取Amazon商品页面的配送信息
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 月份映射字典 - 支持多语言
        self.month_map = {
            # 英语
            'january': '01', 'jan': '01',
            'february': '02', 'feb': '02',
            'march': '03', 'mar': '03',
            'april': '04', 'apr': '04',
            'may': '05',
            'june': '06', 'jun': '06',
            'july': '07', 'jul': '07',
            'august': '08', 'aug': '08',
            'september': '09', 'sep': '09', 'sept': '09',
            'october': '10', 'oct': '10',
            'november': '11', 'nov': '11',
            'december': '12', 'dec': '12',

            # 德语
            'januar': '01', 'jän': '01',
            'februar': '02', 'feb': '02',
            'märz': '03', 'mär': '03',
            'april': '04', 'apr': '04',
            'mai': '05',
            'juni': '06', 'jun': '06',
            'juli': '07', 'jul': '07',
            'august': '08', 'aug': '08',
            'september': '09', 'sep': '09', 'sept': '09',
            'oktober': '10', 'okt': '10',
            'november': '11', 'nov': '11',
            'dezember': '12', 'dez': '12',

            # 法语
            'janvier': '01', 'janv': '01',
            'février': '02', 'févr': '02', 'fevrier': '02',
            'mars': '03',
            'avril': '04', 'avr': '04',
            'mai': '05',
            'juin': '06',
            'juillet': '07', 'juil': '07',
            'août': '08', 'aout': '08',
            'septembre': '09', 'sept': '09',
            'octobre': '10', 'oct': '10',
            'novembre': '11', 'nov': '11',
            'décembre': '12', 'déc': '12', 'decembre': '12',

            # 意大利语
            'gennaio': '01', 'gen': '01',
            'febbraio': '02', 'feb': '02',
            'marzo': '03', 'mar': '03',
            'aprile': '04', 'apr': '04',
            'maggio': '05', 'mag': '05',
            'giugno': '06', 'giu': '06',
            'luglio': '07', 'lug': '07',
            'agosto': '08', 'ago': '08',
            'settembre': '09', 'set': '09',
            'ottobre': '10', 'ott': '10',
            'novembre': '11', 'nov': '11',
            'dicembre': '12', 'dic': '12',

            # 西班牙语
            'enero': '01', 'ene': '01',
            'febrero': '02', 'feb': '02',
            'marzo': '03', 'mar': '03',
            'abril': '04', 'abr': '04',
            'mayo': '05', 'may': '05',
            'junio': '06', 'jun': '06',
            'julio': '07', 'jul': '07',
            'agosto': '08', 'ago': '08',
            'septiembre': '09', 'sep': '09', 'sept': '09',
            'octubre': '10', 'oct': '10',
            'noviembre': '11', 'nov': '11',
            'diciembre': '12', 'dic': '12',

            # 日语
            '1月': '01', '2月': '02', '3月': '03', '4月': '04', '5月': '05', '6月': '06',
            '7月': '07', '8月': '08', '9月': '09', '10月': '10', '11月': '11', '12月': '12'
        }
        
        # 数据库配置
        self.mysql_config = {
            'host': '*************',
            'port': 3306,
            'user': 'jiashilong',
            'password': '49FA6B98',
            'database': 'rpa',
            'charset': 'utf8mb4'
        }
        
        # Playwright配置 - 使用线程本地存储
        import threading
        self.local_data = threading.local()
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # feapder配置 - 使用单线程避免Playwright线程问题
        self.custom_setting = {
            'SPIDER_THREAD_COUNT': 2,  # 改为单线程
            'SPIDER_MAX_RETRY_TIMES': 3,  # 重试次数
            'REQUEST_LOST_TIMEOUT': 600,  # 请求超时时间
            'RETRY_FAILED_REQUESTS': True,  # 重试失败请求
        }
        
    def start_requests(self):
        """
        从数据库获取需要抓取的URL
        """
        try:
            # 连接数据库
            connection = pymysql.connect(**self.mysql_config)
            with connection.cursor() as cursor:
                try:
                    # 查询app_id=13且status=1的数据，不修改状态
                    sql = "SELECT id, asins, site FROM task_listing WHERE app_id = 13 AND status = 1"
                    cursor.execute(sql)
                    results = cursor.fetchall()
                    
                    self.logger.info(f"从task_listing表获取到 {len(results)} 条数据")
                    
                    # 为每个asin生成请求，不在这里修改状态
                    for task_id, asins, site in results:
                        if asins and site:
                            # 构造Amazon URL
                            domain = amazon_sites.get(site.upper(), {}).get("url", "amazon.com")
                            url = f"https://www.{domain}/dp/{asins}?psc=1"
                            
                            yield feapder.Request(
                                url=url,
                                callback=self.parse_delivery_info,
                                meta={
                                    'asin': asins,  # 传递asin参数
                                    'task_id': task_id,  # 传递任务ID
                                    'site': site  # 传递站点信息
                                }
                            )
                except Exception as e:
                    self.logger.error(f"查询task_listing表失败: {e}")
                    raise
            
            connection.close()
            
        except Exception as e:
            self.logger.error(f"数据库连接错误: {e}")
            
    def update_task_status(self, task_id, status):
        """
        更新任务状态
        """
        try:
            connection = pymysql.connect(**self.mysql_config)
            with connection.cursor() as cursor:
                update_sql = "UPDATE task_listing SET status = %s WHERE id = %s"
                cursor.execute(update_sql, (status, task_id))
                connection.commit()
                self.logger.info(f"任务 {task_id} 状态已更新为 {status}")
        except Exception as e:
            self.logger.error(f"更新任务 {task_id} 状态失败: {e}")
        finally:
            if 'connection' in locals():
                connection.close()
            
    def get_browser(self):
        """
        获取线程本地的浏览器实例，并设置公司隧道代理
        """
        if not hasattr(self.local_data, 'browser') or self.local_data.browser is None:
            try:
                from utils_mrc.SpiderTools import IPPool
                from urllib.parse import urlparse
                self.local_data.playwright = sync_playwright().start()
                # 解析隧道代理信息
                proxy_url = IPPool.proxies['http']
                p = urlparse(proxy_url)
                proxy_server = f"{p.scheme}://{p.hostname}:{p.port}"
                proxy_username = p.username
                proxy_password = p.password

                self.local_data.browser = self.local_data.playwright.chromium.launch(
                    headless=True,
                    # headless=False,
                    proxy={
                        "server": proxy_server,
                        "username": proxy_username,
                        "password": proxy_password
                    },
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-blink-features=AutomationControlled',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor'
                    ]
                )
                self.local_data.context = self.local_data.browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                )
                self.logger.info("浏览器启动成功（已设置隧道代理）")
            except Exception as e:
                self.logger.error(f"浏览器启动失败: {e}")
                raise
        return self.local_data.browser, self.local_data.context
        
    def close_browser(self):
        """
        关闭线程本地的浏览器实例
        """
        try:
            if hasattr(self.local_data, 'context') and self.local_data.context:
                self.local_data.context.close()
                self.local_data.context = None
                
            if hasattr(self.local_data, 'browser') and self.local_data.browser:
                self.local_data.browser.close()
                self.local_data.browser = None
                
            if hasattr(self.local_data, 'playwright') and self.local_data.playwright:
                self.local_data.playwright.stop()
                self.local_data.playwright = None
                
            self.logger.info("浏览器关闭成功")
        except Exception as e:
            self.logger.error(f"浏览器关闭失败: {e}")
            
    def start_browser(self):
        """
        启动浏览器（保持兼容性）
        """
        return self.get_browser()
            
    def close_browser_old(self):
        """
        关闭浏览器（旧版本，已弃用）
        """
        pass
            
    def safe_get_text(self, page, xpath: str, timeout: int = 5000) -> str:
        """
        安全获取元素文本
        """
        try:
            element = page.wait_for_selector(xpath, timeout=timeout)
            if element:
                return element.text_content().strip()
            return ""
        except Exception as e:
            self.logger.debug(f"获取元素失败 {xpath}: {e}")
            return ""
            
    def check_delivery_block_exists(self, page) -> bool:
        """
        检查配送信息块是否存在
        """
        try:
            # 检查配送信息容器是否存在
            delivery_container = page.locator('//div[@id="mir-layout-DELIVERY_BLOCK-slot-PRIMARY_DELIVERY_MESSAGE_LARGE"]')
            return delivery_container.count() > 0
        except:
            return False
            
    def simulate_zip_code_input(self, page, url=None) -> bool:
        """
        判断当前页面邮编是否已填写，未填写则自动填写。
        url: 当前页面url，用于判断站点
        """
        try:
            # 1. 根据url判断当前站点，获取目标邮编
            site_code = None
            if url:
                for code, info in amazon_sites.items():
                    if info["url"] in url:
                        site_code = code
                        break
            if not site_code:
                site_code = "US"  # 默认美国
            target_postcode = amazon_sites[site_code]["postcode"]

            # 2. 获取页面当前邮编文本
            try:
                ingress_elem = page.wait_for_selector('#glow-ingress-line2', timeout=5000)
                current_postcode = ingress_elem.text_content().strip() if ingress_elem else ""
            except Exception as e:
                self.logger.debug(f"获取glow-ingress-line2失败: {e}")
                current_postcode = ""

            # 3. 判断是否已填写目标邮编
            if target_postcode in current_postcode:
                self.logger.info(f"页面已填写目标邮编: {current_postcode}")
                return True

            # 4. 点击打开邮编弹窗
            try:
                popover_btn = page.locator('#nav-global-location-popover-link')
                if popover_btn.count() > 0:
                    popover_btn.first.click()
                    page.wait_for_timeout(1500)
                else:
                    self.logger.error("未找到邮编弹窗按钮")
                    return False
            except Exception as e:
                self.logger.error(f"点击邮编弹窗按钮失败: {e}")
                return False

            # 5. 检查弹窗input是否已填写
            try:
                input_elem = page.locator('#GLUXZipUpdateInput')
                if input_elem.count() > 0:
                    input_value = input_elem.first.get_attribute('value')
                    if input_value and input_value.strip() == target_postcode:
                        self.logger.info(f"弹窗已填写目标邮编: {input_value}")
                        # 直接关闭弹窗
                        close_btn = page.locator('//*[@id="a-popover-3"]/div/header/button')
                        if close_btn.count() > 0:
                            close_btn.first.click()
                            page.wait_for_timeout(1000)
                        return True
                    else:
                        # 填写邮编
                        input_elem.first.fill(target_postcode)
                        page.wait_for_timeout(50)
                else:
                    self.logger.error("未找到邮编输入框")
                    return False
            except Exception as e:
                self.logger.error(f"处理邮编输入框失败: {e}")
                return False


            page.wait_for_timeout(10002)

            # 6. 点击提交按钮
            try:
                submit_btn = page.locator('//*[@id="GLUXZipUpdate"]/span/input')
                if submit_btn.count() > 0:
                    submit_btn.first.click()
                    page.wait_for_timeout(1500)
                else:
                    self.logger.error("未找到邮编提交按钮")
                    return False
            except Exception as e:
                self.logger.error(f"点击邮编提交按钮失败: {e}")
                return False

            # 7. 点击确认关闭按钮//*[@id="a-popover-1"]/div/header/button
            try:
                close_btn = page.locator('//*[@id="a-popover-1"]/div/header/button')
                if close_btn.count() > 0:
                    close_btn.first.click()
                    page.wait_for_timeout(1000)
            except Exception as e:
                self.logger.debug(f"关闭邮编弹窗失败: {e}")

            self.logger.info(f"成功填写邮编: {target_postcode}")
            return True

        except Exception as e:
            self.logger.error(f"模拟邮编输入失败: {e}")
            return False

    def clean_delivery_message(self, message: str) -> str:
        """
        清洗配送信息，提取月日信息

        Args:
            message: 原始配送信息

        Returns:
            清洗后的月日信息，格式为 "MM-DD" 或原文本（如果无法提取）
        """
        if not message or not message.strip():
            return ""

        message_lower = message.lower()

        # 正则模式：匹配月份单词和周围的数字
        # 模式1: 数字 + 月份 (如: 23 luglio, 21 Juli)
        pattern1 = r'(\d{1,2})\s*[,.\s]*\s*([a-zA-Zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]+)'

        # 模式2: 月份 + 数字 (如: luglio 23, Juli 21)
        pattern2 = r'([a-zA-Zàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]+)\s*[,.\s]*\s*(\d{1,2})'

        # 模式3: 日语格式 (如: 7月23日)
        pattern3 = r'(\d{1,2})月(\d{1,2})日?'

        try:
            # 尝试日语格式
            match = re.search(pattern3, message)
            if match:
                month = match.group(1).zfill(2)
                day = match.group(2).zfill(2)
                return f"{month}-{day}"

            # 尝试模式1: 数字 + 月份
            match = re.search(pattern1, message_lower)
            if match:
                day_str = match.group(1)
                month_word = match.group(2).strip()

                # 查找月份映射
                month_num = self.month_map.get(month_word)
                if month_num:
                    day = day_str.zfill(2)
                    return f"{month_num}-{day}"

            # 尝试模式2: 月份 + 数字
            match = re.search(pattern2, message_lower)
            if match:
                month_word = match.group(1).strip()
                day_str = match.group(2)

                # 查找月份映射
                month_num = self.month_map.get(month_word)
                if month_num:
                    day = day_str.zfill(2)
                    return f"{month_num}-{day}"

            # 如果都没有匹配到，尝试更宽松的匹配
            # 查找所有可能的月份词
            for month_word, month_num in self.month_map.items():
                if month_word in message_lower:
                    # 在月份词附近查找数字
                    month_pos = message_lower.find(month_word)

                    # 在月份词前后20个字符内查找1-31的数字
                    start_pos = max(0, month_pos - 20)
                    end_pos = min(len(message), month_pos + len(month_word) + 20)
                    context = message[start_pos:end_pos]

                    # 查找1-31的数字
                    day_match = re.search(r'\b([1-9]|[12]\d|3[01])\b', context)
                    if day_match:
                        day = day_match.group(1).zfill(2)
                        return f"{month_num}-{day}"

            # 如果无法提取日期，返回原文本的前50个字符
            return message[:50].strip()

        except Exception as e:
            self.logger.error(f"清洗配送信息失败: {e}")
            return message[:50].strip() if message else ""

    def parse_delivery_info(self, request, response):
        """
        解析配送信息
        """
        asin = request.meta.get('asin')
        task_id = request.meta.get('task_id')
        site = request.meta.get('site')
        url = request.url

        browser = None
        context = None
        page = None

        try:
            # 在开始处理前，将任务状态修改为2（执行中）
            if task_id:
                self.update_task_status(task_id, 2)
                self.logger.info(f"任务 {task_id} 状态已更新为执行中")

            # 获取浏览器实例
            browser, context = self.get_browser()

            # 创建新页面
            page = context.new_page()

            # 设置更长的超时时间并添加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.logger.info(f"ASIN {asin}: 第 {attempt + 1} 次尝试访问页面")
                    # 访问页面，只等待DOM内容加载
                    page.goto(url, wait_until='domcontentloaded', timeout=60000)
                    # 不等待networkidle，而是固定等待一段时间让页面渲染
                    page.wait_for_timeout(5000)  # 等待5秒让页面渲染
                    self.logger.info(f"ASIN {asin}: 页面加载成功")
                    break
                except Exception as e:
                    self.logger.warning(f"ASIN {asin}: 第 {attempt + 1} 次访问失败: {e}")
                    if attempt == max_retries - 1:
                        raise
                    time.sleep(5)  # 等待5秒再重试

            # 随机等待
            time.sleep(random.uniform(2, 5))

            # 首先检查配送信息块是否存在
            if not self.check_delivery_block_exists(page):
                self.logger.info(f"ASIN {asin}: 配送信息块不存在，尝试输入邮编")
                # 尝试输入邮编
                if self.simulate_zip_code_input(page, url):
                    self.logger.info(f"ASIN {asin}: 成功输入邮编，重新检查配送信息")
                    time.sleep(3)  # 等待页面更新

            # 提取配送信息
            result = self.extract_delivery_info(page, asin)
            # 添加任务ID和站点信息
            result['task_id'] = task_id
            result['site'] = site
            # 这里不再 yield feapder.Item(result)
            # 可在此处直接调用 self.save_items([result]) 立即写库，或批量收集后统一写库
            self.save_items([result], task_id)

        except Exception as e:
            self.logger.error(f"解析 {asin} 失败: {e}")
            # 失败时也直接写库
            error_result = {
                'asin': asin,
                'primary': '',
                'secondary': '',
                'primary_cleaned': '',
                'secondary_cleaned': '',
                'status': 'error',
                'error': str(e),
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'task_id': task_id,
                'site': site
            }
            self.save_items([error_result], task_id)
            # 异常时更新任务状态为20
            if task_id:
                self.update_task_status(task_id, 20)

        finally:
            # 关闭页面
            if page is not None:
                try:
                    page.close()
                except:
                    pass
            
    def extract_delivery_info(self, page, asin: str) -> Dict:
        """
        提取配送信息
        """
        try:
            # 优先判断页面是否存在到货信息主块
            offer_div = page.locator('#apex_offerDisplay_single_desktop')
            if offer_div.count() == 0:
                self.logger.info(f"ASIN {asin}: 页面无到货信息主块，直接返回无到货信息")
                return {
                    "asin": asin,
                    "primary": "",
                    "secondary": "",
                    "primary_cleaned": "",
                    "secondary_cleaned": "",
                    "status": "no_delivery_info",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }

            # 优先用更精确的 xpath 获取主要配送信息
            primary = self.safe_get_text(
                page, 
                '//*[@id="mir-layout-DELIVERY_BLOCK-slot-PRIMARY_DELIVERY_MESSAGE_LARGE"]/span/span'
            )
            # 优先用更精确的 xpath 获取次要配送信息
            secondary = self.safe_get_text(
                page, 
                '//*[@id="mir-layout-DELIVERY_BLOCK-slot-SECONDARY_DELIVERY_MESSAGE_LARGE"]/span/span'
            )
            # 如果都没有获取到，尝试原有选择器
            if not primary:
                primary = self.safe_get_text(
                    page, 
                    '//div[@id="mir-layout-DELIVERY_BLOCK-slot-PRIMARY_DELIVERY_MESSAGE_LARGE"]'
                )
            if not secondary:
                secondary = self.safe_get_text(
                    page, 
                    '//div[@id="mir-layout-DELIVERY_BLOCK-slot-SECONDARY_DELIVERY_MESSAGE_LARGE"]'
                )
            # 如果还没有获取到，尝试其他可能的选择器
            if not primary and not secondary:
                alternative_selectors = [
                    '//div[@id="availability"]//span',
                    '//div[@id="priceblock_dealprice"]//span',
                    '//div[contains(@class, "delivery")]//span',
                    '//div[contains(@class, "shipping")]//span'
                ]
                for selector in alternative_selectors:
                    text = self.safe_get_text(page, selector)
                    if text:
                        primary = text
                        break
            # 清洗配送信息
            primary_cleaned = self.clean_delivery_message(primary) if primary else ""
            secondary_cleaned = self.clean_delivery_message(secondary) if secondary else ""

            result = {
                "asin": asin,
                "primary": primary,
                "secondary": secondary,
                "primary_cleaned": primary_cleaned,
                "secondary_cleaned": secondary_cleaned,
                "status": "success" if (primary or secondary) else "no_delivery_info",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            self.logger.info(f"ASIN {asin}: Primary='{primary}', Secondary='{secondary}'")
            return result
        except Exception as e:
            self.logger.error(f"提取配送信息失败 {asin}: {e}")
            return {
                "asin": asin,
                "primary": "",
                "secondary": "",
                "primary_cleaned": "",
                "secondary_cleaned": "",
                "status": "error",
                "error": str(e),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
    def save_items(self, items, task_id=None):
        """
        保存抓取结果到 data_amazon_asin_attr 表
        根据unique_id判断是否存在，存在则更新，不存在则新增
        开启事务，如果新增流程完成，则更新task_listing表对应字段状态修改为10，异常修改为20
        """
        try:
            connection = pymysql.connect(**self.mysql_config)
            with connection.cursor() as cursor:
                # 开启事务
                connection.begin()
                
                try:
                    for item in items:
                        asin = item.get('asin', '')
                        site = item.get('site', '')
                        primary = item.get('primary', '')
                        secondary = item.get('secondary', '')
                        primary_cleaned = item.get('primary_cleaned', '')
                        secondary_cleaned = item.get('secondary_cleaned', '')
                        status = item.get('status', '')
                        error = item.get('error', '')
                        task_id = item.get('task_id', task_id)

                        # 构造unique_id (asin_site)
                        unique_id = f"{asin}_{site}".lower()
                        
                        # 失败时写入失败原因
                        if status == 'no_delivery_info':
                            primary = '无到货信息'
                            secondary = '无到货信息'
                            primary_cleaned = '无到货信息'
                            secondary_cleaned = '无到货信息'
                        elif status == 'error':
                            primary = error[:10] if error else '抓取异常'
                            secondary = error[:10] if error else '抓取异常'
                            primary_cleaned = '抓取异常'
                            secondary_cleaned = '抓取异常'

                        # 检查记录是否存在
                        check_sql = "SELECT id FROM data_amazon_asin_attr WHERE unique_id = %s"
                        cursor.execute(check_sql, (unique_id,))
                        result = cursor.fetchone()
                        
                        current_time = int(time.time())
                        
                        if result:
                            # 记录存在，执行更新操作
                            update_sql = """
                            UPDATE data_amazon_asin_attr
                            SET primary_delivery_message = %s,
                                secondary_delivery_message = %s,
                                primary_delivery_message_cleaned = %s,
                                secondary_delivery_message_cleaned = %s,
                                update_time = CURRENT_TIMESTAMP
                            WHERE unique_id = %s
                            """
                            cursor.execute(update_sql, (primary, secondary, primary_cleaned, secondary_cleaned, unique_id))
                        else:
                            # 记录不存在，执行插入操作
                            insert_sql = """
                            INSERT INTO data_amazon_asin_attr (
                                tenant_id, company_id, unique_id, app_id, task_id, datetime, task_time,
                                data_status, user_id, username, platform_account, site, asin, status,
                                has_cart, title, keywords, short_desc1, short_desc2, short_desc3, short_desc4,
                                short_desc5, detail, tech_spec, product_desc, detail_seller_info, img_url,
                                url, primary_delivery_message, secondary_delivery_message,
                                primary_delivery_message_cleaned, secondary_delivery_message_cleaned,
                                create_time, update_time
                            ) VALUES (
                                0, 1, %s, 13, %s, %s, %s,
                                1, 0, '', '', %s, %s, 10,
                                0, '', '', '', '', '', '',
                                '', '', '{}', '', '', '',
                                %s, %s, %s,
                                %s, %s,
                                %s, CURRENT_TIMESTAMP
                            )
                            """
                            # 获取任务的时间信息
                            task_datetime = 0
                            task_time = 0
                            if task_id:
                                task_sql = "SELECT datetime, create_time FROM task_listing WHERE id = %s"
                                cursor.execute(task_sql, (task_id,))
                                task_result = cursor.fetchone()
                                if task_result:
                                    task_datetime = task_result[0] if task_result[0] else 0
                                    task_time = task_result[1] if task_result[1] else 0
                            
                            cursor.execute(insert_sql, (
                                unique_id, task_id or 0, task_datetime, task_time,
                                site, asin, 
                                f"https://www.amazon.{amazon_sites.get(site.upper(), {}).get('url', 'amazon.com')}/dp/{asin}?psc=1",
                                primary, secondary,
                                primary_cleaned, secondary_cleaned,
                                current_time
                            ))
                    
                    # 提交事务
                    connection.commit()
                    self.logger.info(f"已写入 {len(items)} 条结果到 data_amazon_asin_attr")
                    
                    # 如果任务ID存在，更新task_listing表状态为10（已完成）
                    if task_id:
                        self.update_task_status(task_id, 10)
                        
                except Exception as e:
                    # 回滚事务
                    connection.rollback()
                    self.logger.error(f"保存数据到 data_amazon_asin_attr 失败: {e}")
                    
                    # 如果任务ID存在，更新task_listing表状态为20（异常）
                    if task_id:
                        self.update_task_status(task_id, 20)
                    
                    raise
            
            connection.close()
        except Exception as e:
            self.logger.error(f"数据库操作失败: {e}")
            
    def run(self):
        """
        运行爬虫
        """
        try:
            self.logger.info("开始运行Amazon配送信息爬虫")
            super().run()
        except Exception as e:
            self.logger.error(f"爬虫运行出错: {e}")
        finally:
            # 确保浏览器关闭
            self.close_browser()
            
    def __del__(self):
        """
        析构函数，确保资源释放
        """
        try:
            self.close_browser()
        except:
            pass
            

if __name__ == "__main__":
    # 创建爬虫实例 - 使用单线程
    spider = AmazonDeliverySpider(
        thread_count=1,  # 单线程避免Playwright线程冲突
    )
    
    # 运行爬虫
    spider.run()
