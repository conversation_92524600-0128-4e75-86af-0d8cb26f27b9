import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    new_layer_name = ""
    if args is None:
        ps_instance = None
        file_path = ""
        layer_name = ""
    else:
        ps_instance = args.get("ps_instance", None)
        file_path = args.get("file_path", "")
        layer_name = args.get("layer_name", "")
    try:
        new_layer_name = xbot_visual.process.invoke_module(module="invoke_module", package=__name__, function="add_smart_object_layer_by_image", params={
            "ps_instance": ps_instance,
            "filepath": file_path,
            "layer_name": layer_name,
        }, _block=("添加图片图层", 1, "调用模块"))
    finally:
        args["new_layer_name"] = new_layer_name
