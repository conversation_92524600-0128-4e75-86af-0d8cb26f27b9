# -*- coding:UTF-8 -*-
# @FileName  :LingXingSessionPage.py
# @Time      :2024/12/17 17:58
# <AUTHOR>
import base64
import random
import time
import traceback
from math import ceil
from utils_mrc.pub_fun import *
from utils_mrc.MysqlHelper import *
from utils_mrc.SpiderTools import IPPool, SessionPageManager
from utils_mrc.RedisClient import *
import json
import hashlib
from DrissionPage import SessionPage, SessionOptions, WebPage
import requests
import pandas as pd
from concurrent.futures import ProcessPoolExecutor, as_completed
from queue import Queue, Empty
from multiprocessing.managers import BaseManager
from multiprocessing import Manager

db_cookie = RedisClient('cookies', 'lingxing')
db_account = RedisClient('accounts', 'lingxing')


class LingXingSessionPage(SessionPageManager):

    def __init__(self, user='jszg01', proxy_type=0):
        super().__init__(proxy_type)
        self.user = user
        self.proxy_type = proxy_type
        self.so.headers.update({
            "connection": "keep-alive",
            "X-AK-Company-Id": "901122764666708480",
            "auth-token": self.get_token(),
            "AK-Client-Type": "web",
            "AK-Origin": "https://muke.lingxing.com",
            "Origin": "https://muke.lingxing.com",
            # "Referer": "https://muke.lingxing.com/",
        })
        self.page.set.headers(self.so.headers)

        self.cur_info = {}
        self.task_params = {}

        # self.yxy_url = "https://erpapi.yxyglobal.com"
        self.yxy_url = "http://fba.zs.com"

    def get_token(self):
        # 获取端口号
        if self.user:
            content = db_cookie.get(self.user)  # 获取账号cookies信息
        else:
            content = db_cookie.random()
        self.token = content
        return content

    def logging(self, msg, tip='INFO'):
        """logging"""
        info = f'[{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())} {tip}]: <{self.user}>:{msg}'
        print(info)

    def check_erp_user(self):
        url = "https://gw.lingxingerp.com/newadmin/api/user/manage/myInfo?req_time_sequence=%2Fnewadmin%2Fapi%2Fuser%2Fmanage%2FmyInfo$$2"
        self.page.set.header("auth-token", self.get_token())
        self.page.get(url)
        if '已过期' in self.page.html or self.page.json.get('code') != 1:
            logging(f'当前登录账号erp已过期，尝试登录重新获取...')
        else:
            logging(f'当前登录账号erp有效，继续执行...')

    def check_page_json(self, response=None):
        if response:
            url = response.url
            result = response.json() if response else {}
        else:
            url = self.page.url
            result = self.page.json or {}
        if not result:
            self.logging('响应json为空')
            time.sleep(random.uniform(2, 3))
            raise Exception(f"请求{url}失败,响应json为空:{self.page.html}")
        elif any(x in result['msg'] for x in ['频繁', '繁忙', '稍后']):
            self.logging(result)
            time.sleep(random.uniform(2, 3))
            raise Exception(f"请求{url}失败:{self.page.html}")
        elif any(x in result['msg'] for x in ['登录', '过期']):
            token = self.get_token()
            self.page.set.header("auth-token", token)
            self.so.headers.update({'auth-token': token})
            self.logging(result)
            self.logging(f'当前Token：', token)
            time.sleep(random.uniform(2, 3))
            raise Exception(f"请求【{url}】的cookies异常:{self.page.html}")
        elif result['code'] != 1:
            self.logging(result)
            time.sleep(1)
            raise Exception(f"请求{url}异常:{result}")
        return result

    def api_get_profile_list(self):
        url = "https://ads.lingxing.com/common/common_list/common_list/get_profile_list"
        data = {
            'is_with_report_date': '0',
        }
        self.page.set.header("auth-token", self.get_token())
        self.page.post(url, data=data)
        result = self.check_page_json()
        data_list = result.get('data') or []
        if data_list:
            sql = """
                INSERT INTO rpa.data_lingxing_account (alias, profile_id, country, type, create_time)
                VALUES (%(alias)s, %(profile_id)s, %(country)s, %(type)s, UNIX_TIMESTAMP())
                ON DUPLICATE KEY UPDATE
                    alias=VALUES(alias),
                    country=VALUES(country),
                    type=VALUES(type)
            """
            MS.insert_many(sql, data_list)
            if MS.err:
                self.logging(f"{self.cur_info['alias']} 数据操作失败 {MS.err}")
            else:
                self.logging(f"{len(data_list)}条数据操作成功")
        return result

    def api_newadmin_custom_detail(self) -> str:
        '''
        获取订单导出字段
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/newadmin/custom/detail"
        params = {
            "key": "orderExportField8",
            "req_time_sequence": "/newadmin/custom/detail$$10"
        }
        self.page.get(url, params=params)
        result = self.check_page_json()
        # fields_str = "sellerName,marketplace,itemPriceCurrencyCode,amazonOrderId,orderStatus,requestReviewErrorMsg,buyerName,buyerEmail,fulfillmentChannel,salesChannel,purchaseDateLocal,postedDate,earliestShipDate,latestShipDate,lastUpdateDate,remark,sellerName,marketplace,itemPriceCurrencyCode,amazonOrderId,orderStatus,requestReviewErrorMsg,buyerName,buyerEmail,fulfillmentChannel,salesChannel,purchaseDateLocal,postedDate,earliestShipDate,latestShipDate,lastUpdateDate,remark,shipmentDateLocale,shipServiceLevel,carrier,trackingNumber,estimatedArrivalDate,shipmentDateLocale,shipServiceLevel,carrier,trackingNumber,estimatedArrivalDate,name,phone,postalCode,address,city,stateOrRegion,countryCode,name,phone,postalCode,address,city,stateOrRegion,countryCode,asin,sellerSku,title,orderItemId,sku,productName,quantityOrdered,unitPriceAmount,salesPriceAmount,promotionIds,feeCost,commissionCost,typeName,asin,sellerSku,title,orderItemId,sku,productName,quantityOrdered,unitPriceAmount,salesPriceAmount,taxAmount,cgPrice,cgTransportCosts,fbaShipmentAmount,promotionAmount,promotionIds,commissionAmount,otherAmount,feeCost,commissionCost,typeName,profit,profitRate,isAssessed,isBusinessOrder,isPremiumOrder,isPrime,isPromotionValue,isReplacementOrder,isReplacedOrder,isReturnOrder,isReturn,returnPriceAmount,orderTotalAmount,isAssessed,isBusinessOrder,isPremiumOrder,isPrime,isPromotionValue,isReplacementOrder,isReplacedOrder,isReturnOrder,isReturn,returnPriceAmount,orderTotalAmount"
        fields_str = "sellerName,marketplace,itemPriceCurrencyCode,amazonOrderId,orderStatus,requestReviewErrorMsg,buyerName,buyerEmail,fulfillmentChannel,salesChannel,purchaseDateLocal,postedDate,earliestShipDate,latestShipDate,lastUpdateDate,remark,shipmentDateLocale,shipServiceLevel,carrier,trackingNumber,estimatedArrivalDate,name,phone,postalCode,address,city,stateOrRegion,countryCode,asin,sellerSku,title,orderItemId,sku,productName,quantityOrdered,unitPriceAmount,salesPriceAmount,taxAmount,cgPrice,cgTransportCosts,fbaShipmentAmount,promotionAmount,promotionIds,commissionAmount,otherAmount,pointsMonetaryValue,feeCost,commissionCost,typeName,profit,profitRate,isAssessed,isBusinessOrder,isPremiumOrder,isPrime,isPromotionValue,isReplacementOrder,isReplacedOrder,isReturnOrder,isReturn,returnPriceAmount,orderTotalAmount"
        try:
            info = json.loads(result['info'])
            fields = []
            for item in info:
                fields.extend(item['checkeds'])
            fields_str = ','.join(fields)
        except:
            self.logging(f'订单导出字段解析异常，使用默认字段！')
        # 在返回前去重，保持顺序
        seen = set()
        dedup_fields = []
        for f in fields_str.split(','):
            if f not in seen:
                dedup_fields.append(f)
                seen.add(f)
        return ','.join(dedup_fields)

    def api_download_downloadCenterReport_cancelReport(self, report_id: str) -> bool:
        '''
        取消报告
        :param report_id:
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://muke.lingxing.com/api/download/downloadCenterReport/cancelReport"
        params = {
            "report_id": report_id,
            "req_time_sequence": "/api/download/downloadCenterReport/cancelReport$$14"
        }
        self.page.get(url, params=params)
        result = self.check_page_json()
        self.logging(f'{report_id} 已取消生成！')
        return True

    def api_download_downloadCenterReport_retryReportExport(self, report_id: str) -> bool:
        '''
        取消报告
        :param report_id:
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://muke.lingxing.com/api/download/downloadCenterReport/retryReportExport"
        data = {
            "report_id": report_id,
            "req_time_sequence": "/api/download/downloadCenterReport/retryReportExport$$1"
        }
        self.page.post(url, json=data)
        result = self.check_page_json()
        self.logging(f'{report_id} 已重新生成！')
        return True

    def api_ware_house_newSimpleLists(self) -> list:
        '''
        仓库列表
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://muke.lingxing.com/api/ware_house/newSimpleLists"
        params = {
            "type": "2",
            "is_own": "",
            "is_api": "",
            "is_company": "0",
            "length": "4000",
            "req_time_sequence": "/api/ware_house/newSimpleLists$$1"
        }
        self.page.get(url, params=params)
        result = self.check_page_json()
        return result['list']

    def api_download_downloadCenterReport_getReportData(self, start_time=None, end_time=None) -> list:
        '''
        获取当天导出报表数据
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        today = datetime.today().strftime("%Y-%m-%d")
        start_time = start_time or today
        end_time = end_time or today
        url = "https://muke.lingxing.com/api/download/downloadCenterReport/getReportData"
        params = {
            "offset": "0",
            "length": "200",
            "report_time_type": "0",
            "start_time": start_time,
            "end_time": end_time,
            "req_time_sequence": "/api/download/downloadCenterReport/getReportData$$1"
        }
        self.page.get(url, params=params, headers=self.so.headers)
        result = self.check_page_json()
        data_list = result['data']['list']
        return data_list

    def get_today_report_data(self, report_name: str = '', report_time: datetime = None, retry_count=0):
        '''
        获取当天导出报表数据
        :param report_name:
        :param report_time:
        :param retry_count:
        :return:
        '''
        while True:
            data_list = self.api_download_downloadCenterReport_getReportData()

            if report_name and report_time:
                conditions = f'report_name:{report_name}, report_time>={report_time.strftime("%Y-%m-%d %H:%M:%S")}'
                self.logging(f'开始查找{conditions}')
                target_item = next(
                        (
                            item for item in data_list
                            if item.get('report_name') == report_name
                               and datetime.strptime(item.get('gmt_create'), '%Y-%m-%d %H:%M:%S') >= report_time
                        ),
                        {}  # 如果没有找到符合条件的项，返回 {}
                )
                if retry_count >= 3:
                    self.logging(f'超过最大重试次数{retry_count}次没有找到符合条件的项：{conditions}。')
                    return {}
                elif not target_item:
                    if retry_count < 3:
                        retry_count = retry_count + 1
                        time.sleep(2)
                        continue
                return target_item
            return data_list

    def api_storage_fbaLists(self, param_payload: dict = None, only_total=False):
        '''
        获取FBA库存数据列表
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        json_data = {
            "cid": "",
            "bid": "",
            "attribute": "",
            "asin_principal": "",
            "search_field": "sku",
            "is_cost_page": 0,
            "status": "",
            "senior_search_list": "[]",
            "offset": 0,
            "length": 200,
            "fulfillment_channel_type": "FBA",
            "is_hide_zero_stock": "1",
            "is_parant_asin_merge": "0",
            "is_contain_del_ls": "0",
            "req_time_sequence": "/api/storage/fbaLists$$1"
        }
        url = "https://muke.lingxing.com/api/storage/fbaLists"
        if param_payload: json_data.update(param_payload)
        self.page.post(url, json=json_data)
        result = self.check_page_json()
        if only_total: return result['total']
        return result['list']

    def match_warehouses_id(self, store_list, warehouses):
        """
        输入一个 store_list，返回匹配的仓库信息字典。

        :param store_list: 商店列表
        :param warehouses: 仓库列表
        :return: 匹配的仓库id
        """
        # 构建一个从仓库名称到仓库信息的映射
        warehouse_name_map = {warehouse["name"]: warehouse for warehouse in warehouses}
        # 提前提取所有仓库名称以便后续快速查找
        all_warehouse_names = set(warehouse_name_map.keys())
        matched_store = {}
        for store in store_list:
            # 检查每个仓库名称是否包含当前商店名称
            for name in all_warehouse_names:
                if store in name:
                    warehouse = warehouse_name_map[name]
                    matched_store[warehouse["wid"]] = warehouse["name"]
        wids = ','.join(map(str, matched_store.keys()))
        return wids

    def generate_inventory_file(self, param_payload: dict = None):
        self.page.set.header("auth-token", self.get_token())
        json_data = {
            # "wids": "10,11,12,30,31,32,33",
            "wids": "33",
            "cid": "",
            "bid": "",
            "attribute": "",
            "asin_principal": "",
            "search_field": "sku",
            "is_cost_page": 0,
            "status": "",
            "senior_search_list": "[]",
            "offset": 0,
            "length": 200,
            "fulfillment_channel_type": "FBA",
            "is_hide_zero_stock": "0",
            "is_parant_asin_merge": "0",
            "is_contain_del_ls": "0",
            "selected_fields": "",
            "has_export_origin": False,
            "req_time_sequence": "/api/storage/fbaExport$$1"
        }
        if param_payload: json_data.update(param_payload)

        # wids = json_data['wids']
        # total = self.api_storage_fbaLists({'wids': wids}, only_total=True)
        # if total > 200000:
        #     raise Exception(f'检测到当前分组店铺数量: {wids.count(",") + 1},库存数据为 {total} 超过 20000 条，主动终止导出！')
        wait = 30

        url = "https://muke.lingxing.com/api/storage/fbaExport"
        for _ in range(3):
            try:
                self.page.post(url, json=json_data)
                result = self.check_page_json()
                report_id = result['data']['report_id']
                self.logging(f'库存报表生成成功。report_id:{report_id}')
                return report_id
            except Exception as e:
                if '频繁' in str(e):
                    self.logging(f'检测到频繁请求，等待 {wait} 秒后重试...')
                    time.sleep(wait)
                    continue

    def prepare_order_export(self, retry_count=1):
        """
        在生成订单报表前，先发送设置请求来验证和准备资源
        参考 index.py 中的请求逻辑
        """
        try:
            self.page.set.header("auth-token", self.get_token())
            
            # 第一步：发送设置请求
            set_url = "https://gw.lingxingerp.com/newadmin/custom/set"
            set_data = {
                "info": "{\"sellerName\":{\"id\":\"sellerName\",\"title\":\"基础信息\",\"name\":\"店铺\",\"isShow\":true},\"marketplace\":{\"id\":\"marketplace\",\"title\":\"基础信息\",\"name\":\"国家\",\"isShow\":true},\"itemPriceCurrencyCode\":{\"id\":\"itemPriceCurrencyCode\",\"title\":\"基础信息\",\"name\":\"订单币种\",\"isShow\":true},\"amazonOrderId\":{\"id\":\"amazonOrderId\",\"title\":\"基础信息\",\"name\":\"订单号\",\"isShow\":true},\"orderStatus\":{\"id\":\"orderStatus\",\"title\":\"基础信息\",\"name\":\"订单状态\",\"isShow\":true},\"requestReviewErrorMsg\":{\"id\":\"requestReviewErrorMsg\",\"title\":\"基础信息\",\"name\":\"请求评论状态\",\"isShow\":true},\"buyerName\":{\"id\":\"buyerName\",\"title\":\"基础信息\",\"name\":\"买家姓名\",\"isShow\":true},\"buyerEmail\":{\"id\":\"buyerEmail\",\"title\":\"基础信息\",\"name\":\"买家邮箱\",\"isShow\":true},\"fulfillmentChannel\":{\"id\":\"fulfillmentChannel\",\"title\":\"基础信息\",\"name\":\"订单类型\",\"isShow\":true},\"salesChannel\":{\"id\":\"salesChannel\",\"title\":\"基础信息\",\"name\":\"销售渠道\",\"isShow\":true},\"purchaseDateLocal\":{\"id\":\"purchaseDateLocal\",\"title\":\"基础信息\",\"name\":\"订购日期\",\"isShow\":true},\"postedDate\":{\"id\":\"postedDate\",\"title\":\"基础信息\",\"name\":\"结算时间\",\"isShow\":true},\"earliestShipDate\":{\"id\":\"earliestShipDate\",\"title\":\"基础信息\",\"name\":\"发货时限（最早）\",\"isShow\":true},\"latestShipDate\":{\"id\":\"latestShipDate\",\"title\":\"基础信息\",\"name\":\"发货时限（最迟）\",\"isShow\":true},\"lastUpdateDate\":{\"id\":\"lastUpdateDate\",\"title\":\"基础信息\",\"name\":\"更新时间\",\"isShow\":true},\"remark\":{\"id\":\"remark\",\"title\":\"基础信息\",\"name\":\"备注\",\"isShow\":true},\"shipmentDateLocale\":{\"id\":\"shipmentDateLocale\",\"title\":\"发货信息\",\"name\":\"发货时间\",\"isShow\":true},\"shipServiceLevel\":{\"id\":\"shipServiceLevel\",\"title\":\"发货信息\",\"name\":\"配送服务\",\"isShow\":true},\"carrier\":{\"id\":\"carrier\",\"title\":\"发货信息\",\"name\":\"物流商\",\"isShow\":true},\"trackingNumber\":{\"id\":\"trackingNumber\",\"title\":\"发货信息\",\"name\":\"运单号\",\"isShow\":true},\"estimatedArrivalDate\":{\"id\":\"estimatedArrivalDate\",\"title\":\"发货信息\",\"name\":\"收货预计\",\"isShow\":true},\"name\":{\"id\":\"name\",\"title\":\"收货地址\",\"name\":\"收件人\",\"isShow\":true},\"phone\":{\"id\":\"phone\",\"title\":\"收货地址\",\"name\":\"电话\",\"isShow\":true},\"postalCode\":{\"id\":\"postalCode\",\"title\":\"收货地址\",\"name\":\"邮编\",\"isShow\":true},\"address\":{\"id\":\"address\",\"title\":\"收货地址\",\"name\":\"地址\",\"isShow\":true},\"city\":{\"id\":\"city\",\"title\":\"收货地址\",\"name\":\"城市\",\"isShow\":true},\"stateOrRegion\":{\"id\":\"stateOrRegion\",\"title\":\"收货地址\",\"name\":\"州/地区\",\"isShow\":true},\"countryCode\":{\"id\":\"countryCode\",\"title\":\"收货地址\",\"name\":\"国家/地区\",\"isShow\":true},\"asin\":{\"id\":\"asin\",\"title\":\"商品信息\",\"name\":\"ASIN\",\"isShow\":true},\"sellerSku\":{\"id\":\"sellerSku\",\"title\":\"商品信息\",\"name\":\"MSKU\",\"isShow\":true},\"title\":{\"id\":\"title\",\"title\":\"商品信息\",\"name\":\"标题\",\"isShow\":true},\"orderItemId\":{\"id\":\"orderItemId\",\"title\":\"商品信息\",\"name\":\"Order Item ID\",\"isShow\":true},\"sku\":{\"id\":\"sku\",\"title\":\"商品信息\",\"name\":\"SKU\",\"isShow\":true},\"productName\":{\"id\":\"productName\",\"title\":\"商品信息\",\"name\":\"品名\",\"isShow\":true},\"quantityOrdered\":{\"id\":\"quantityOrdered\",\"title\":\"商品信息\",\"name\":\"数量\",\"isShow\":true},\"unitPriceAmount\":{\"id\":\"unitPriceAmount\",\"title\":\"商品信息\",\"name\":\"单价\",\"isShow\":true},\"salesPriceAmount\":{\"id\":\"salesPriceAmount\",\"title\":\"商品信息\",\"name\":\"销售收益\",\"isShow\":true},\"taxAmount\":{\"id\":\"taxAmount\",\"title\":\"商品信息\",\"name\":\"税费\",\"isShow\":true},\"cgPrice\":{\"id\":\"cgPrice\",\"title\":\"商品信息\",\"name\":\"采购成本\",\"isShow\":true},\"cgTransportCosts\":{\"id\":\"cgTransportCosts\",\"title\":\"商品信息\",\"name\":\"头程费用\",\"isShow\":true},\"fbaShipmentAmount\":{\"id\":\"fbaShipmentAmount\",\"title\":\"商品信息\",\"name\":\"FBA费\",\"isShow\":true},\"promotionAmount\":{\"id\":\"promotionAmount\",\"title\":\"商品信息\",\"name\":\"促销费\",\"isShow\":true},\"promotionIds\":{\"id\":\"promotionIds\",\"title\":\"商品信息\",\"name\":\"促销编码\",\"isShow\":true},\"commissionAmount\":{\"id\":\"commissionAmount\",\"title\":\"商品信息\",\"name\":\"平台费\",\"isShow\":true},\"otherAmount\":{\"id\":\"otherAmount\",\"title\":\"商品信息\",\"name\":\"其他\",\"isShow\":true},\"pointsMonetaryValue\":{\"id\":\"pointsMonetaryValue\",\"title\":\"商品信息\",\"name\":\"积分成本\",\"isShow\":true},\"feeCost\":{\"id\":\"feeCost\",\"title\":\"商品信息\",\"name\":\"站外推广费-本金\",\"isShow\":true},\"commissionCost\":{\"id\":\"commissionCost\",\"title\":\"商品信息\",\"name\":\"站外推广费-佣金\",\"isShow\":true},\"typeName\":{\"id\":\"typeName\",\"title\":\"商品信息\",\"name\":\"推广类型\",\"isShow\":true},\"profit\":{\"id\":\"profit\",\"title\":\"商品信息\",\"name\":\"毛利润\",\"isShow\":true},\"profitRate\":{\"id\":\"profitRate\",\"title\":\"商品信息\",\"name\":\"毛利率\",\"isShow\":true},\"isAssessed\":{\"id\":\"isAssessed\",\"title\":\"其他信息\",\"name\":\"是否推广\",\"isShow\":true},\"isBusinessOrder\":{\"id\":\"isBusinessOrder\",\"title\":\"其他信息\",\"name\":\"是否B2B\",\"isShow\":true},\"isPremiumOrder\":{\"id\":\"isPremiumOrder\",\"title\":\"其他信息\",\"name\":\"是否优质配送\",\"isShow\":true},\"isPrime\":{\"id\":\"isPrime\",\"title\":\"其他信息\",\"name\":\"是否会员\",\"isShow\":true},\"isPromotionValue\":{\"id\":\"isPromotionValue\",\"title\":\"其他信息\",\"name\":\"是否促销\",\"isShow\":true},\"isReplacementOrder\":{\"id\":\"isReplacementOrder\",\"title\":\"其他信息\",\"name\":\"换货订单\",\"isShow\":true},\"isReplacedOrder\":{\"id\":\"isReplacedOrder\",\"title\":\"其他信息\",\"name\":\"是否已换货\",\"isShow\":true},\"isReturnOrder\":{\"id\":\"isReturnOrder\",\"title\":\"其他信息\",\"name\":\"是否退货\",\"isShow\":true},\"isReturn\":{\"id\":\"isReturn\",\"title\":\"其他信息\",\"name\":\"是否退款\",\"isShow\":true},\"returnPriceAmount\":{\"id\":\"returnPriceAmount\",\"title\":\"其他信息\",\"name\":\"退款总金额\",\"isShow\":true},\"orderTotalAmount\":{\"id\":\"orderTotalAmount\",\"title\":\"其他信息\",\"name\":\"订单总金额\",\"isShow\":true}}",
                "key": "orderExportFields-exportMemory",
                "req_time_sequence": f"/newadmin/custom/set$${retry_count}"
            }
            
            self.page.post(set_url, json=set_data)
            result = self.check_page_json()
            self.logging(f'订单报表导出设置请求已发送 (retry_count={retry_count})')
            self.logging(f'设置请求返回结果: {result}')
            
            # 等待一下确保设置生效
            time.sleep(2)
            
            return True
            
        except Exception as e:
            self.logging(f'订单报表导出准备失败: {e}')
            return False

    def generate_order_file(self, param_payload: dict = None):
        # 先进行导出准备，如果第一次失败，重试第二次
        retry_count = 1
        if not self.prepare_order_export(retry_count):
            self.logging('第一次订单报表导出准备失败，尝试第二次...')
            retry_count = 2
            if not self.prepare_order_export(retry_count):
                self.logging('订单报表导出准备失败，无法继续生成报表')
                return False
            
        self.page.set.header("auth-token", self.get_token())
        d = get_last_n_days(31)
        # 1下生成订单报表
        json_data = {
            'startDate': d[-1],
            'endDate': d[0],
            'sortField': 'purchaseDateLocal',
            'sortType': 'desc',
            # 'sids': '',
            'searchField': 'amazonOrderId',
            'searchValue': '',
            'fulfillmentChannel': '',
            'isReturn': '',
            'isMcfOrder': '0',
            'pageNo': 1,
            'pageSize': 20,
            'exportFields': self.api_newadmin_custom_detail(),
            'req_time_sequence': f'/amz-order/api/order/createExportInfo$${retry_count}',
        }
        if param_payload: json_data.update(param_payload)
        print(json_data)
        url = "https://gw.lingxingerp.com/amz-order/api/order/createExportInfo"
        self.page.post(url, json=json_data)
        self.check_page_json()
        self.logging(f'订单报表已添加导出报表队列 (retry_count={retry_count})。')
        time.sleep(3)
        return True

    def rebuild_report(self, report: dict):
        report_id = report['report_id']
        report_name = report['report_name']
        self.api_download_downloadCenterReport_cancelReport(report_id)
        if report_name == 'sc订单导出':
            self.generate_order_file()
        elif report_name == '仓库管理-FBA库存':
            rs = MS100.get_dict_one('select request_param from rpa.`data_lingxing_report_generate` where report_id = %s', report_id)
            if not rs:
                self.logging(f"{report_name} {report_id} 未匹配wids,重新生成失败!")
                return
            request_param = json.loads(rs['request_param'])
            # self.generate_inventory_file({"wids": wids})
            self.generate_inventory_file(request_param)

    def check_report_status(self):
        MAX_WAIT_TIME = 3 * 3600
        SLEEP_INTERVAL = 120

        fields_to_update = [
            'report_name', 'report_date_range', 'report_type', 'report_status',
            'file_size', 'data_count', 'extra', 'gmt_create', 'gmt_modified',
            'action', 'content', 'progress'
        ]
        while True:
            try:
                reports = self.get_today_report_data()
                if not reports:
                    self.logging(f"当天未查询到报告!")
                    return False

                list_ok = []
                list_ing = []
                list_fail = []

                for report in reports:
                    values = [report.get(field) for field in fields_to_update]
                    values.append(report.get('report_id'))
                    sql = f"UPDATE rpa.`data_lingxing_report_generate` SET " + \
                          ", ".join([f"`{field}`=%s" for field in fields_to_update]) + \
                          " WHERE `report_id`=%s"
                    MS100.insert(sql, values)

                    report_status = report.get('report_status')
                    gmt_create = report.get('gmt_create')
                    if report_status == 2:  # 已完成
                        list_ok.append(report)
                    elif report_status == 1:  # 生成中
                        consuming_time = (datetime.now() - datetime.strptime(gmt_create, '%Y-%m-%d %H:%M:%S')).seconds
                        list_ing.append({**report, 'consuming': consuming_time})
                    elif report_status == 0:  # 待处理
                        list_ing.append(report)
                    else:  # 未知
                        list_fail.append(report)

                len_ok = len(list_ok)
                len_ing = len(list_ing)
                len_fail = len(list_fail)
                self.logging(f'当天总数量：{len(reports)}，已生成报表数量：{len_ok}，生成中：{len_ing}，生成失败：{len_fail}')

                if len_fail > 0:
                    for report in list_fail:
                        # self.rebuild_report(report)
                        self.logging(f'{report["report_id"]} {report["report_name"]} 当前状态 {report["report_status"]} 等待当天其他任务生成完毕后重新生成!')
                        if len_ing == 0:
                            self.logging(f'{report["report_id"]} {report["report_name"]} 生成失败。重新生成')
                            self.api_download_downloadCenterReport_retryReportExport(report["report_id"])  # -1 处理失败
                elif len_ing > 0:
                    for report in list_ing:
                        if report.get('report_status') == 0:
                            continue
                        # elif report.get('consuming') >= MAX_WAIT_TIME:
                        #     self.logging(f'生成文件时间超过{MAX_WAIT_TIME}秒，重新生成。')
                        #     self.rebuild_report(report)
                        #     continue
                        report_id = report.get('report_id')
                        report_name = report.get('report_name')
                        content = report.get('content')
                        progress = report.get('progress')
                        self.logging(f'{report_id}：【{report_name}】生成中，已耗时{report.get("consuming")}秒，当前进度【{progress}】，{content}。')
                    time.sleep(SLEEP_INTERVAL)
                else:
                    self.logging(f"所有报表生成完成。")
                    return True
            except Exception as e:
                err = traceback.format_exc()
                print(err)
                self.logging(e)
                fsmsg.send(None, '领星报表下载异常', err)
                time.sleep(SLEEP_INTERVAL)

    def cancel_other_report(self):
        t = get_last_n_days(7)
        start_date = t[-1]
        end_date = t[1]  # 截止昨天
        reports = self.api_download_downloadCenterReport_getReportData(start_date, end_date)
        if not reports:
            self.logging(f"近期未查询到报告!")
            return False

        count_ing = 0

        for report in reports:
            report_status = report.get('report_status')
            if report_status == 1:  # 生成中
                count_ing += 1
                try:
                    self.api_download_downloadCenterReport_cancelReport(report["report_id"])
                except Exception as e:
                    self.logging(f"取消{report['report_id']}失败，{e}")
                    fsmsg.send(None, f"取消{report['report_id']}失败，{e}")

        self.logging(f"已取消{count_ing}个生成中的非当天报表。")
        return True

    def calculate_and_export_inventory_warehouse_groupings(self):
        warehouses = self.api_ware_house_newSimpleLists()
        max_total = 200000
        best_group_size = 6

        # 计算合适的分组数
        num_warehouses = len(warehouses)
        for num_groups in range(best_group_size, num_warehouses + 1):
            # 按照num_groups对仓库进行分组
            group_size = len(warehouses) // num_groups
            groups = [warehouses[i:i + group_size] for i in range(0, len(warehouses), group_size)]
            results = []
            for index, group in enumerate(groups, 1):
                wids = ','.join(str(w['wid']) for w in group)
                total = self.api_storage_fbaLists({'wids': wids}, True)
                proportion = total / max_total
                print(f"分{num_groups}组，第{index}组，数量{total}, 占比{proportion:.2%}", end=' ')
                if proportion <= 0.8:
                    print(f"未超过 80%。")
                    results.append(wids)
                else:
                    print(f"{num_groups}分组不合适，已超过 80%。")
                    break
            if len(results) == num_groups:
                print(f"最优分组数：{num_groups}，每组总数：{group_size}。")
                # return results
                return num_groups  # 最优分组数
            print("当前分组不合适，继续尝试下一组...")
        raise ValueError("无法找到合适的分组数，每个分组的数量都超过了最大限制")

    def group_report_tasks(self, fetch_user=None, group_num=None, group_name='fba库存明细'):
        if fetch_user is None:
            fetch_user = [self.user]
        if not group_num:
            group_num = self.calculate_and_export_inventory_warehouse_groupings()
        fetch_count = len(fetch_user)
        sql = "INSERT INTO rpa.`data_lingxing_report_generate` (`unique_id`,`request_param`,`username`,`create_time`) VALUES (%s,%s,%s,unix_timestamp()) on duplicate key update `request_param`=VALUES(`request_param`),`username`=VALUES(`username`)"
        warehouses = self.api_ware_house_newSimpleLists()

        # 计算每个账号的基本分组数和剩余分组数
        base_group_per_user = group_num // fetch_count
        extra_groups = group_num % fetch_count

        group_size = len(warehouses) // group_num
        groups = [warehouses[i:i + group_size] for i in range(0, len(warehouses), group_size)]
        if len(groups) > group_num:
            # 合并多余的组到最后一组
            last_group = groups.pop()  # 移除最后一个组
            while len(groups) > group_num:
                last_group = groups.pop() + last_group  # 将移除的组合并到最后一个组
            groups[-1].extend(last_group)  # 将合并后的组添加到最后一个组

        for index, group in enumerate(groups):
            unique_id = f'{group_name}{index + 1}'
            wids = ','.join(str(w['wid']) for w in group)
            params = {"wids": wids}

            # 确定当前分组应该分配给哪个账号
            if (cur_index := index // base_group_per_user) < len(fetch_user):
                user_index = cur_index
            else:
                user_index = max(extra_groups, 0)
                extra_groups -= 1
            current_user = fetch_user[user_index]

            self.logging(f'{unique_id}，由账号{current_user}抓取,数据量为{len(group)}。')
            # 插入数据库
            MS100.insert(sql, (unique_id, json.dumps(params), current_user))

    def action_fetch_createInboundPlan(self):
        need_task_status = 5
        sql_sel = f"SELECT *,a.lingxing_id as platform_account_lingxing_id FROM ims.fba_need n left JOIN dms.data_platform_account a on a.id = n.platform_account_id  where n.target_store = '' and n.`status` in ({need_task_status}) and GROUP BY n.batch_no,fba_name"
        sql_sel = f"SELECT *,a.lingxing_id as platform_account_lingxing_id FROM ims.fba_need n left JOIN dms.data_platform_account a on a.id = n.platform_account_id  where n.target_store = '' and n.`status` in (6) and branch_id=16 GROUP BY n.batch_no,fba_name"
        sql_sel2 = f"SELECT id,msku FROM ims.fba_need where `status` in ({need_task_status}) and fba_name = %s and batch_no=%s"
        rs = MS100.get_dict(sql_sel)
        for row in rs:
            fba_name = row['fba_name']
            plan_name = f'预览测试-{fba_name}'
            sid = row['platform_account_lingxing_id']
            batch_no = row['batch_no']
            rs2 = MS100.get_dict(sql_sel2, (fba_name, batch_no,))
            msku_list = [row['msku'] for row in rs2]
            msku_info_ids = [row['id'] for row in rs2]
            inbound_plan_id = ''
            local_task_id = ''
            try:
                # 获取店铺地址
                address_info = self.api_shipment_address_list(sid)

                # 匹配msku信息
                msku_info = self.api_showAllOnline(sid, msku_list)

                # 批量设置预处理信息
                self.api_batchSetPrepInfo(sid, list(msku_info.keys()))

                # 创建STA计划
                request_body = {
                    "sid": sid,
                    "planName": plan_name,
                    "positionType": "2",
                    "remark": plan_name,
                    "address_id": address_info.get('id'),
                    "addressLine1": address_info['address1'],
                    "addressLine2": address_info['address2'],
                    "city": address_info['city'],
                    "companyName": "",
                    "countryCode": address_info['country_code'],
                    "email": address_info['email'],
                    "phoneNumber": address_info.get('phone', '10086'),
                    "postalCode": address_info['postal_code'],
                    "shipperName": plan_name,
                    "stateOrProvinceCode": address_info['state_or_province_code'],
                    "planCreateType": 1,
                    "inboundPlanItemBOS": [{
                        "local_name": info.get('local_name'),
                        "sku": info.get('sku'),
                        "asin": info.get('asin'),
                        "expiration": "",
                        "fnsku": info.get('fnsku'),
                        "labelOwner": "SELLER",
                        "msku": info.get('msku'),
                        "prepOwner": "SELLER",
                        "quantity": "1",
                        "sid": sid
                    } for info in msku_info.values()],
                    "req_time_sequence": "/amz-sta-server/inbound-plan/createInboundPlan$$1",
                }
                plan_info = self.api_createInboundPlan(request_body)

                inbound_plan_id = plan_info['inboundPlanId']
                local_task_id = plan_info['localTaskId']

                # 货件预览
                preview_info = self.api_shipmentPreView(inbound_plan_id, local_task_id, sid)
                if len(preview_info) > 1:
                    fsmsg.send('STA预览数据预警', f'STA预览数据结构异常,外层数量为{len(preview_info)}')
                shipmentInformationList = [{
                    'fba_name': fba_name,
                    # 'platform_account_id': row['platform_account_id'],
                    'target_store': item['wareHouseId'],
                    # 'item_list': [sub_item['msku'] for sub_item in item['itemList']]
                    'ids': msku_info_ids
                } for item in preview_info[0]['shipmentInformationList']]
                print(shipmentInformationList)
                self.yxy_api_updateWarehouseInfo(shipmentInformationList)
                pg.alert('请查看结果')
            except Exception as e:
                traceback.print_exc()
                self.logging(f'{fba_name}，生成预览失败！')
            finally:
                if inbound_plan_id and local_task_id:
                    self.api_cancel_inbound_plan(inbound_plan_id, local_task_id, plan_name, sid)

    def yxy_api_updateWarehouseInfo(self, data):
        '''
        获取卖家列表
        :return:
        '''
        requestBody = {
            'params': json.dumps(data),
        }
        req_local_erp(url=f'{self.yxy_url}/api/v1/index/post?c=ims/fba&a=updateWarehouseInfo', json_data=requestBody)
        return True

    def api_my_sellers(self):
        '''
        获取卖家列表
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        self.page.get('https://muke.lingxing.com/api/my/sellers?req_time_sequence=%2Fapi%2Fmy%2Fsellers$$1', )
        result = self.check_page_json()
        datas = {item['id']: item['name'] for item in result['list']}
        return datas

    def api_shipment_address_list(self, sid):
        '''
        查询店铺地址
        :param sid: 店铺ID
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = f"https://muke.lingxing.com/api/fba_address/shipment_address_list?sid={sid}&req_time_sequence=%2Fapi%2Ffba_address%2Fshipment_address_list$$1"
        self.page.get(url, headers=self.so.headers)
        result = self.check_page_json()
        return result['data'][0]

    def api_showAllOnline(self, sid, mskus):
        '''
        匹配msku信息
        :param sid: 店铺ID
        :param mskus: msku列表
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://muke.lingxing.com/api/fba_shipment/showAllOnline"
        params = {
            "offset": 0,
            "length": 200,
            "sids": [sid],
            "wid": "",
            "search_field": "msku",
            "search_value": "",
            "is_filter_inactive_incomplete": 0,
            "seniorSearchList": [
                {
                    "name": "MSKU",
                    "search_field": "msku",
                    "search_value": mskus
                }
            ],
            "req_time_sequence": "/api/fba_shipment/showAllOnline$$4"
        }
        self.page.post(url, json=params)
        result = self.check_page_json()
        return {item['msku']: item for item in result['list']}

    def api_getPrepListByMsku(self, sid, mskus):
        '''
        匹配msku信息2
        :param sid: 店铺ID
        :param mskus: msku列表
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://muke.lingxing.com/api/fba_shipment/getPrepListByMsku"
        params = {
            "mskus": ",".join(mskus),
            "sid": sid,
            "req_time_sequence": "/api/fba_shipment/getPrepListByMsku$$1"
        }
        self.page.post(url, json=params)
        result = self.check_page_json()
        return {item['msku']: item['PrepDetailsList'] for item in result['data']}

    def api_batchSetPrepInfo(self, sid, msku_list):
        batch_size = 20
        num_batches = ceil(len(msku_list) / batch_size)

        for batch_index in range(num_batches):
            start_index = batch_index * batch_size
            end_index = min(start_index + batch_size, len(msku_list))
            batch_msku_list = msku_list[start_index:end_index]

            mskuPrepInfo = [
                {
                    "prepOwnerConstraint": "NONE_ONLY",
                    "labelOwnerConstraint": "SELLER_ONLY",
                    "msku": msku,
                    "prepCategory": "NONE",
                    "prepTypes": ["ITEM_NO_PREP"]
                }
                for msku in batch_msku_list
            ]

            params = {
                "mskuPrepInfo": mskuPrepInfo,
                "sid": sid,
                "req_time_sequence": "/amz-sta-server/inbound-plan/batchSetPrepInfo$$2"
            }

            res = requests.post(
                    "https://gw.lingxingerp.com/amz-sta-server/inbound-plan/batchSetPrepInfo",
                    json=params,
                    headers={**self.so.headers, 'auth-token': self.get_token()}
            )

            if res.status_code != 200:
                print(f"批处理 {batch_index + 1} 失败，状态代码为 {res.status_code}: {res.text}")
                return False
        return True

    def api_createInboundPlan(self, request_body):
        '''
        创建STA计划
        :param request_body: 请求体
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-plan/createInboundPlan"

        retry_count = 0
        while True:
            if retry_count >= 2:
                raise Exception("创建STA计划失败超过2次，请检查请求参数是否正确")
            retry_count += 1

            r = self.page.session.post(url, json=request_body, headers={**self.so.headers, 'auth-token': self.get_token()})
            result = r.json()

            if result.get('code') == 1:
                return result['data']
            elif result.get('code') == 10002:
                # 获取需要更新的数据
                update_data = result.get('data', [])
                if not update_data:
                    raise Exception("返回代码10002但没有更新数据")
                # 创建msku到更新数据的映射
                msku_update_map = {item['msku']: item for item in update_data}
                # 更新request_body中的inboundPlanItemBOS
                for item in request_body['inboundPlanItemBOS']:
                    if item['msku'] in msku_update_map:
                        # 更新所有返回的字段
                        update_item = msku_update_map[item['msku']]
                        item.update(update_item)

                self.logging(f"更新了{len(update_data)}个商品的信息")
                continue

            elif result.get('code') == 10005:
                # 获取需要更新的数据
                update_data = result.get('data', [])
                if not update_data:
                    raise Exception("返回代码10005但没有更新数据")

                # 构建预处理信息
                msku_prep_info = [{
                    "prepOwnerConstraint": "NONE_ONLY",
                    "labelOwnerConstraint": "SELLER_ONLY",
                    "msku": item['msku'],
                    "prepCategory": "NONE",
                    "prepTypes": ["ITEM_NO_PREP"]
                } for item in update_data]

                # 批量设置预处理信息
                self.api_batchSetPrepInfo(request_body['sid'], msku_prep_info)
                self.logging(f"更新了{len(update_data)}个商品的预处理信息")
                continue

            # 其他错误情况
            raise Exception(f"创建STA计划失败:{result.get('code')} {result.get('msg')}")

    def api_generatePackingInformation(self, inbound_plan_id, local_task_id, sid):
        '''
        生成装箱信息
        :param inbound_plan_id: 入库计划ID
        :param local_task_id: 本地任务ID
        :param sid: 店铺ID
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-plan-item/generatePackingInformation"
        params = {
            "inboundPlanId": inbound_plan_id,
            "localTaskId": local_task_id,
            "sid": sid,
            "req_time_sequence": "/amz-sta-server/inbound-plan-item/generatePackingInformation$$1"
        }
        self.page.post(url, json=params)
        result = self.check_page_json()
        return result['data'][0]

    def api_getPackingList(self, inbound_plan_id, sid):
        '''
        获取装箱列表
        :param inbound_plan_id: 入库计划ID
        :param sid: 店铺ID
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-packing/getPackingList"
        params = {
            "inboundPlanId": inbound_plan_id,
            "sid": sid,
            "req_time_sequence": "/amz-sta-server/inbound-packing/getPackingList$$1"
        }
        self.page.post(url, json=params)
        result = self.check_page_json()
        return result['data'][0]

    def api_localSaveNew(self, save_json_param):
        '''
        保存装箱数据
        :param save_json_param: 保存参数
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-packing/localSaveNew"
        self.page.post(url, json=[save_json_param])
        return self.check_page_json()

    def api_setPackingNew(self, save_json_param):
        '''
        保存装箱数据2
        :param save_json_param: 保存参数
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-packing/setPackingNew"
        self.page.post(url, json=[save_json_param])
        return self.check_page_json()

    def api_shipmentPreView(self, inbound_plan_id, local_task_id, sid=None):
        '''
        货件预览
        :param inbound_plan_id: 入库计划ID
        :param local_task_id: 本地任务ID
        :param sid: 店铺ID
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-shipment/shipmentPreView"
        params = {
            "inboundPlanId": inbound_plan_id,
            "localTaskId": local_task_id,
            "req_time_sequence": "/amz-sta-server/inbound-shipment/shipmentPreView$$2"
        }
        if sid: params['sid'] = sid
        r = self.page.session.post(url, json=params, headers={**self.so.headers, 'auth-token': self.get_token()})
        result = r.json()
        if not result:
            self.logging(result)
            raise Exception(f"请求{url}失败:{self.page.html}")
        elif result['code'] != 1:
            self.logging(result)
            raise Exception(f"请求{url}异常:{result}")
        return result['data']['placementOptionList']

    def api_confirmPlacementOption(self, placement_option_id, local_task_id, inbound_plan_id, plan_name, position_type, shipment_ids, fees, discounts, sid):
        '''
        申报
        :param placement_option_id: 位置选项ID
        :param local_task_id: 本地任务ID
        :param inbound_plan_id: 入库计划ID
        :param plan_name: 计划名称
        :param position_type: 位置类型
        :param shipment_ids: 货件ID列表
        :param fees: 费用
        :param discounts: 折扣
        :param sid: 店铺ID
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-shipment/confirmPlacementOption"
        params = {
            "placementOptionId": placement_option_id,
            "localTaskId": local_task_id,
            "inboundPlanId": inbound_plan_id,
            "planName": plan_name,
            "positionType": position_type,
            "shipmentIds": shipment_ids,
            "fees": fees,
            "discounts": discounts,
            "sid": sid,
            "req_time_sequence": "/amz-sta-server/inbound-shipment/confirmPlacementOption$$1"
        }
        self.page.post(url, json=params)
        return self.check_page_json()

    def api_updateShipmentName(self, inbound_plan_id, shipment_id, sid, name):
        '''
        修改货件名称
        :param inbound_plan_id: 入库计划ID
        :param shipment_id: 货件ID
        :param sid: 店铺ID
        :param name: 货件名称
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-shipment/updateShipmentName"
        params = {
            "inboundPlanId": inbound_plan_id,
            "shipmentId": shipment_id,
            "sid": sid,
            "name": name,
            "req_time_sequence": "/amz-sta-server/inbound-shipment/updateShipmentName$$6"
        }
        self.page.post(url, json=params)
        return self.check_page_json()

    def api_shipmentDetailList(self, inbound_plan_id, local_task_id, sid):
        '''
        查询货件详情
        :param inbound_plan_id: 入库计划ID
        :param local_task_id: 本地任务ID
        :param sid: 店铺ID
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-shipment/shipmentDetailList"
        params = {
            "inboundPlanId": inbound_plan_id,
            "localTaskId": local_task_id,
            "sid": sid,
            "req_time_sequence": "/amz-sta-server/inbound-shipment/shipmentDetailList$$1"
        }
        self.page.post(url, json=params)
        result = self.check_page_json()
        return result['data'][0]

    def api_getDeliveryDateList(self, inbound_plan_id, shipment_id, sid):
        '''
        查询配送日期详情
        :param inbound_plan_id: 入库计划ID
        :param shipment_id: 货件ID
        :param sid: 店铺ID
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-shipment/getDeliveryDateList"
        params = {
            "inboundPlanId": inbound_plan_id,
            "shipmentId": shipment_id,
            "sid": sid,
            "req_time_sequence": "/amz-sta-server/inbound-shipment/getDeliveryDateList$$4"
        }
        self.page.post(url, json=params)
        result = self.check_page_json()
        return result['data']

    def api_getTransportList(self, inbound_plan_id, pallet_list, shipping_mode, shipping_solution, placement_option_id, shiping_time, shipment_confirmation_id, shipment_id, sid):
        '''
        查询配送方式详情
        :param inbound_plan_id: 入库计划ID
        :param pallet_list: 托盘列表
        :param shipping_mode: 运输方式
        :param shipping_solution: 运输解决方案
        :param placement_option_id: 位置选项ID
        :param shiping_time: 发货时间
        :param shipment_confirmation_id: 货件确认ID
        :param shipment_id: 货件ID
        :param sid: 店铺ID
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-shipment/getTransportList"
        params = [{
            "inboundPlanId": inbound_plan_id,
            "palletList": pallet_list,
            "shippingMode": shipping_mode,
            "shippingSolution": shipping_solution,
            "placementOptionId": placement_option_id,
            "shipingTime": shiping_time,
            "shipmentConfirmationId": shipment_confirmation_id,
            "shipmentId": shipment_id,
            "sid": sid
        }]
        self.page.post(url, json=params)
        result = self.check_page_json()
        return result['data'][0]['transportVOList']

    def api_cancel_inbound_plan(self, inbound_plan_id, local_task_id, plan_name, sid):
        '''
        取消货件
        :param inbound_plan_id: 入库计划ID
        :param local_task_id: 本地任务ID
        :param plan_name: 计划名称
        :param sid: 店铺ID
        :return:
        '''
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/amz-sta-server/inbound-plan/cancel"
        params = {
            "inboundPlanId": inbound_plan_id,
            "localTaskId": local_task_id,
            "planName": plan_name,
            "sid": sid,
            "req_time_sequence": "/amz-sta-server/inbound-plan/cancel$$1"
        }
        self.page.post(url, json=params)
        result = self.check_page_json()
        self.logging(f'【{plan_name}】 已取消STA')
        return True

    def api_order_list(self, param_payload: dict = None, only_total=False):
        '''
        获取订单列表数据
        :param param_payload: 请求参数
        :param only_total: 是否只返回总数
        :return: 订单列表或总数
        '''
        self.page.set.header("auth-token", self.get_token())
        json_data = {
            "startDate": get_last_n_days(3)[-1],  # 默认3天前
            "endDate": get_last_n_days(1)[0],  # 默认今天
            "sortField": "purchaseDateLocal",
            "sortType": "desc",
            "searchField": "amazonOrderId",
            "searchValue": "",
            "fulfillmentChannel": "",
            "isReturn": "",
            "isMcfOrder": "0",
            "pageNo": 1,
            "pageSize": 200,
            "req_time_sequence": "/amz-order/api/order/OrderList$$1"
        }
        if param_payload:
            json_data.update(param_payload)

        url = "https://gw.lingxingerp.com/amz-order/api/order/OrderList"
        # self.page.post(url, json=json_data, headers=self.so.headers)
        res = requests.post(url, json=json_data, headers=self.so.headers)
        result = self.check_page_json(res)
        if only_total:
            return result['data']['total']
        return result['data']['data']

    def api_fba_listing_list(self, param_payload: dict, only_total=False):
        """FBA Listing 列表接口"""
        self.page.set.header("auth-token", self.get_token())
        url = "https://gw.lingxingerp.com/listing-api/api/fbaListing/list"
        # 默认参数
        json_data = {
            "fulfillment_channel_type": "FBA",
            "length": 200,
            "mids": "",
            "offset": 0,
            "req_time_sequence": "/listing-api/api/fbaListing/list$$2",
            "search_field": "msku",
            "sids": "",
            "sort_field": "",
            "sort_type": "",
            "status": "1"
        }
        if param_payload:
            json_data.update(param_payload)
        self.page.post(url, json=json_data)
        result = self.check_page_json()
        
        # 添加错误处理
        if 'data' not in result:
            self.logging(f"API返回缺少data字段: {result}")
            return 0 if only_total else []
            
        if only_total:
            return result['data']['total']
        
        # 修正：使用'list'而不是'data'
        return result['data']['list']

    def api_fba_inventory_list(self, param_payload: dict = None, only_total=False):
        '''
        获取FBA库存列表数据
        :param param_payload: 请求参数  
        :param only_total: 是否只返回总数
        :return: 库存列表或总数
        '''
        self.page.set.header("auth-token", self.get_token())
        json_data = {
            "wids": "",
            "cid": "",
            "bid": "",
            "attribute": "",
            "asin_principal": "",
            "search_field": "sku",
            "is_cost_page": 0,
            "status": "",
            "senior_search_list": "[]",
            "offset": 0,
            "length": 200,
            "fulfillment_channel_type": "FBA",
            "is_hide_zero_stock": "0",
            "is_parant_asin_merge": "0",
            "is_contain_del_ls": "0",
            "req_time_sequence": "/api/storage/fbaLists$$1"
        }
        if param_payload:
            json_data.update(param_payload)

        url = "https://muke.lingxing.com/api/storage/fbaLists"
        self.page.post(url, json=json_data)
        result = self.check_page_json()
        if only_total:
            return result['total']
        
        # 处理数据
        data_list = []
        for info in result['list']:
            unique_id = f"{info['name']}_{info['seller_name']}_{info['asin']}{info['fnsku']}{info['seller_sku']}"
            info['unique_id'] = hashlib.md5(unique_id.encode('utf-8')).hexdigest()  # 确保unique_id为字符串
            data_list.append(info)
        return result['list']

    def api_sales_list(self, param_payload: dict = None, only_total=False):
        '''
        获取销量统计数据
        :param param_payload: 请求参数
        :param only_total: 是否只返回总数
        :return: 销量列表或总数
        '''
        self.page.set.header("auth-token", self.get_token())

        # 计算日期范围：从15天前到今天
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=14)).strftime('%Y-%m-%d')

        json_data = {
            'asin_type': 'msku',
            'bid': '',
            'cid': '',
            'currency_code': '',
            'date_type': '1',
            'delivery_methods': [],
            'developers': [],
            'end': end_date,
            'field_key': 't-sales-stat-fix-msku',
            'gtag_ids': '',
            'length': 500,
            'mids': '',
            'offset': 0,
            'order_type': 0,
            'req_time_sequence': '/api/report/asinDailyLists$$6',
            'search_field': 'asin',
            'search_value': [],
            'seller_relation_uids': '',
            'sids': '',
            'sort_field': 'total',
            'sort_type': 'desc',
            'start': start_date,
            'turn_on_summary': '0',
            'type': 'volume'
        }

        if param_payload:
            json_data.update(param_payload)

        url = "https://muke.lingxing.com/api/report/asinDailyLists"
        self.page.post(url, json=json_data)
        result = self.check_page_json()

        if only_total:
            return result.get('count', 0)  # 使用 count 字段而不是 total

        return result.get('list', [])

    def api_fee_diff_list(self, param_payload: dict = None):
        """FBA费差异列表接口"""
        self.page.set.header("auth-token", self.get_token())
        url = "https://muke.lingxing.com/api/tool/fba_different_fee/list"
        
        # 默认参数
        json_data = {
            "mids": [],
            "sids": [],
            "length": 200,
            "offset": 0,
            "req_time_sequence": "/api/tool/fba_different_fee/list$$1"
        }
        
        if param_payload:
            json_data.update(param_payload)
            
        self.page.post(url, json=json_data)
        result = self.check_page_json()
        
        # 处理返回数据
        if 'data' not in result:
            self.logging(f"API返回缺少data字段: {result}")
            return []
            
        data = result.get('data', [])
        if isinstance(data, dict):
            data = data.get('list', [])
            
        return data

    def api_shipment_claim_summary(self, param_payload: dict = None):
        """货件签收差异店铺汇总接口"""
        self.page.set.header("auth-token", self.get_token())
        url = "https://muke.lingxing.com/api/tool/shipment_claim/getSummaryBody"

        # 默认参数
        json_data = {
            'sid_list': [],
            'mid_list': [],
            'offset': 0,
            'length': 200,
            'req_time_sequence': '/api/tool/shipment_claim/getSummaryBody$$5'
        }

        if param_payload:
            json_data.update(param_payload)

        self.page.post(url, json=json_data)
        result = self.check_page_json()

        # 处理返回数据
        if 'data' not in result:
            self.logging(f"API返回缺少data字段: {result}")
            return []

        data = result.get('data', {})
        if isinstance(data, dict):
            return data.get('list', [])

        return []

    def api_shipment_claim_list(self, param_payload: dict = None):
        """货件签收差异详情列表接口"""
        self.page.set.header("auth-token", self.get_token())
        url = "https://muke.lingxing.com/api/tool/shipment_claim/list"

        # 默认参数
        json_data = {
            'sid': None,
            'sort_field': '',
            'sort_type': '',
            'offset': 0,
            'length': 200,
            'status': 0,
            'search_value': '',
            'search_field': 'msku',
            'req_time_sequence': '/api/tool/shipment_claim/list$$12'
        }

        if param_payload:
            json_data.update(param_payload)

        self.page.post(url, json=json_data)
        result = self.check_page_json()

        # 处理返回数据
        if 'data' not in result:
            self.logging(f"API返回缺少data字段: {result}")
            return []

        data = result.get('data', {})
        if isinstance(data, dict):
            return data.get('list', [])

        return []

    def fetch_order_data(self, params: dict = None):
        '''
        抓取订单数据
        :param params: 任务参数
        :return: 抓取结果
        '''
        self.cur_info = init_fetch_info()
        data_list = []

        try:
            # 获取总数据量
            total = self.api_order_list(only_total=True)
            if not total:
                self.cur_info['result'] = '无数据'
                return data_list

            # 计算总页数
            page_size = 200
            total_pages = ceil(total / page_size)
            self.cur_info['total'] = total
            self.cur_info['page_count'] = total_pages

            # 分页获取数据
            for page in range(1, total_pages + 1):
                self.cur_info['cur_page'] = page

                param_payload = {
                    "pageNo": page,
                    "pageSize": page_size
                }
                orders = self.api_order_list(param_payload)
                if not orders:
                    continue

                # 处理数据
                for order in orders:
                    order['unique_id'] = order['amazonOrderId']  # 使用订单ID作为唯一标识
                    data_list.append(order)

                self.logging(f'第{page}/{total_pages}页,获取{len(orders)}条数据')

            # 更新任务信息
            self.cur_info.update({
                'task_num': len(data_list),
                'platform_num': total,
                'done_time': now_int()
            })

        except Exception as e:
            self.cur_info['status'] = 20
            self.cur_info['result'] = f'抓取异常:{str(e)}'
            traceback.print_exc()

        return data_list

    def fetch_fba_inventory_data(self, params: dict = None):
        '''
        抓取FBA库存数据
        :param params: 任务参数
        :return: 抓取结果
        '''
        self.cur_info = init_fetch_info()
        data_list = []

        try:
            # 获取总数据量
            total = self.api_fba_inventory_list(only_total=True)
            if not total:
                self.cur_info['result'] = '无数据'
                return data_list

            # 计算总页数
            page_size = 200
            total_pages = ceil(total / page_size)
            self.cur_info['total'] = total
            self.cur_info['page_count'] = total_pages

            # 分页获取数据
            for page in range(1, total_pages + 1):
                self.cur_info['cur_page'] = page

                param_payload = {
                    "offset": (page - 1) * page_size,
                    "length": page_size
                }
                inventories = self.api_fba_inventory_list(param_payload)
                if not inventories:
                    continue

                # 处理数据
                for inventory in inventories:
                    inventory['unique_id'] = str(inventory['unique_id'])  # 确保unique_id为字符串
                    data_list.append(inventory)

                self.logging(f'第{page}/{total_pages}页,获取{len(inventories)}条数据')

            # 更新任务信息    
            self.cur_info.update({
                'task_num': len(data_list),
                'platform_num': total,
                'done_time': now_int()
            })

        except Exception as e:
            self.cur_info['status'] = 20
            self.cur_info['result'] = f'抓取异常:{str(e)}'
            traceback.print_exc()

        return data_list


def update_task_status(report_id, unique_id):
    sql = f"UPDATE rpa.`data_lingxing_report_generate` SET `report_id`=%s, `fetch_status`=10, `datetime`=UNIX_TIMESTAMP(CURRENT_DATE) WHERE `unique_id`=%s"
    MS100.insert(sql, (report_id, unique_id,))


def main_run_task_report_generate(user):
    # LingXingSessionPage().group_report_tasks(users, self.calculate_and_export_inventory_warehouse_groupings(),'fba库存明细')  # 任务分组-fba库存明细
    lx = LingXingSessionPage(user=user)
    lx.cancel_other_report()
    date_today = datetime.combine(datetime.today().date(), dtime.min)  # 当天0点的datetime

    rs = MS100.get_dict(f'select unique_id,request_param from rpa.`data_lingxing_report_generate` where fetch_status=1 and username="{user}"')
    print(rs)
    for task in rs:
        unique_id = task['unique_id']
        request_param = json.loads(task['request_param'])
        if unique_id == 'sc订单导出':
            lx.generate_order_file()
            target_item = lx.get_today_report_data(unique_id, date_today)
            report_id = target_item.get('report_id')
            lx.logging(f'report_id:{report_id}')
            update_task_status(report_id, unique_id)
            print(' *' * 30)
        # elif 'fba库存明细' in unique_id:
        #     report_id = lx.generate_inventory_file(request_param)
        #     update_task_status(report_id, unique_id)
    lx.check_report_status()


def gen_erp_token(data):
    # CRYPT_KEY 是一个字符串
    CRYPT_KEY = "Oepd1OBMamXolAQXSoAetFAhwaHxXN982D"

    # 排序字典的键
    sorted_params = {k: v for k, v in sorted(data.items(), key=lambda item: item[0])}

    # 构建查询字符串
    params_str = urlencode(sorted_params)

    # 添加 CRYPT_KEY 并进行 Base64 编码
    encoded_params = base64.b64encode((params_str + CRYPT_KEY).encode('utf-8')).decode('utf-8')

    # 替换字符并去除等号
    replaced_encoded_params = encoded_params.replace('+', '-').replace('/', '_').rstrip('=')

    # 计算 MD5 哈希
    return hashlib.md5(replaced_encoded_params.encode('utf-8')).hexdigest()


def req_local_erp(url, json_data=None, param_data=None):
    url_with_query = url
    headers = {"Content-Type": "application/json"}
    data = None

    parsed_url = urlparse(url_with_query)
    all_param = parse_qs(parsed_url.query)
    c = all_param.get('c', [None])[0]
    a = all_param.get('a', [None])[0]

    if json_data:
        if (
                any(x.lower() == c.lower() for x in ["ims/fba"]) and
                any(x.lower() == a.lower() for x in ["savedraft", "savebox", "getcreatefbabybox"])
        ):
            json_data = {"params": json.dumps(json_data)}
        url_with_query = url_with_query.replace("/get?", "/post?")
        encrypt_param = json_data
        data = json.dumps(json_data)
    else:
        if param_data:
            other_params_str = urlencode(param_data)
            url_with_query += "&" + other_params_str
        encrypt_param = parse_qs(parsed_url.query)
        encrypt_param.pop('c', None)
        encrypt_param.pop('a', None)

    method = "POST" if "/post?" in url_with_query.lower() else "GET"
    headers["X-Token"] = gen_erp_token(encrypt_param)  # 获取 token

    response = requests.request(method, url_with_query, headers=headers, data=data)
    response_data = response.json()

    code = response_data.get('code')
    msg = response_data.get('msg')
    result_data = response_data.get('data', [])

    if code == 200000:
        return response_data
    if code != 1:
        raise Exception(f"c={c}&a={a} 接口异常：{msg}")
    return result_data


def insert_lingxing_data(data_list: list, params: dict, table_name: str):
    '''
    插入领星数据
    :param data_list: 数据列表
    :param params: 任务参数
    :param table_name: 表名
    '''
    if not data_list:
        return

    task_id = params.get('task_id', 0)
    status = params.get('status', 10)
    result = params.get('result', '成功')
    task_num = len(data_list)
    platform_num = params.get('platform_num', task_num)
    done_time = params.get('done_time', 0)

    # 转换为DataFrame
    df = pd.DataFrame(data_list)

    # JSON字段处理
    json_columns = [
        'slist', 'promotionIds', 'category_arr', 'attribute',
        'parent_asin_arr', 'asin_arr', 'seller_sku_arr', 'fnsku_arr',
        'sku_arr', 'product_name_arr', 'asin_principal_arr',
        'parent_mearg_field', 'asin_principal_list'
    ]

    for col in json_columns:
        if col in df.columns:
            try:
                df[col] = df[col].apply(lambda x: json.dumps(x) if x else "[]")
            except:
                pass

    # 添加任务相关字段
    df['app_id'] = params.get('app_id', 0)
    df['task_id'] = task_id
    df['datetime'] = params.get('datetime', get_today_zero_timestamp())
    df['task_time'] = params.get('task_time', now_int())
    df['data_status'] = params.get('data_status', 0)
    df['user_id'] = params.get('user_id', 0)
    df['username'] = params.get('username', '')
    df['create_time'] = params.get('create_time', now_int())
    # 删除ID 使用自增ID
    df.drop(columns=['id'], inplace=True)

    # 执行插入
    insert_data_from_pd(table_name, df, sql_conn=MS)

    # 更新任务状态
    # task_table = 'rpa.task_lingxing_order' if 'order' in table_name else 'rpa.task_lingxing_fba'
    # sql = f"# UPDATE {task_table} SET status={status},result='{result}',update_time={now_int()} WHERE id={task_id}"
    # MS100.update(sql)
    # print(sql)


def main_fetch_lingxing_data(data_type: str, users: list, param_payload: dict = None):
    '''
    主抓取入口,实现多账号动态数据分片抓取
    '''
    if not users:
        return

    # 使用Manager创建进程间共享的数据结构
    manager = Manager()
    shared_data = manager.dict({
        'success_pages': manager.list(),
        'failed_pages': manager.dict(),
        'data': manager.list(),
        'user_stats': manager.dict()
    })

    # 创建进程安全的队列
    task_queue = manager.Queue()

    # 使用第一个账号获取总数据量
    lx = LingXingSessionPage(user=users[0])
    try:
        if data_type == 'order':
            total = lx.api_order_list(param_payload=param_payload, only_total=True)
        else:
            total = lx.api_fba_inventory_list(param_payload=param_payload, only_total=True)
    except Exception as e:
        print(f'获取总数据量失败: {e}')
        return

    if not total:
        print('无数据需要抓取')
        return

    # 计算总页数
    page_size = 200
    total_pages = ceil(total / page_size)
    # total_pages = 5  # 先测试五页

    # 打印本次采集总览
    print(f'本次采集共{total}条数据, 共{total_pages}页, 本次采集账号数量为{len(users)}个')

    # 初始化任务队列
    for page in range(1, total_pages + 1):
        task_queue.put(page)

    # 创建进程池
    with ProcessPoolExecutor(max_workers=len(users)) as executor:
        futures = []

        for user in users:
            future = executor.submit(
                    run_fetch_task_v2,
                    data_type=data_type,
                    user=user,
                    param=param_payload,
                    task_queue=task_queue,
                    shared_data=shared_data,
                    page_size=page_size,
                    max_retries=5
            )
            futures.append(future)

        # 等待所有任务完成
        for future in as_completed(futures):
            try:
                result = future.result()
                print(f'任务已完成，结果为：{result}')
            except Exception as e:
                print(f'任务失败，出现错误: {e}')

    # 处理结果
    data_list = list(shared_data['data'])
    failed_pages = dict(shared_data['failed_pages'])
    user_stats = dict(shared_data['user_stats'])

    if failed_pages:
        print(f'存在失败页面: {failed_pages}')

    print(f'总计抓取数据: {len(data_list)} 条')
    print('各账号抓取统计:')
    for user, stats in user_stats.items():
        print(f'{user}: 成功{stats["success"]}页, 失败{stats["failed"]}页')

    return {
        'data': data_list,
        'failed_pages': failed_pages,
        'user_stats': user_stats
    }


def run_fetch_task_v2(data_type: str, user: str, param: dict, task_queue,
                      shared_data: dict, page_size: int, max_retries: int = 3):
    '''
    运行抓取任务(改进版)
    '''
    param = param or {}
    lx = LingXingSessionPage(user=user)
    user_stats = {'success': 0, 'failed': 0}

    while True:
        try:
            # 获取下一个待抓取页面
            page = task_queue.get_nowait()
        except Empty:
            break

        # 检查是否已成功抓取
        if page in shared_data['success_pages']:
            continue

        # 检查重试次数
        retry_count = shared_data['failed_pages'].get(page, 0)
        if retry_count >= max_retries:
            continue

        # 尝试抓取数据
        try:
            if data_type == 'order':
                param_payload = param
                param_payload.update({
                    "pageNo": page,
                    "pageSize": page_size
                })
                items = lx.api_order_list(param_payload)
            else:
                param_payload = param
                param_payload.update({
                    "offset": (page - 1) * page_size,
                    "length": page_size
                })
                items = lx.api_fba_inventory_list(param_payload)

            if items:
                # 处理数据
                for item in items:
                    if data_type == 'order':
                        item['unique_id'] = item['amazonOrderId']
                    else:
                        item['unique_id'] = str(item['unique_id'])
                    shared_data['data'].append(item)

                # 标记成功
                shared_data['success_pages'].append(page)
                shared_data['failed_pages'].pop(page, None)
                user_stats['success'] += 1
                lx.logging(f'第{page}页抓取成功,获取{len(items)}条数据')

            else:
                raise Exception('返回数据为空')

        except Exception as e:
            # 记录失败信息
            retry_count = shared_data['failed_pages'].get(page, 0) + 1
            shared_data['failed_pages'][page] = retry_count

            lx.logging(f'第{page}页抓取失败(第{retry_count}次): {str(e)}')
            if retry_count >= max_retries:
                user_stats['failed'] += 1
                lx.logging(f'第{page}页抓取失败，已超过最大重试次数，跳过')

            # 重新放回队列等待重试
            if retry_count < max_retries:
                task_queue.put(page)

    # 更新用户统计
    shared_data['user_stats'][user] = user_stats

    return {
        'user': user,
        'success': user_stats['success'],
        'failed': user_stats['failed']
    }


def main_fetch_lingxing_data_v2(data_params):
    users = ['jszg01', 'yxyJS2']
    params = {
        'task_id': 1,
        'app_id': 1,
        'datetime': get_today_zero_timestamp(),
        'task_time': now_int()
    }
    data_type = 'order'
    data_type = 'fba_inventory'

    # 抓取订单数据
    results = main_fetch_lingxing_data(data_type, users, params)

    # 处理抓取结果
    if results['data']:
        # 插入数据库
        table_name = 'rpa.data_lingxing_order' if 'order' in data_type else 'idc.lingxing_fba_inventory'
        # task_table = 'rpa.task_lingxing_order' if 'order' in table_name else 'rpa.task_lingxing_fba'
        params.update({
            'task_num': len(results['data']),
            'platform_num': len(results['data']),
            'done_time': now_int()
        })
        insert_lingxing_data(results['data'], params, table_name)

    # 处理失败页面
    failed_pages = results['failed_pages']
    if failed_pages:
        print('失败页面统计:')
        for page, retry_count in failed_pages.items():
            print(f'页码:{page}, 重试次数:{retry_count}')


def init_fetch_info():
    """
    初始化抓取信息
    :return: dict
    """
    return {
        'result': '',  # 执行结果描述
        'status': 10,  # 状态码 10正常 20异常
        'page_count': 0,  # 总页数
        'end_page_num': 0,  # 结束页码
        'total': 0,  # 总数据量
        'cur_page': 1,  # 当前页码
        'real_page': 1,  # 实际页码
        'task_num': 0,  # 任务数量
        'platform_num': 0,  # 平台数量
        'done_time': 0,  # 完成时间
        'error_pages': set(),  # 错误页面
        'success_pages': set(),  # 成功页面
        'retry_pages': {},  # 重试页面
        'cur_result': ''  # 当前执行结果
    }
    
 
def init_lingxing_fba_inventory():
    '''
    初始化领星库存数据
    '''

    # 先将当前的total值复制到history_total字段
    copy_sql = 'UPDATE idc.lingxing_fba_inventory SET `history_total` = `total`'
    MS.update(copy_sql)
    print('已将total字段值复制到history_total字段')

    # 初始化领星库存数据
    sql = 'update idc.lingxing_fba_inventory set `total` = 0,`available_total` = 0,`afn_fulfillable_quantity` = 0,`quantity` = 0,`real_transit_quantity` = 0,`reserved_fc_transfers` = 0,`reserved_fc_processing` = 0,`reserved_customerorders` = 0,`afn_inbound_working_quantity` = 0,`afn_inbound_shipped_quantity` = 0,`stock_up_num` = 0,`afn_inbound_receiving_quantity` = 0,`afn_unsellable_quantity` = 0,`afn_researching_quantity` = 0,`total_fulfillable_quantity` = 0,`inv_age_0_to_90_days` = 0,`inv_age_271_to_365_days` = 0,`inv_age_0_to_30_days` = 0,`inv_age_31_to_60_days` = 0,`inv_age_61_to_90_days` = 0,`inv_age_91_to_180_days` = 0,`inv_age_181_to_270_days` = 0,`inv_age_271_to_330_days` = 0,`inv_age_331_to_365_days` = 0,`inv_age_365_plus_days` = 0,`historical_days_of_supply` = 0,`long_term_historical_days_of_supply` = 0,`short_term_historical_days_of_supply` = 0,`recommended_action` = "",`sell_through` = 0,`estimated_excess_quantity` = 0,`estimated_storage_cost_next_month` = 0,`fba_minimum_inventory_level` = 0,`fba_inventory_level_health_status` = "",`low_inventory_level_fee_applied` = "",`total_unfulfillable_quantity` = 0,`customer_damaged_quantity` = 0,`warehouse_damaged_quantity` = 0,`distributor_damaged_quantity` = 0,`carrier_damaged_quantity` = 0,`defective_quantity` = 0,`expired_quantity` = 0,`total_price` = 0,`total_amount` = 0,`total_cost` = 0,`available_total_price` = 0,`afn_fulfillable_quantity_price` = 0,`quantity_price` = 0,`reserved_fc_transfers_price` = 0,`reserved_fc_processing_price` = 0,`reserved_customerorders_price` = 0,`afn_inbound_working_quantity_price` = 0,`afn_inbound_shipped_quantity_price` = 0,`stock_up_num_price` = 0,`afn_inbound_receiving_quantity_price` = 0,`afn_unsellable_quantity_price` = 0,`afn_researching_quantity_price` = 0,`inv_age_0_to_90_price` = 0,`inv_age_271_to_365_price` = 0,`inv_age_0_to_30_price` = 0,`inv_age_31_to_60_price` = 0,`inv_age_61_to_90_price` = 0,`inv_age_91_to_180_price` = 0,`inv_age_181_to_270_price` = 0,`inv_age_271_to_330_price` = 0,`inv_age_331_to_365_price` = 0,`inv_age_365_plus_price` = 0,`historical_days_of_supply_price` = 0'
    MS.update(sql)
    print('初始化数据成功')


if __name__ == '__main__':
    users = ['yxy023', 'yxy025']
    # LingXingSessionPage().group_report_tasks(users, 6, 'fba库存明细')  # 任务分组-fba库存明细
    # main_run_task_report_generate(users[0])
    main_fetch_lingxing_data_v2({
        'user': users,
        'is_hide_zero_stock': 1
    })
    # lx = LingXingSessionPage()
    # print(lx.api_storage_fbaLists())
