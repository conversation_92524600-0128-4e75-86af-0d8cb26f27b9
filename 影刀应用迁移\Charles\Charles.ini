working.directory=.
classpath.1=lib/charles.jar
main.class=com.xk72.charles.gui.MainWithClassLoader
vm.version.min=11
vm.location=jdk\bin\server\jvm.dll
vmarg.1=-Dsun.java2d.d3d=false
vmarg.2=-Djava.net.preferIPv4Stack=false
vmarg.3=-Djava.net.preferIPv6Addresses=true
vmarg.4=-Djava.library.path=lib
dde.enabled=true
dde.class=com.xk72.charles.win32.Win32DDEManager
dde.server.name=Charles
dde.topic=System
single.instance=dde
log.level=warning

[ErrorMessages]
java.not.found=The bundled Java installation was not found. Please uninstall and reinstall Charles.
java.failed=The bundled Java installation is broken. Please uninstall and reinstall Charles.
