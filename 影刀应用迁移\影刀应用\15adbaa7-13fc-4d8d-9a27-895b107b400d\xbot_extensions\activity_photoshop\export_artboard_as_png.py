# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import re
import os
import xbot
import json
import win32com.client

from xbot import print, sleep
from .import package
from .package import variables as glv
from .js_code import ARTBOARD_EXPORT


invalid_chars_pattern = r'[<>:"/\\|?*？：／＼“”]'

def remove_invalid_chars(text):
    return re.sub(invalid_chars_pattern, '', text)


def export_artboard_to_png(ps_instance, save_dir, file_type) -> list:
    files_path = []
    tmp_dir = save_dir
    save_dir = json.dumps(save_dir).strip('"')
    photoshop_app = ps_instance.doc.Application
    js_code = ARTBOARD_EXPORT.format(save_dir=save_dir, file_type=file_type)
    photoshop_app.DoJavaScript(js_code)

    for layerset in ps_instance.doc.layersets:
        save_name = remove_invalid_chars(layerset.Name) + f".{file_type}"
        save_name.replace(" ", "_")
        files_path.append(os.path.join(tmp_dir, save_name))

    return files_path


def main(args):
    ps_instance = args.get("ps_instance")
    save_dir = args.get("save_dir") 
    file_type = args.get("file_type")

    files_path = export_artboard_to_png(ps_instance, save_dir, file_type)

    args["files_path"] = files_path

    pass
