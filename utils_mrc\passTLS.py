import random
import certifi
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.ssl_ import create_urllib3_context
import ssl
from DrissionPage import SessionPage

ORIGIN_CIPHERS = (
    "ECDH+AESGCM:DH+AESGCM:ECDH+AES256:DH+AES256:ECDH+AES128:DH+AES:ECDH+HIGH:"
    "DH+HIGH:ECDH+3DES:DH+3DES:RSA+AESGCM:RSA+AES:RSA+HIGH:RSA+3DES"
)
# ORIGIN_CIPHERS = (
#     "ECDHE+AESGCM:"
#     "ECDHE+CHACHA20:"
#     "DHE+AESGCM:"
#     "DHE+CHACHA20:"
#     "ECDH+AESGCM:"
#     "DH+AESGCM:"
#     "ECDH+AES:"
#     "DH+AES:"
#     "RSA+AESGCM:"
#     "RSA+AES:"
#     "RSA+HIGH:"
#     "RSA+3DES:"
#     "ECDHE+AES256:"
#     "ECDHE+AES128:"
#     "ECDH+AES256:"
#     "ECDH+AES128:"
#     "ECDHE-RSA-AES256-GCM-SHA384:"
#     "ECDHE-RSA-AES128-GCM-SHA256:"
#     "ECDHE-ECDSA-AES256-GCM-SHA384:"
#     "ECDHE-ECDSA-AES128-GCM-SHA256:"
#     "ECDHE-RSA-AES256-SHA384:"
#     "ECDHE-RSA-AES128-SHA256:"
#     "ECDHE-ECDSA-AES256-SHA384:"
#     "ECDHE-ECDSA-AES128-SHA256:"
#     "ECDHE-RSA-AES256-SHA:"
#     "ECDHE-RSA-AES128-SHA:"
#     "ECDHE-ECDSA-AES256-SHA:"
#     "ECDHE-ECDSA-AES128-SHA:"
#     "AES256-GCM-SHA384:"
#     "AES128-GCM-SHA256:"
#     "AES256-SHA256:"
#     "AES128-SHA256:"
#     "AES256-SHA:"
#     "AES128-SHA:"
#     "DES-CBC3-SHA:"
#     "TLS_AES_128_GCM_SHA256:"
#     "TLS_AES_256_GCM_SHA384:"
#     "TLS_CHACHA20_POLY1305_SHA256"
# )


class CustomTLSAdapter(HTTPAdapter):
    def __init__(self, *args, **kwargs):
        ciphers = ORIGIN_CIPHERS.split(":")
        random.shuffle(ciphers)
        self.ciphers = ":".join(ciphers) + ":!aNULL:!eNULL:!MD5"
        super().__init__(*args, **kwargs)

    def init_poolmanager(self, *args, **kwargs):
        context = create_urllib3_context(ciphers=self.ciphers)
        context.load_verify_locations(certifi.where())  # 加载 certifi 提供的证书
        context.check_hostname = True  # 启用主机名检查
        context.verify_mode = ssl.CERT_REQUIRED  # 启用证书验证
        kwargs["ssl_context"] = context
        return super().init_poolmanager(*args, **kwargs)


# 使用自定义适配器
tls_session = requests.Session()
tls_session.mount("https://", CustomTLSAdapter())
tls_session.mount("http://", CustomTLSAdapter())
