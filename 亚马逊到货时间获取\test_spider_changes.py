#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试爬虫修改是否正确
"""

import pymysql
import time

# 数据库配置
mysql_config = {
    'host': '*************',
    'port': 3306,
    'user': 'root',
    'password': '123456',
    'database': 'rpa',
    'charset': 'utf8mb4'
}

def test_task_listing_query():
    """测试从task_listing表查询数据"""
    try:
        connection = pymysql.connect(**mysql_config)
        with connection.cursor() as cursor:
            # 查询task_listing表中app_id=13且status=1的数据
            sql = "SELECT id, asins, site FROM task_listing WHERE app_id = 13 AND status = 1"
            cursor.execute(sql)
            results = cursor.fetchall()
            
            print(f"从task_listing表获取到 {len(results)} 条待处理任务")
            
            for task_id, asins, site in results:
                print(f"任务ID: {task_id}, ASIN: {asins}, 站点: {site}")
                
                # 测试URL构建
                amazon_sites = {
                    "US": {"url": "amazon.com", "country": "美国", "postcode": "10001"},
                    "CA": {"url": "amazon.ca", "country": "加拿大", "postcode": "M5V 3L9"},
                    "UK": {"url": "amazon.co.uk", "country": "英国", "postcode": "EC1A 1BB"},
                    "DE": {"url": "amazon.de", "country": "德国", "postcode": "10117"},
                    "FR": {"url": "amazon.fr", "country": "法国", "postcode": "75001"},
                    "IT": {"url": "amazon.it", "country": "意大利", "postcode": "00144"},
                    "ES": {"url": "amazon.es", "country": "西班牙", "postcode": "28013"},
                    "JP": {"url": "amazon.co.jp", "country": "日本", "postcode": "100-0005"},
                    "AU": {"url": "amazon.com.au", "country": "澳大利亚", "postcode": "2000"},
                    "IN": {"url": "amazon.in", "country": "印度", "postcode": "400001"},
                    "MX": {"url": "amazon.com.mx", "country": "墨西哥", "postcode": "06140"},
                    "BR": {"url": "amazon.com.br", "country": "巴西", "postcode": "20031-000"}
                }
                
                site_info = amazon_sites.get(site, amazon_sites["US"])
                url = f"https://www.{site_info['url']}/dp/{asins}"
                print(f"  构建的URL: {url}")
                
                # 测试unique_id构建
                unique_id = f"{asins}_{site.lower()}"
                print(f"  构建的unique_id: {unique_id}")
                
        connection.close()
        
    except Exception as e:
        print(f"测试失败: {e}")

def test_data_insert():
    """测试数据插入逻辑"""
    try:
        connection = pymysql.connect(**mysql_config)
        with connection.cursor() as cursor:
            # 测试数据
            test_data = {
                'asin': 'B0FCBBQ92C',
                'site': 'DE',
                'primary': '测试主要配送信息',
                'secondary': '测试次要配送信息',
                'status': 'success'
            }
            
            asin = test_data.get('asin', '')
            site = test_data.get('site', '')
            primary = test_data.get('primary', '')
            secondary = test_data.get('secondary', '')
            
            # 构建unique_id = asin_site
            unique_id = f"{asin}_{site.lower()}"
            print(f"测试unique_id: {unique_id}")
            
            # 插入数据到 data_amazon_asin_attr 表
            insert_sql = """
            INSERT INTO data_amazon_asin_attr 
            (unique_id, app_id, site, asin, primary_delivery_message, primary_delivery_message_cleaned, 
             secondary_delivery_message, secondary_delivery_message_cleaned, create_time, update_time)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
            ON DUPLICATE KEY UPDATE
            primary_delivery_message = VALUES(primary_delivery_message),
            primary_delivery_message_cleaned = VALUES(primary_delivery_message_cleaned),
            secondary_delivery_message = VALUES(secondary_delivery_message),
            secondary_delivery_message_cleaned = VALUES(secondary_delivery_message_cleaned),
            update_time = NOW()
            """
            
            current_time = int(time.time())
            cursor.execute(insert_sql, (
                unique_id, 13, site, asin, primary, primary, secondary, secondary, current_time
            ))
            connection.commit()
            print("测试数据插入成功")
            
        connection.close()
        
    except Exception as e:
        print(f"测试数据插入失败: {e}")

if __name__ == "__main__":
    print("=== 测试task_listing查询 ===")
    test_task_listing_query()
    
    print("\n=== 测试数据插入 ===")
    test_data_insert() 