import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        photoshop_instance = ""
        name_of_layer = ""
        path_to_image_to_replace = ""
        keep_original_size_of_image_file = True
    else:
        photoshop_instance = args.get("photoshop_instance", "")
        name_of_layer = args.get("name_of_layer", "")
        path_to_image_to_replace = args.get("path_to_image_to_replace", "")
        keep_original_size_of_image_file = args.get("keep_original_size_of_image_file", True)
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="change_content_of_layer_by_name_from_file", params={
            "photoshop_instance": photoshop_instance,
            "name_of_layer": name_of_layer,
            "path_to_image_to_replace": path_to_image_to_replace,
            "keep_original_size_of_image_file": keep_original_size_of_image_file,
        }, _block=("替换图层图片", 1, "调用模块"))
    finally:
        pass
