import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        photoshop_instance = ""
        save_path = ""
        quality = ""
    else:
        photoshop_instance = args.get("photoshop_instance", "")
        save_path = args.get("save_path", "")
        quality = args.get("quality", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="export_as_image", params={
            "photoshop_instance": photoshop_instance,
            "save_path": save_path,
            "quality": quality,
        }, _block=("导出为图片", 1, "调用模块"))
    finally:
        pass
