# -*- coding:UTF-8 -*-
# Redis数据库地址
# REDIS_HOST = '**************'
REDIS_HOST = '*************'
# Redis端口
REDIS_PORT = 6379
# Redis密码，如无填None
REDIS_PASSWORD = None

# 产生器使用的浏览器
BROWSER_TYPE = 'S'
# BROWSER_TYPE = 'Chrome'

# 产生器类，如扩展其他站点，请在此配置
GENERATOR_MAP = {
    'sprite': 'SpriteCookiesGenerator',
    'spriteEp': 'SpriteExpansionCookiesGenerator',
    'lingxing': 'LingXingCookiesGenerator',
    # 'lingxingAD': 'LingXingADCookiesGenerator',
    'amazonUS': 'AmazonUSCookiesGenerator',
    'amazon': 'AmazonCookiesGenerator',
}
# 测试类，如扩展其他站点，请在此配置
TESTER_MAP = {
    'sprite': 'SpriteValidTester',
    'spriteEp': 'SpriteExpansionValidTester',
    'lingxing': 'LingXingValidTester',
    # 'lingxingAD': 'LingXingADValidTester',
    # 'amazon': 'AmazonValidTester',
}
TEST_URL_MAP = {
    'spriteEp': 'https://www.sellersprite.com/v2/extension/competitor-lookup/quick-view/us?asins=B0CKTSYFJH&source=offline&miniMode=false&withRelation=true&withSaleTrend=false&tk=919265.531000&version=4.6.1&language=zh_CN&extension=ecanjpklimgeijdcdpdfoooofephbbln',
    'lingxing': 'https://gw.lingxingerp.com/newadmin/api/user/manage/myInfo?req_time_sequence=%2Fnewadmin%2Fapi%2Fuser%2Fmanage%2FmyInfo$$1',
    'lingxingAD': 'https://ads.lingxing.com/ad_report/keyword_grab/index/get_expired_quotas_info',
    'amazon': 'https://www.amazon.de',
}

# 产生器和验证器循环周期
CYCLE = 120
# API地址和端口
API_HOST = '0.0.0.0'
API_PORT = 5000
# 产生器开关，模拟登录添加Cookies
GENERATOR_PROCESS = True
# 验证器开关，循环检测数据库中Cookies是否可用，不可用删除
VALID_PROCESS = True
# API接口服务
API_PROCESS = True
