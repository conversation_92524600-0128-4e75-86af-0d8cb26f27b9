# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
import inspect
from xbot import print, sleep
from . import package
from .package import variables as glv
from .resources.photoshop_api import Photoshop
from win32com.client import Dispatch, GetActiveObject
from .ps_back import get_artlayer_text
from .ps_back import set_artlayer_font
from .ps_back import get_layer_parent_name
from .ps_back import get_names_of_all_layers 
from .ps_back import open as ps_open
from .ps_back import export_as_image
from .ps_back import rename_layer_name
from .ps_back import fill_content_aware
from .ps_back import set_rectangle_tool_fill_color
from .ps_back import set_rectangle_tool_stroke_color
from .ps_back import get_artlayer_font_info
from .ps_back import change_content_of_layer_by_name_from_file


class AcPs(Photoshop):
    def __init__(self, ):
        self.app = self.get_active_instance()
        self.doc = self.app.activeDocument

    def get_active_instance(self):
        return GetActiveObject("Photoshop.Application")


def test_get_artlayer_text():
    ps_instance = AcPs()
    res = get_artlayer_text(ps_instance, "1.2", False)
    print(res)


def test_set_artlayer_font():
    ps_instance = AcPs()
    set_artlayer_font(ps_instance, "1.2", "20", "3270NerdFontComplete-Regular", False)


def test_get_layer_parent_name():
    ps_instance = AcPs()
    res = get_layer_parent_name(ps_instance, "1.2")
    print(res)
    res = get_layer_parent_name(ps_instance, "夏季团购")
    print(res)


def test_get_names_of_all_layers():
    ps_instance = AcPs()
    res = get_names_of_all_layers(ps_instance, False)
    print(res)
    res = get_names_of_all_layers(ps_instance, True)
    print(res)



def test_export_as_image():
    # path = r"C:\Users\<USER>\OneDrive\文档\ps\2031161_仲景_逍遥丸_360丸测试.psd"
    # save_path = r"2031161_仲景_逍遥丸_360丸测试.jpg"
    # path = r"C:\Users\<USER>\OneDrive\文档\ps\psd34905.psd"
    # save_path = r"C:\Users\<USER>\OneDrive\文档\ps\psd34905-%s.jpg"
    # ps_instance = ps_open(path)
    # for i in range(1, 13):
    # export_as_image(ps_instance, save_path, 8)
    # options = Dispatch("Photoshop.JPEGSaveOptions")
    # options.Quality = 2
    # options.EmbedColorProfile = False
    # options.FormatOptions = 1
    # options.Matte = 1
    # options.Scans = 3
    # ps_instance.doc.SaveAs(save_path, options, False)

    pass


def test_rename_layer_name():
    ps_instance = AcPs()
    rename_layer_name(ps_instance, "聚水潭吊牌", "聚水潭吊牌测试")


def test_fill_content_aware():
    ps_instance = AcPs()
    fill_content_aware(ps_instance, "背景", [[200, 210],[280,210],[280,250],[200,250]])


def test_rgb_2_cmyk():
    """
    2 (psConvertToRGB)
    3 (psConvertToCMYK)
    """
    ps_instance = AcPs()
    ps_instance.doc.ChangeMode(2)
    # ps_instance.app.BackgroundColor.RGB.HexValue = "FF0000"
    rgb = Dispatch("Photoshop.SolidColor")
    # 设置颜色值为 RGB FF0E00
    rgb.RGB.HexValue = "000000"
    ps_instance.app.BackgroundColor = rgb
    print(ps_instance.app.BackgroundColor.CMYK.Black)


def test_set_rectangle_tool_fill_color():
    ps_instance = AcPs()
    ps_instance.doc.ChangeMode(2)
    set_rectangle_tool_fill_color(ps_instance,"镜头框 ", (60,60,60))


def test_set_rectangle_tool_stroke_color():
    ps_instance = AcPs()
    ps_instance.doc.ChangeMode(2)
    set_rectangle_tool_stroke_color(ps_instance, "镜头小圆框3", (60,60,60))


def test_get_artlayer_font_info():
    ps_instance = AcPs()
    res = get_artlayer_font_info(ps_instance, "1.2", True)
    print(res)


def test_change_content_of_layer_by_name_from_file():
    path = r"C:\Users\<USER>\Desktop\勿动！影刀RPA所有文件 2024.11.19\丽得姿美蒂优氨基酸保湿面膜(艾斯)10P.psd"
    replace_path = r"C:\Users\<USER>\Desktop\勿动！影刀RPA所有文件 2024.11.19\已下载图片\壹年5季0脂肪蒸煮西梅168g.png"
    ps_instance = ps_open(path)

    change_content_of_layer_by_name_from_file(ps_instance, "商品主图", replace_path,)
    pass


def main(args):
    test_change_content_of_layer_by_name_from_file()
    # ps_instance = AcPs()
    # ps_instance.app
    # test_set_artlayer_font()
    # test_get_layer_parent_name()
    # test_get_names_of_all_layers()
    # test_export_as_image()
    # tmp()
    # ps_instance = AcPs()
    # set_rectangle_tool_fill_color(ps_instance, "镜头框 ", "FF00FF")
    # set_rectangle_tool_stroke_color(ps_instance, "镜头小圆框3", "FF00FF")
    # test_rename_layer_name()
    # test_fill_content_aware()
    # test_rgb_2_cmyk()
    # test_set_rectangle_tool_fill_color()
    # test_set_rectangle_tool_stroke_color()
    # test_get_artlayer_font_info()
  

    pass
