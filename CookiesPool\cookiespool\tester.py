# -*- coding:UTF-8 -*-
import json
import requests
from utils_mrc.SpiderTools import IPPool, SessionPageManager
from requests.exceptions import ConnectionError
from .login.amazon.cookies import AmazonCookies
from .login.sprite.cookies import SpriteCookies, SpriteExpansionCookies
from .db import *


class ValidTester(object):
    def __init__(self, website='default'):
        self.website = website
        self.cookies_db = RedisClient('cookies', self.website)
        self.accounts_db = RedisClient('accounts', self.website)
        self.headers = IPPool.headers
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.spm = SessionPageManager()
        self.sp = self.spm.page

    def test(self, username, cookies):
        raise NotImplementedError

    def run(self):
        cookies_groups = self.cookies_db.all()
        for username, cookies in cookies_groups.items():
            self.test(username, cookies)


class WeiboValidTester(ValidTester):
    def __init__(self, website='weibo'):
        ValidTester.__init__(self, website)

    def test(self, username, cookies):
        print('正在测试Cookies', '用户名', username)
        try:
            cookies = json.loads(cookies)
        except TypeError:
            print('Cookies不合法', username)
            self.cookies_db.delete(username)
            print('删除Cookies', username)
            return
        try:
            test_url = TEST_URL_MAP[self.website]
            response = requests.get(test_url, cookies=cookies, timeout=5, allow_redirects=False)
            if response.status_code == 200:
                print('Cookies有效', username)
            else:
                print(response.status_code, response.headers)
                print('Cookies失效', username)
                self.cookies_db.delete(username)
                print('删除Cookies', username)
        except ConnectionError as e:
            print('发生异常', e.args)


class AmazonValidTester(ValidTester):
    def __init__(self, website='amazon'):
        ValidTester.__init__(self, website)

    def test(self, username, cookies):
        print('正在测试Cookies', '用户名', username)
        if not cookies:
            print('Cookies不合法', username)
            self.cookies_db.delete(username)
            print('删除Cookies', username)
            return
        try:
            AmazonCookies(username).main()
        except ConnectionError as e:
            print('发生异常', e.args)


class LingXingValidTester(ValidTester):
    def __init__(self, website='lingxing'):
        ValidTester.__init__(self, website)

    def test(self, username, cookies):
        print('正在测试Cookies', '用户名', username)
        if not cookies:
            print('Cookies不合法', username)
            self.cookies_db.delete(username)
            print('删除Cookies', username)
            return
        try:
            test_url = TEST_URL_MAP[self.website]
            headers = {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0",
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "connection": "keep-alive",
                "X-AK-Company-Id": "901122764666708480",
                "auth-token": cookies,
                "AK-Client-Type": "web",
                "AK-Origin": "https://muke.lingxing.com",
                "Origin": "https://muke.lingxing.com",
                "Referer": "https://muke.lingxing.com/",
            }
            response = self.session.get(test_url, headers=headers, timeout=120, allow_redirects=False)
            if response.status_code == 200 and '操作成功' in response.text:
                print('Cookies有效', username)
            else:
                print(response.status_code, response.headers)
                print('Cookies失效', username)
                self.cookies_db.delete(username)
                print('删除Cookies', username)
        except ConnectionError as e:
            print('发生异常', e.args)


class LingXingADValidTester(ValidTester):
    def __init__(self, website='lingxingAD'):
        ValidTester.__init__(self, website)

    def test(self, username, cookies):
        print('正在测试Cookies', '用户名', username)
        if not cookies:
            print('Cookies不合法', username)
            self.cookies_db.delete(username)
            print('删除Cookies', username)
            return
        try:
            test_url = TEST_URL_MAP[self.website]
            headers = {
                **self.headers,
                "Content-Type": "application/json;charset=UTF-8",
                "Origin": "https://ads.lingxing.com",
                # "Referer": "https://ads.lingxing.com/home",
            }
            self.sp.set.cookies(cookies)
            self.sp.post(test_url, headers=headers, timeout=120, allow_redirects=False)
            response = self.sp.response
            if response.status_code == 200 and '操作成功' in response.text:
                print('Cookies有效', username)
            else:
                print(response.status_code, response.headers)
                print('Cookies失效', username)
                self.cookies_db.delete(username)
                print('删除Cookies', username)
        except ConnectionError as e:
            print('发生异常', e.args)


class SpriteValidTester(ValidTester):
    def __init__(self, website='sprite'):
        ValidTester.__init__(self, website)

    def test(self, username, cookies):
        print('正在测试Cookies', '用户名', username)
        if not cookies:
            print('Cookies不合法', username)
            self.cookies_db.delete(username)
            print('删除Cookies', username)
            return
        try:
            # test_url = TEST_URL_MAP[self.website]
            s = SpriteCookies('', '')
            s.set_cookies(cookies)
            if s.login_successfully():
                print('Cookies有效', username)
            else:
                print('Cookies失效', username)
                self.cookies_db.delete(username)
                print('删除Cookies', username)
        except ConnectionError as e:
            print('发生异常', e.args)


class SpriteExpansionValidTester(ValidTester):
    def __init__(self, website='spriteEp'):
        ValidTester.__init__(self, website)

    def test(self, username, cookies):
        print('正在测试Cookies', '用户名', username)
        # try:
        #     cookies = json.loads(cookies)
        # except TypeError:
        #     print('Cookies不合法', username)
        #     self.cookies_db.delete(username)
        #     print('删除Cookies', username)
        #     return
        if not cookies:
            print('Cookies不合法', username)
            self.cookies_db.delete(username)
            print('删除Cookies', username)
            return
        try:
            test_url = TEST_URL_MAP[self.website]
            headers = {
                **IPPool.headers,
                'content-type': 'application/json',
                'Host': 'www.sellersprite.com',
                'random-token': '7dbae9db-557d-44879-889d-07da258f4d69',
                'auth-token': cookies
            }
            response = self.session.get(test_url, headers=headers, timeout=120, allow_redirects=False)
            rs_data = response.json()['data'] if 'application/json' in str(response.headers.get('content-type')).lower() else ''
            if '令牌过期' in response.text or not response.text or not rs_data:
                print(response.status_code, response.headers)
                print('Cookies失效', username)
                self.cookies_db.delete(username)
                print('删除Cookies', username)
            else:
                print('Cookies有效', username)
        except ConnectionError as e:
            print('发生异常', e.args)


if __name__ == '__main__':
    SpriteExpansionValidTester().run()
