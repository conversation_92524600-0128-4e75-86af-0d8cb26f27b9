# -*- coding:UTF-8 -*-
# @FileName  :SellerSpriteSpider.py
# @Time      :2024/5/5 15:25
# <AUTHOR>
from utils_mrc.pub_fun import *
from utils_mrc.SpiderTools import SessionPageManager
import copy
import random
import tkinter as tk
from utils_mrc.FeiShuAPI import *
from utils_mrc.MysqlHelper import *
from urllib.parse import urlparse, parse_qs, parse_qsl
import amazoncaptcha
from utils_mrc.RedisClient import RedisClient

db_cookies = RedisClient('cookies', 'sprite')

 
class SellerSpriteSpider(SessionPageManager):
    def __init__(self, proxy_type=1):
        super().__init__(proxy_type)
        self.change_cookies()
        self.max_amount = 2000  # 接口单个筛选条件最大返回数量
        self.max_total = 50000  # 单次抓取最大数量
        self.cur_info = {
            'result': '完成',
            'status': 10,
            'finally_result': '',
            'finally_status': 10,
            'cur_total': 0,
            'cur_page': 1,
            'cur_fetch_datas': [],
            'unique_id': set(),
        }
        self.same_count = 0
        self.err = ''

    def check_login_status(self):
        # 检查登录状态
        url = 'https://www.sellersprite.com/'
        headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        }
        self.page.get(url, headers=headers)

        if '/user/login' in self.page.html:
            logging('登陆已失效!')
            return False
        elif user := self.page('@data-user-surname'):
            logging(f"卖家精灵登录状态生效,当前登录账号:{user.text}")
            return True

    def req_data(self, netloc, data, max_retries=3):
        if max_retries <= 0:
            return None
        try:
            self.page.set.headers({
                **self.headers,
                'content-type': 'application/json;charset=UTF-8',
                'origin': f'https://{netloc}',
                # 'referer': f'https://{netloc}',
            })
            url = f'https://{netloc}/v3/api/product-research'
            self.page.post(url=url, json=data, timeout=120)  # 添加超时设置，防止长时间无响应
            time.sleep(random.uniform(0.5, 1.2))
            self.page.response.raise_for_status()  # 检查HTTP状态码是否正常
            json_datas = self.page.json or {}
            if not data or '登陆已失效' in self.page.html:
                self.change_cookies()
                logging('登录状态失效，需要重新登录...')
                if self.check_login_status() is False:
                    return None
                return self.req_data(netloc, data, max_retries - 1)  # 重新尝试请求，会触发登录流程
            elif json_datas.get('code') != 'OK':
                self.change_cookies()
                logging(f"{self.page.json.get('message')},即将重试")
                return self.req_data(netloc, data, max_retries - 1)
            elif json_datas.get('code') == 'ERR_ROBOT_CHECK':
                logging(f'出现验证码！')
                yzm_url = 'https://www.sellersprite.com/kaptcha'
                yzm = amazoncaptcha.AmazonCaptcha.fromlink(yzm_url).solve()
                if not yzm:
                    raise Exception('卖家精灵验证码识别失败，或需更换OCR识别！')
                check_url = f'https://www.sellersprite.com/kaptcha/verify/{yzm}'
                self.page.get(check_url)
                logging(f'本次识别验证码为：{yzm}，已发送校验请求')
                self.change_cookies()
                return self.req_data(netloc, data, max_retries - 1)
            return self.page.json
        except requests.exceptions.RequestException as e:
            self.change_cookies()
            logging(f"{self.cur_info['cur_page']}页请求异常：{e}")
            return None
        except Exception as e:
            traceback.print_exc()
            self.change_cookies()
            print(f"请求捕获异常：{e}")
            return None

    def parse_json_data(self, json_data):
        datas = []
        count = 0
        if json_data:
            items = json_data.get("data", {}).get("items", []) or []
            if self.cur_info['cur_total'] == 0:
                self.cur_info['cur_total'] = json_data.get("data", {}).get("total") if json_data.get("data", {}).get("total") else 0
            for item in items:
                image = item.get("imageUrl") if item.get("imageUrl") else ''  # 图片链接
                # asin = item.get("asin")  # ASIN
                asin = re.sub(r'[^a-zA-Z0-9]', '', item.get("asin", ''))  # ASIN 可能出现意外字符'‎B0B6QXPHH7'
                site = AmazonConfig.COUNTRY_SITE_SPRITE.get(item.get("id")[:2].lower()) if item.get("id") else '未知'
                currency = AmazonConfig.CURRENCY.get(site, '未知')
                brand = item.get("brand") if item.get("brand") else ''  # 品牌
                title = item.get("title")  # 商品标题
                url = f'https://www.amazon.{get_key_by_value(site, AmazonConfig.COUNTRY_SITE_SPRITE)}/dp/{asin}?psc=1'
                parent = item.get("parent") if item.get("parent") else ''  # 父商品
                node_label_path = item.get("nodeLabelPath") if item.get("nodeLabelPath") else ''  # 节点标签路径
                category_name = item.get("categoryName") or ''  # 大类目
                category_id = item.get("nodeIdPath").split(':')[0] if item.get("nodeIdPath") else ''  # 大类目id
                bsr_rank = item.get("bsrRank") or ''  # 大类目BSR排名
                subcategories = item.get("subcategories") if item.get("subcategories") else []  # 子分类
                subcategory = subcategories[0].get('label') if subcategories else ''  # 小类目名称
                subcategory_rank = subcategories[0].get('rank') if subcategories else ''  # 小类目排名
                subcategory_code = subcategories[0].get('code') if subcategories else ''  # 小类目id
                brs_json = copy.deepcopy(subcategories)
                brs_json = [{
                    "code": category_id,
                    "rank": bsr_rank,
                    "label": category_name
                }] + subcategories if subcategories else []
                brs_json_str = json.dumps(brs_json, ensure_ascii=False, separators=(",", ":"))

                units = item.get("totalUnits") if item.get("totalUnits") else ''  # 总销量
                amount = item.get("totalAmount") if item.get("totalAmount") else ''  # 月销售额
                month_units = item.get("amzUnit") if item.get("amzUnit") else ''  # 子体销量
                variations = item.get("variations") if item.get("variations") else ''  # 变体数
                price = item.get("price", '')  # 价格
                coupon = item.get("coupon") if item.get("coupon") else ''  # 优惠
                questions = item.get("questions") if item.get("questions") else ''  # Q&A
                ratings = item.get("reviews") if item.get("reviews") else ''  # 评论数
                stars = item.get("rating") if item.get("rating") else ''  # 评分
                fba = item.get("fba") if item.get("fba") else ''  # FBA
                profit = item.get("profit") if item.get("profit") else 0  # 毛利率
                available = item.get("availableDate") if item.get("availableDate") else ''  # 上架时间
                available = int(available) / 1000 if len(str(available)) == 13 else available

                seller_type = item.get("sellerType") if item.get("sellerType") else ''  # 配送方式
                sellers = item.get("sellers") if item.get("sellers") else ''  # 卖家数
                seller_name = item.get("sellerName") if item.get("sellerName") else ''  # 卖家名称
                seller_location = item.get("sellerNation") if item.get("sellerNation") else ''  # 卖家所属地
                seller_id = item.get("sellerId") or ''  # 卖家ID
                seller_url = f'https://www.amazon.{get_key_by_value(site, AmazonConfig.COUNTRY_SITE_SPRITE)}/sp?seller={seller_id}' if seller_id else ''

                sellerDto = item.get("sellerDto", {})  # 卖家信息
                business_name = ''
                business_address = ''
                business_type = ''
                phone = ''
                manger = ''

                if sellerDto:
                    business_name = sellerDto.get("businessName") if sellerDto.get("businessName") else ''  # 公司名称
                    business_address = sellerDto.get("businessAddress") if sellerDto.get("businessAddress") else ''  # 办公地址
                    business_type = sellerDto.get("businessType") if sellerDto.get("businessType") else ''  # 公司类型
                    phone = sellerDto.get("phone") if sellerDto.get("phone") else ''  # 电话
                    manger = sellerDto.get("manger") if sellerDto.get("manger") else ''  # 主管经理
                    sellerInfo = f'{business_name or ""} {business_address or ""} {business_type or ""} {phone or ""} {manger or ""}'.strip()  # 组合卖家信息
                    sellerDto = sellerInfo

                sellerUrl = f'https://www.amazon.de/sp?seller={item.get("sellerId")}' if item.get("sellerId", "") else ''  # 卖家链接

                # weight = item.get("weight", '')  # 重量
                weight = item.get("weightTag") if item.get("weightTag") else ''  # 重量标签
                # dimension = item.get("dimensions", '')  # 尺寸
                dimension = item.get("dimensionsTag") if item.get("dimensionsTag") else ''  # 尺寸标签

                d_col = {
                    'image': image,
                    'asin': asin,
                    'site': site,
                    'currency': currency,
                    'brand': brand,
                    'title': title,
                    'url': url,
                    'parent': parent,
                    'node_label_path': node_label_path,
                    'category_name': category_name,
                    'category_id': category_id,
                    'bsr_rank': bsr_rank,
                    'subcategories': subcategories,
                    'subcategory': subcategory,
                    'subcategory_rank': subcategory_rank,
                    'subcategory_code': subcategory_code,
                    'brs_json_str': brs_json_str,
                    'units': units,
                    'amount': amount,
                    'month_units': month_units,
                    'variations': variations,
                    'price': price,
                    'coupon': coupon,
                    'questions': questions,
                    'ratings': ratings,
                    'stars': stars,
                    'fba': fba,
                    'profit': profit,
                    'available': available,
                    'seller_type': seller_type,
                    'sellers': sellers,
                    'seller_name': seller_name,
                    'seller_url': seller_url,
                    'seller_location': seller_location,
                    'sellerDto': sellerDto,
                    'business_name': business_name,
                    'business_address': business_address,
                    'business_type': business_type,
                    'phone': phone,
                    'manger': manger,
                    'sellerUrl': sellerUrl,
                    'weight': weight,
                    'dimension': dimension
                }
                unique_id = asin + '_' + site
                if unique_id not in self.cur_info['unique_id']:
                    self.cur_info['unique_id'].add(unique_id)
                    self.cur_info['cur_fetch_datas'].append(d_col)
                    datas.append(d_col)
                else:
                    count += 1

        if count == self.cur_info['size']:
            # logging(f'本页皆为重复asin，次数{count}')
            self.same_count += count
        else:
            self.same_count = 0

        if self.same_count >= self.max_amount:  # 相同asin次数超接口请求返回上限
            logging(f'连续重复asin次数{self.same_count},进入记录点')
            self.cur_info['is_same_second'] = self.cur_info['is_same_first']
            self.cur_info['is_same_first'] = True

        return datas

    def get_trafficword(self, asin, market):
        url = "https://www.sellersprite.com/v3/api/relation/reversing"
        params = {
            'market': market,
        }
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://www.sellersprite.com',
            # 'referer': 'https://www.sellersprite.com/v3/keyword-reverse',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        }
        json_data = {
            'asin': asin,
            'limit': 10,
            'skip': 0,
            'month': '',
            'badges': [],
            'conversionKeywordTypes': [],
            'trafficKeywordTypes': [],
            'order': 12,
            'desc': True,
            'exactly': False,
            'ac': False,
            'filterDeletedKeywords': False,
        }

        for _ in range(3):
            self.page.post(url, params=params, json=json_data, headers=headers)
            if '登陆已失效' not in self.page.html:
                break
            self.login()
        else:
            return '获取流量词失败'
        data = self.page.json
        if not data.get('data', {}).get('items', {}):
            return '暂无'
        # print(data)
        keywords_list = [i['keywords'] for i in data['data']['items']]
        keywordCns_list = [i['keywordCn'] for i in data['data']['items']]
        # print(keywords_list)
        # print(keywordCns_list)
        # keyword_list = [i['keywords'] + ':' + i['keywordCn'] for i in data['data']['items']]
        keywordCns = ','.join(keywords_list)
        return keywordCns

    def run(self, origin_url, pages=1):
        self.cur_info = {
            'result': '完成',
            'status': 10,
            'finally_result': '',
            'finally_status': 10,
            'cur_total': 0,
            'cur_page': 1,
            'cur_fetch_datas': [],
            'unique_id': set(),
            'condition': {},
            'is_same_first': False,  # 条件一超过请求上限无新数据
            'is_same_second': False,  # 更换条件二超过请求上限无新数据
        }  # 初始化采集信息
        logging('正在采集卖家精灵数据...')
        netloc, params = parse_url(origin_url)

        if pages == -1:  # 抓取全部
            params['size'] = 100
            pages = 500
            self.max_total = 50000
        elif pages == -2:  # 抓取全部
            params['size'] = 100
            pages = 5000
            self.max_total = pages * params['size']

        self.cur_info['size'] = params['size']

        # # 初始化数据列表
        datas = []

        # 是否翻页抓取
        if pages <= 1:
            json_data = self.req_data(netloc, params) or []
            result = self.parse_json_data(json_data)
            datas.extend(result)
        else:
            fetch_page = pages
            while True:
                data_list, cur_page = self.get_all_datas(netloc, params, fetch_page)
                if len(data_list) > 1:
                    datas.extend(data_list)
                    self.same_count = 0
                elif self.cur_info['is_same_second']:
                    logging(f'连续两次筛选条件，接口数据皆为重复asin，判定为已无新数，终止采集！')
                    break
                elif self.cur_info['is_same_first']:
                    self.same_count = 0
                else:
                    logging(f"采集完成，累计采集 {self.cur_info['cur_page']} 页， {len(self.cur_info['cur_fetch_datas'])} 条数据")
                    break
                if len(datas) >= self.max_total or len(self.cur_info['cur_fetch_datas']) >= self.max_total:  # 数据量过大，终止采集
                    logging(f"数据量过大，采集终止，共采集 {self.cur_info['cur_page']} 页， {len(self.cur_info['cur_fetch_datas'])} 条数据")
                    break
                # # 找到最后一条数据的bsr_rank 大类排名  按升序抓
                # last_data = max([int(i.get('bsr_rank') or 1) for i in datas])
                # params['minRanking'] = last_data - 1
                # 找到最后一条数据的totalUnits 月销量  按降序抓
                last_data = min([int(i.get('amount') or 1) for i in datas])  # 需要取数据对应的字段，而非接口返回
                maxAmount = last_data - 1 if self.cur_info['is_same_first'] else last_data
                params['maxAmount'] = max(0, maxAmount)  # 确保 maxAmount 不小于0
                self.cur_info['condition']['maxAmount'] = params['maxAmount']

                params['page'] = 1
                fetch_page -= cur_page
                self.cur_info['cur_page'] = pages - fetch_page  # 当前一抓取页数
                if fetch_page <= 0:
                    logging(f"采集完成，累计采集 {self.cur_info['cur_page']} 页， {len(self.cur_info['cur_fetch_datas'])} 条数据")
                    break
        return datas

    def get_all_datas(self, netloc, params, fetch_page):
        """
        获取单个筛选条件大类排名降序下所有数据
        :param netloc:
        :param params:
        :param fetch_page:
        :return:
        """
        # 初始化数据列表
        list_datas = []
        cur_page = 1
        params['page'] = 1
        params['order'] = {
            # 'field': 'bsr_rank',
            # 'desc': False,
            # 'field': 'total_units',
            # 'desc': True,
            'field': 'total_amount',
            'desc': True,
        }
        fail_count = 0
        while True:
            # 请求数据
            json_data = self.req_data(netloc, params)

            if not json_data or not json_data.get('success'):
                logging("数据请求失败")
                fail_count += 1
                if fail_count >= 5:
                    logging("数据请求失败次数过多，终止采集")
                    break
                continue
            else:
                fail_count = 0

            result = self.parse_json_data(json_data)
            list_datas.extend(result)

            real_pages = int(json_data['data'].get('pages') or 1)
            total = int(json_data['data'].get('total') or 0)
            size = int(json_data['data'].get('size') or 1)
            cur_page = int(json_data['data'].get('page') or 1)

            # if not result:
            #     logging(f'当前所在第{cur_page}页未产生新数据，maxAmount:{params.get("maxAmount")}，返回数量：{total}，每页条数：{size}，当前采集{len(self.cur_info["cur_fetch_datas"])}')

            if len(list_datas) >= self.max_amount or cur_page * size >= self.max_amount:
                # logging(
                #         f"中止采集，已达到接口单个筛选条件请求上限 {self.max_amount}，当前采集{len(list_datas)}，当前页：{cur_page}，当前筛选条件【maxAmount：{params.get('maxAmount')}】，返回数量：{total}，每页条数：{size}，本次任务累计采集{len(self.cur_info['cur_fetch_datas'])}，如需更多数据，请更换条件筛选")
                # # print(f"当前筛选条件{params}")
                time.sleep(2)
                break
            elif cur_page >= real_pages or total <= size * cur_page or cur_page >= fetch_page:
                # print(f"到达采集限制，终止采集，当前页：{params['page']}，总条数：{total}，当前采集{len(list_datas)}，每页条数：{size}")
                # print(f"当前筛选条件{params}已达到上限，终止采集，当前页：{cur_page}，返回数量：{total}，每页条数：{size}，当前采集{len(self.cur_info['cur_fetch_datas'])}")
                break
            params['page'] += 1
        return list_datas, cur_page

    def get_more_datas(self, netloc, params, fetch_page):
        """
        获取单个筛选条件大类排名降序下所有数据
        :param netloc:
        :param params:
        :param fetch_page:
        :return:
        """
        # 初始化数据列表
        list_datas = []
        page = 1
        params['page'] = 1
        params['order'] = {
            'field': 'bsr_rank',
            'desc': False,
        }
        while True:
            if page >= fetch_page:
                # print(f"到达采集限制，终止采集，当前页：{params['page']}，总条数：{total}，每页条数：{size}")
                break
            # 请求数据
            json_data = self.req_data(netloc, params)

            params['page'] = page + 1

            if json_data:
                result = self.parse_json_data(json_data)
                list_datas.extend(result)

                # last_bsr_rank = int(result[-1].get('bsr_rank') or 1)
                last_bsr_rank = max([int(i.get('bsr_rank') or 1) for i in result])
                params['minRanking'] = last_bsr_rank + 1
                real_pages = int(json_data['data'].get('pages') or 1)
                total = int(json_data['data'].get('total') or 0)
                size = int(json_data['data'].get('size') or 1)
                cur_page = int(json_data['data'].get('page') or 1)
            else:
                logging("数据请求失败")
                break

            if cur_page >= real_pages or total <= size * cur_page or cur_page >= fetch_page:
                # print(f"到达采集限制，终止采集，当前页：{params['page']}，总条数：{total}，每页条数：{size}")
                break
        return list_datas

    def change_cookies(self):
        new_cookies = db_cookies.random()
        self.set_cookies(new_cookies)


def parse_url(url: str):
    """
    解析URL中的查询参数，并将它们转换为更复杂的数据结构。

    特别处理：
        - 将'true'/'false'字符串转换为布尔值。
        - 将'[]'解析为空列表。
        - 处理嵌套参数，如'order[field]'。
    参数:
    url (str): 需要解析的URL字符串。

    返回:
    tuple: 包含两个元素的元组。第一个元素是不包含查询参数的基本URL，第二个元素是一个字典，
           包含解析后的查询参数，参数名可能是嵌套的。
    """
    # 解析URL以获取各个组成部分
    parsed_data = urlparse(url)
    # 构建网络地址
    netloc = parsed_data.netloc
    # 使用parse_qs函数解析查询字符串，并得到一个字典
    params = parse_qs(parsed_data.query)
    # 初始化一个空字典，用于存储最终解析后的参数
    parsed_params = {}
    # 遍历解析后的查询参数字典中的每个键值对
    for key, value in params.items():
        # 检查参数名是否包含'['，以处理嵌套参数
        # 处理嵌套参数，如 'order[field]' 转换为 'order': {'field': ...}
        if '[' in key and ']' in key:
            main_key, sub_key = key.split('[')
            sub_key = sub_key.rstrip(']')
            if main_key not in parsed_params:
                parsed_params[main_key] = {}
            if len(value) == 1:
                parsed_params[main_key][sub_key] = convert_value(value[0])
            else:
                parsed_params[main_key][sub_key] = [convert_value(v) for v in value]
        else:
            # 非嵌套参数处理
            if len(value) == 1:
                if key == 'symbolFlag' and isinstance(convert_value(value[0]), bool):
                    parsed_params[key] = not convert_value(value[0])  # 取反
                elif key == 'selectType':
                    # selectType字段保持为字符串类型，与浏览器请求保持一致
                    parsed_params[key] = value[0]
                else:
                    parsed_params[key] = convert_value(value[0])
            else:
                parsed_params[key] = [convert_value(v) for v in value]
    # 返回基本URL和解析后的参数字典
    return netloc, parsed_params


def center_window(window, width=None, height=None):
    """使窗口居中显示"""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()

    if width is None:
        width = window.winfo_reqwidth()
    if height is None:
        height = window.winfo_reqheight()

    x_cordinate = int((screen_width / 2) - (width / 2))
    y_cordinate = int((screen_height / 2) - (height / 2))

    window.geometry(f"{width}x{height}+{x_cordinate}+{y_cordinate}")


def get_userinfo():
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口，因为我们只需要模态对话框

    # 设置对话框的大小
    DIALOG_WIDTH = 300
    DIALOG_HEIGHT = 180

    # 创建顶级窗口作为模态对话框
    dialog = tk.Toplevel()
    dialog.title("卖家精灵登录")

    # 设置对话框大小并居中
    center_window(dialog, width=DIALOG_WIDTH, height=DIALOG_HEIGHT)

    # 禁用对话框之外的交互，使其成为模态对话框
    dialog.grab_set()
    dialog.focus_set()

    # 用户名输入
    tk.Label(dialog, text="用户名:").pack()
    username_entry = tk.Entry(dialog)
    username_entry.pack(padx=10, pady=10)

    # 密码输入
    tk.Label(dialog, text="密码:").pack()
    password_entry = tk.Entry(dialog, show="*")  # 显示为星号
    password_entry.pack(padx=10, pady=10)

    def submit():
        """提交按钮的回调函数，用于获取输入并关闭对话框"""
        global username, password
        username = username_entry.get().strip()
        password = password_entry.get().strip()
        dialog.destroy()
        # root.destroy()  # 这里关闭主窗口

    # 提交按钮
    tk.Button(dialog, text="登录", command=submit).pack(pady=10)

    # 运行对话框的主循环
    dialog.wait_window(dialog)

    # 对话框关闭后，可以在这里继续处理用户名和密码
    print(f"用户名: {username}, 密码: {password}")

    # root.mainloop()  # 保持程序运行，直到对话框关闭
    return username, password


def insert_or_update_sprite(data, params):
    """
    将数据插入到数据库中。

    :param data: 包含商品信息的列表，每个元素是一个字典。
    :param params: 包含任务相关参数的字典，如任务ID、应用ID等。
    """

    # 处理卖家精灵数据表
    # 从params字典中获取相关参数
    app_id = params.get('app_id') or 0
    task_id = params.get('task_id') or 0
    date_time = params.get('date_time') or 0
    task_time = params.get('task_time') or 0
    data_status = params.get('data_status') or 0
    user_id = params.get('user_id') or 0
    username = params.get('username') or ''
    site = params.get('site') or ''
    create_time = params.get('create_time') or 0
    task_num = params.get('task_num') or 0
    platform_num = params.get('platform_num') or ''
    done_time = params.get('done_time') or 0

    status = params.get('status') or 10
    result = params.get('result') or '成功'
    run_num = 1

    # 定义用于插入或更新Amazon数据的SQL语句,确保unique_id列是数据库表中的唯一键或主键，以利用ON DUPLICATE KEY UPDATE特性
    # 定义用于插入或更新的SQL语句，针对`data_sprite_asin`表
    sql_upsert_sprite = """
            INSERT INTO data_sprite_asin (
                unique_id, app_id, task_id, datetime, task_time, data_status, 
                user_id, username, asin, site, title, brand, parent, node_label_path, 
                category_id, category_name, bsr_rank, subcategory_code, subcategory, 
                subcategory_rank, units, amount, month_units, variations, price, 
                currency, coupon, questions, ratings, stars, fba, profit, available, 
                manger, seller_name, seller_location, seller_type, sellers, weight, 
                dimension, image, url, business_type, business_address, business_name, 
                phone, brs_json_str, create_time,seller_url
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,  %s, %s, %s, %s, %s, %s, %s,  %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            ON DUPLICATE KEY UPDATE 
                app_id = VALUES(app_id), 
                task_id = VALUES(task_id), 
                datetime = VALUES(datetime), 
                task_time = VALUES(task_time), 
                data_status = VALUES(data_status), 
                user_id = VALUES(user_id), 
                username = VALUES(username), 
                asin = VALUES(asin), 
                site = VALUES(site), 
                title = VALUES(title), 
                brand = VALUES(brand), 
                parent = VALUES(parent), 
                node_label_path = VALUES(node_label_path), 
                category_id = VALUES(category_id), 
                category_name = VALUES(category_name), 
                bsr_rank = VALUES(bsr_rank), 
                subcategory_code = VALUES(subcategory_code), 
                subcategory = VALUES(subcategory), 
                subcategory_rank = VALUES(subcategory_rank), 
                units = VALUES(units), 
                amount = VALUES(amount), 
                month_units = VALUES(month_units), 
                variations = VALUES(variations), 
                price = VALUES(price), 
                currency = VALUES(currency), 
                coupon = VALUES(coupon), 
                questions = VALUES(questions), 
                ratings = VALUES(ratings), 
                stars = VALUES(stars), 
                fba = VALUES(fba), 
                profit = VALUES(profit), 
                available = VALUES(available), 
                manger = VALUES(manger), 
                seller_name = VALUES(seller_name), 
                seller_location = VALUES(seller_location), 
                seller_type = VALUES(seller_type), 
                sellers = VALUES(sellers), 
                weight = VALUES(weight), 
                dimension = VALUES(dimension), 
                image = VALUES(image), 
                url = VALUES(url), 
                business_type = VALUES(business_type), 
                business_address = VALUES(business_address), 
                business_name = VALUES(business_name), 
                phone = VALUES(phone),
                seller_url = VALUES(seller_url),
                brs_json_str = VALUES(brs_json_str);
        """

    # 准备插入数据的字典列表
    # 准备插入数据的元组列表
    insert_params = [
        (
            item.get('asin') + '_' + (site or item.get('site') or ''),
            app_id,
            task_id,
            date_time,
            task_time,
            data_status,
            user_id,
            username,
            item.get('asin'),
            site or item.get('site') or '',
            item.get('title') or '',
            item.get('brand') or '',
            item.get('parent') or '',
            item.get('node_label_path') or '',
            item.get('category_id') or '',
            item.get('category_name') or '',
            item.get('bsr_rank') or '',
            item.get('subcategory_code') or '',
            item.get('subcategory') or '',
            item.get('subcategory_rank') or '',
            item.get('units') or '',
            item.get('amount') or '',
            item.get('month_units') or '',
            item.get('variations') or '',
            item.get('price') or '',
            item.get('currency') or '',
            item.get('coupon') or '',
            item.get('questions') or '',
            item.get('ratings') or '',
            item.get('stars') or '',
            item.get('fba') or '',
            item.get('profit') or '',
            item.get('available') or '',
            item.get('manger') or '',
            item.get('seller_name') or '',
            item.get('seller_location') or '',
            item.get('seller_type') or '',
            item.get('sellers') or '',
            item.get('weight') or '',
            item.get('dimension') or '',
            item.get('image') or '',
            item.get('url') or '',
            item.get('business_type') or '',
            item.get('business_address') or '',
            item.get('business_name') or '',
            item.get('phone') or '',
            item.get('brs_json_str') or '',
            create_time,
            item.get('seller_url', ''),
        ) for item in data
    ]

    # 如果有数据需要插入，则执行插入操作
    if insert_params:
        # 执行插入操作
        rs = MS.insert_many(sql_upsert_sprite, insert_params)
        # 根据插入结果设置状态和结果信息
        f_status, f_result = (10, '成功') if rs > 0 else (10, '无新增或需要更新的数据') if rs == 0 else (20, '异常')
        # 记录任务执行情况
        logging(f'当前应用ID:{app_id};当前应用ID:{app_id};任务ID-{task_id};抓取{task_num}条数据;操作{rs}条{result}')
    else:
        f_status, f_result = (10, '无数据入库')

    if status == 20:
        # print('params:', params)
        # print('data:', data)
        # pg.alert(f"应用：{app_id}，id:{task_id}，数据插入异常，请检查！")
        if MS.err:
            msg = f"应用：{app_id}，id:{task_id}，数据插入异常，请检查！"
            logging(msg)
            fsmsg.send('卖家精灵数据采集', msg, MS.err)

    if f_status == 20:
        result += f' |入库提示:{f_result}'
        status = 20
    elif f_status == 10 and '成功' not in f_result:
        result += f' |入库提示:{f_result}'

    # 更新任务状态
    MS.update(
            f'update `task_goods` set status = %s, result = %s, platform_num = %s, task_num = %s, run_num = %s,done_time=%s where id = %s',
            (status, result, platform_num, task_num, run_num, done_time, task_id)
    )


if __name__ == '__main__':
    sss = SellerSpriteSpider()
    origin_url = "https://www.sellersprite.com/v3/product-research?market=DE&page=1&size=60&symbolFlag=false&monthName=bsr_sales_nearly&selectType=2&filterSub=false&weightUnit=g&order%5Bfield%5D=total_units&order%5Bdesc%5D=true&productTags=%5B%5D&nodeIdPaths=%5B%2212950651%3A360374031%3A360384031%22,%2212950651%3A359480031%3A5211931031%3A359481031%22,%223167641%3A368114031%3A2993231031%3A2993281031%3A2993282031%22,%2212950651%3A359480031%3A5211931031%3A5211937031%3A22936600031%22,%223167641%3A368114031%3A2993073031%3A26410524031%3A26410526031%22,%2211961464031%3A12419319031%3A10459546031%3A10459548031%22,%2211961464031%3A5846354031%3A5846359031%3A5846364031%22,%2211961464031%3A12419319031%3A10459546031%3A10459565031%22,%22327472011%3A10459546031%3A10459565031%22,%22355007011%3A3968975031%3A3969017031%3A9645610031%3A3969027031%22,%2212950651%3A360374031%3A360376031%3A360382031%22,%2212950651%3A27950684031%3A27950685031%3A359467031%22,%2211961464031%3A12709636031%3A11971708031%3A11971709031%3A2699203031%22,%2216435051%3A18175158"
    origin_url = "https://www.sellersprite.com/v3/product-research?market=DE&page=1&size=60&symbolFlag=false&monthName=bsr_sales_nearly&selectType=2&filterSub=false&weightUnit=g&order%5Bfield%5D=total_units&order%5Bdesc%5D=true&productTags=%5B%5D&nodeIdPaths=%5B%2212950651%3A360374031%3A360384031%22,%2212950651%3A359480031%3A5211931031%3A359481031%22,%223167641%3A368114031%3A2993231031%3A2993281031%3A2993282031%22,%2212950651%3A359480031%3A5211931031%3A5211937031%3A22936600031%22,%223167641%3A368114031%3A2993073031%3A26410524031%3A26410526031%22,%2211961464031%3A12419319031%3A10459546031%3A10459548031%22,%2211961464031%3A5846354031%3A5846359031%3A5846364031%22,%2211961464031%3A12419319031%3A10459546031%3A10459565031%22,%22327472011%3A10459546031%3A10459565031%22,%22355007011%3A3968975031%3A3969017031%3A9645610031%3A3969027031%22,%2212950651%3A360374031%3A360376031%3A360382031%22,%2212950651%3A27950684031%3A27950685031%3A359467031%22,%2211961464031%3A12709636031%3A11971708031%3A11971709031%3A2699203031%22,%2216435051%3A18175158031%3A16435211%3A345230011%3A235161011%22,%2211961464031%3A12709636031%3A11971707031%3A2699200031%3A11971739031%22,%222454118031%3A11971707031%3A2699200031%3A11971739031%22,%2284230031%3A2975644031%3A591298031%3A2975649031%3A591305031%22,%22192416031%3A197751031%3A202797031%3A202798031%22,%223167641%3A3490861%3A372854011%22,%2212950651%3A360374031%3A14494795031%3A367736031%22,%2212950651%3A360469031%3A360473031%3A14494911031%22,%2212950651%3A360541031%3A360542031%22,%2211961464031%3A12419317031%3A10459505031%3A10459518031%3A10459520031%22,%2278191031%3A79923031%3A27019304031%3A27019305031%3A27019307031%22%5D&sellerTypes=%5B%5D&dimensionTypeList=%5B%5D&sellerNationList=%5B%5D&putawayMonth=6&video=&open_in_browser=true"
    # pages = -1
    pages = 1
    datas = sss.run(origin_url, pages)
    print(len(datas))
    logging(f'cur_fetch_datas:{len(sss.cur_info["cur_fetch_datas"])}')
    # print(sss.cur_info)
    for item in sss.cur_info.items():
        if item[0] != 'cur_fetch_datas' and item[0] != 'unique_id':
            print(item)
