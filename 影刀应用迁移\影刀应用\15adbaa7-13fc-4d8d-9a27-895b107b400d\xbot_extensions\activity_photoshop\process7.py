import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        photoshop_instance = ""
        save_change = False
        quit = True
    else:
        photoshop_instance = args.get("photoshop_instance", "")
        save_change = args.get("save_change", False)
        quit = args.get("quit", True)
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="close", params={
            "photoshop_instance": photoshop_instance,
            "save_change": save_change,
            "quit": lambda: quit,
        }, _block=("关闭 PSD 文件", 1, "调用模块"))
    finally:
        pass
