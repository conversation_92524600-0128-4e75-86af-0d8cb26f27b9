# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
import os
from xbot import print, sleep
from .import package
from .package import variables as glv
from .resources.photoshop_api import Photoshop

from win32com.client import Dispatch, GetActiveObject, GetObject

from .js_code import ARTBOARD_EXPORT

# appRef = GetActiveObject("Photoshop.Application")
# docRef = appRef.ActiveDocument


class ExportOptionsSaveForWeb:
    """Options for exporting Save For Web files."""

    object_name = "ExportOptionsSaveForWeb"

    def __init__(self):
        self.obj = Dispatch("Photoshop.ExportOptionsSaveForWeb")
        self.PNG8 = False  # Sets it to PNG-24 bit
        self.Quality = 85

    @property
    def Blur(self) -> float:
        """apply blur to image to reduce artifacts ( default: 0.0 )"""
        return self.obj.Blur

    @Blur.setter
    def Blur(self, value: float) -> None:
        self.obj.Blur = value

    @property
    def ColorReduction(self) -> int:
        """color reduction algorithm ( default: Selective )
        0 (psPerceptualReduction)
        1 (psSelective)
        2 (psAdaptive)
        3 (psRestrictive)
        4 (psCustomReduction)
        5 (psBlackWhiteReduction)
        6 (psSFWGrayscale)
        7 (psMacintoshColors)
        8 (psWindowsColors)
        """
        return self.obj.ColorReduction

    @ColorReduction.setter
    def ColorReduction(self, value: int) -> None:
        self.obj.ColorReduction = value

    @property
    def Colors(self) -> float:
        """Read-write. The number of colors in the palette. Default: 256."""
        return self.obj.Colors

    @Colors.setter
    def Colors(self, value: float) -> None:
        self.obj.Colors = value

    @property
    def Dither(self) -> int:
        """Read-write. The type of dither. Default: 2 (psDiffusion)
        1 (psNoDither)
        2 (psDiffusion)
        3 (psPattern)
        4 (psNoise)
        """
        return self.obj.Dither

    @Dither.setter
    def Dither(self, value: int) -> None:
        self.obj.Dither = value

    @property
    def DitherAmount(self) -> int:
        """Read-write. The amount of dither. Default: 100.
        Note: Valid only when Dither = 2. See Dither."""
        return self.obj.DitherAmount

    @DitherAmount.setter
    def DitherAmount(self, value: int) -> int:
        self.obj.DitherAmount = value

    @property
    def Format(self) -> int:
        """Read-write. The file format to use. Default: 3
        (psCompuServeGIFSave)
        1 (psPhotoshopSave)
        2 (psBMPSave)
        3 (psCompuServeGIFSave)
        4 (psPhotoshopEPSSave)
        6 (psJPEGSave)
        7 (psPCXSave)
        8 (psPhotoshopPDFSave)
        10 (psPICTFileFormatSave)
        12 (psPixarSave)
        13 (psPNGSave)
        14 (psRawSave)
        15 (psScitexCTSave)
        16 (psTargaSave)
        17 (psTIFFSave)
        18 (psPhotoshopDCS_1Save)
        19 (psPhotoshopDCS_2Save)
        25 (psAliasPIXSave)
        26 (psElectricImageSave)
        27 (psPortableBitmapSave)
        28 (psWavefrontRLASave)
        29 (psSGIRGBSave)
        30 (psSoftImageSave)
        31 (psWirelessBitmapSave)1
        """
        return self.obj.Format

    @Format.setter
    def Format(self, value: int) -> None:
        self.obj.Format = value

    @property
    def IncludeProfile(self) -> bool:
        """Read-write. Indicates whether to include the document’s embedded color profile. Default: false"""
        return self.obj.IncludeProfile

    @IncludeProfile.setter
    def IncludeProfile(self, value: bool) -> None:
        self.obj.IncludeProfile = value

    @property
    def Interlaced(self) -> bool:
        """Read-write. Indicates whether to download in
        multiple passes; progressive. Default: false.

        """
        return self.obj.Interlaced

    @Interlaced.setter
    def Interlaced(self, value: bool) -> None:
        self.obj.Interlaced = value

    @property
    def Lossy(self) -> int:
        """Read-write. The amount of lossiness allowed.
        Default: 0."""
        return self.obj.Lossy

    @Lossy.setter
    def Lossy(self, value: int) -> None:
        self.obj.Lossy = value

    @property
    def MatteColor(self):
        """Read-write. The colors to blend transparent pixels against.
        此对象比较复杂, 暂时不优化
        """
        return self.obj.MatteColor

    @MatteColor.setter
    def MatteColor(self, value: float):
        self.obj.MatteColor = value

    @property
    def Optimized(self) -> bool:
        """Read-write. Indicates whether to create smaller
        but less compatible files. Default: true.
        Note: Valid only when format = 6
        (psJPEGSave). See Format."""
        return self.obj.Optimized

    @Optimized.setter
    def Optimized(self, value: bool) -> None:
        self.obj.Optimized = value

    @property
    def PNG8(self) -> bool:
        """Read-write. Indicates the number of bits; true = 8, false = 24. Default: true"""
        return self.obj.PNG8

    @PNG8.setter
    def PNG8(self, value: True) -> None:
        self.obj.PNG8 = value

    @property
    def Quality(self) -> int:
        """Read-write. The quality of the produced image (0 - 100 as percentage; default: 60)."""
        return self.obj.Quality

    @Quality.setter
    def Quality(self, value: int) -> None:
        self.obj.Quality = value

    @property
    def Transparency(self) -> bool:
        """Read-write. Indicates transparent areas of the
        image should be included in the saved image.
        Default: true."""
        return self.obj.Transparency

    @Transparency.setter
    def Transparency(self, value: bool) -> None:
        self.obj.Transparency = value

    @property
    def TransparencyAmount(self) -> int:
        """Read-write. The amount of transparency dither.
        Default: 100"""
        return self.obj.TransparencyAmount

    @TransparencyAmount.setter
    def TransparencyAmount(self, value: int) -> None:
        self.obj.TransparencyAmount = value

    @property
    def TransparencyDither(self) -> int:
        """Read-write. The transparency dither algorithm.
        Default: 1.
        1 (psNoDither)
        2 (psDiffusion)
        3 (psPattern)
        4 (psNoise)
        """
        return self.obj.TransparencyDither

    @TransparencyDither.setter
    def TransparencyDither(self, value: int) -> None:
        self.obj.TransparencyDither = value

    @property
    def typename(self):
        """Read-only. The class name of the referenced
        ExportOptionsSaveForWeb object"""
        return self.obj.typename

    @property
    def WebSnap(self) -> int:
        """Read-write. The tolerance amount within which
        to snap close colors to Web palette colors.
        Default: 0"""
        return self.obj.WebSnap

    @WebSnap.setter
    def WebSnap(self, value: int) -> int:
        self.obj.WebSnap = value


def get_all_layer(docRef, all_layer=[]):
    # print(len(all_layer))

    for item in docRef.ArtLayers:
        all_layer.append(item)

    if docRef.LayerSets.Count == 0:
        for item in docRef.LayerSets:
            get_all_layer(item)
        return all_layer


def get_all_layerset(docRef, all_layerset=[], flag_layerset=""):
    for item in docRef.LayerSets:
        all_layerset.append(item)
    for item in docRef.LayerSets:
        if flag_layerset and item.Name == flag_layerset:
            continue
        get_all_layerset(item)
    if docRef.LayerSets.Count == 0:
        return
    return all_layerset


def export_as_png(ps_instance, mode, name, save_path):
    docRef = ps_instance.doc 
    cache = []
    all_layerset = []

    if mode == "artlayer":
        res = get_all_layer(docRef, all_layer=all_layerset)

    if mode == "layerset":
        res = get_all_layerset(docRef, all_layerset=all_layerset, flag_layerset=name)

    find_layer = False

    for item in res:
        if item.Name == name:
            item.Visible = True
            find_layer = True
        else:
            cache.append([item, item.Visible])
            item.Visible = False

    assert find_layer, f"找不到图层或图层组 {name}"

    options = ExportOptionsSaveForWeb()
    options.Blur = 1
    options.ColorReduction = 2
    _, ext = os.path.splitext(save_path)
    if ext.upper() in ".PNG":
        options.Format = 13
    
    docRef.Export(save_path, ExportAs=2, Options=options.obj)

    for item in cache:
        item[0].Visible = item[1]


def add_smart_object_layer_by_image(ps_instance, filepath, layer_name):
    """通过图片添加智能图层"""
    
    assert os.path.exists(filepath), f"找不到文件 {filepath}"
    assert isinstance(layer_name, str), f"图层名 {layer_name} 应该是字符串"

    app = ps_instance.app
    layer = app.ActiveDocument.ArtLayers.Add()
    app.ActiveDocument.ActiveLayer = layer

    desc = Dispatch("Photoshop.ActionDescriptor")
    app.ExecuteAction(app.StringIDToTypeID("newPlacedLayer"), desc, 3)

    desc = Dispatch("Photoshop.ActionDescriptor")
    desc.PutPath(app.CharIDToTypeID("null"), filepath)
    app.ExecuteAction(app.StringIDToTypeID("placedLayerReplaceContents"), desc, 3)
    if layer_name:
        app.ActiveDocument.ActiveLayer.Name = layer_name
    return app.ActiveDocument.ActiveLayer.Name


def delete_layer(ps_instance, layer_name):
    """删除图层"""
    ps_instance.layer(layer_name).Delete()


def active_document(ps_instance):
    """激活ps对象"""
    ps_instance.app.ActiveDocument = ps_instance.doc


def parse_path_to_layer(ps_instance, layer_name:str):
    """通过文本定位图层, 组1>组2>组3>图层1, 并激活图层
    :param ps_instance: object, ps_instace
    :param layer_name: str, 图层名称
    """
    # 判断 layer_name 是否包含 > , 如果不包含, 使用 ps_instance.layer 方法, 否按照层级查找

    if ">" not in layer_name:
        layer = ps_instance.layer(layer_name)
        
    else: 
        *layersets, layer_name = [path.strip() for path in layer_name.split(">")]

        current_layerset = ps_instance.doc
        for layerset_name in layersets:
            current_layerset = current_layerset.LayerSets(layerset_name)

        layer = current_layerset.Layers(layer_name)

    ps_instance.app.ActiveDocument.ActiveLayer = layer
    return layer


def replace_image_frame(ps_instance, layer_name, image_path):
    """替换图框
    :param ps_instance: object, ps_instace
    :param layer_name: str, 图层名称
    :param image_path: str, 图片路径
    """
    # 激活图层, 替换图框
    app = ps_instance.app
    parse_path_to_layer(ps_instance, layer_name)
    
    # Set dialog mode to 3 (no dialogs)
    dialog_mode = 3

    # Create the ActionDescriptor
    desc1369 = Dispatch("Photoshop.ActionDescriptor")

    # Set the identifier
    id_idnt = app.CharIDToTypeID("Idnt")
    desc1369.PutInteger(id_idnt, 23)

    # Set the path
    id_null = app.CharIDToTypeID("null")
    desc1369.PutPath(id_null, image_path)

    # Set the transform options
    id_ftcs = app.CharIDToTypeID("FTcs")
    id_qcst = app.CharIDToTypeID("QCSt")
    id_qcsa = app.CharIDToTypeID("Qcsa")
    desc1369.PutEnumerated(id_ftcs, id_qcst, id_qcsa)

    # Set the offset
    desc1370 = Dispatch("Photoshop.ActionDescriptor")
    id_hrzn = app.CharIDToTypeID("Hrzn")
    id_pxl = app.CharIDToTypeID("#Pxl")
    desc1370.PutUnitDouble(id_hrzn, id_pxl, 0.0)

    id_vrtc = app.CharIDToTypeID("Vrtc")
    desc1370.PutUnitDouble(id_vrtc, id_pxl, 0.0)

    id_ofst = app.CharIDToTypeID("Ofst")
    desc1369.PutObject(id_ofst, id_ofst, desc1370)

    # Set the layer replacement options
    desc1371 = Dispatch("Photoshop.ActionDescriptor")
    id_from = app.CharIDToTypeID("From")

    ref90 = Dispatch("Photoshop.ActionReference")
    id_lyr = app.CharIDToTypeID("Lyr ")
    ref90.PutIdentifier(id_lyr, 21)
    desc1371.PutReference(id_from, ref90)

    id_t = app.CharIDToTypeID("T   ")
    ref91 = Dispatch("Photoshop.ActionReference")
    ref91.PutIdentifier(id_lyr, 23)
    desc1371.PutReference(id_t, ref91)

    id_replace_layer = app.StringIDToTypeID("replaceLayer")
    id_plc = app.CharIDToTypeID("Plc ")
    desc1369.PutObject(id_replace_layer, id_plc, desc1371)

    # Execute the action
    app.ExecuteAction(id_plc, desc1369, dialog_mode)




def main(args):
    print(ARTBOARD_EXPORT.format(save_dir=r"C:\\Users\\<USER>\\Downloads\\export-png"))

    pass
