import shutil
import socket
import inspect
import os
import sys
import json
import time
import traceback
import urllib.parse
import re
import zipfile
import hashlib
import concurrent.futures
import math
import pandas as pd
from utils_mrc.MysqlHelper import MysqlHelper, logging
from datetime import datetime, timedelta, time as dtime
from html import unescape
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse, quote


def convert_value(val):
    """
    将特定字符串值转换为对应的Python类型。
    """
    if val.lower() == 'true':
        return True
    elif val.lower() == 'false':
        return False
    # elif val == '[]':
    #     return []
    elif '[' in val and ']' in val:
        return eval(val)
    else:
        try:
            # 尝试转换为整数
            return int(val)
        except ValueError:
            # 如果不是整数，则保持原样
            return val


def parse_url_href(url):
    results = url.split('/', )
    if len(results) < 3:
        return ''
    href = '/'.join(results[:3])
    return href


def extract_number(s):
    """
    从给定的字符串中提取数字，如果没有找到数字，则返回0。

    参数:
    s (str): 需要提取数字的字符串。

    返回:
    int: 提取出的第一个数字，如果字符串中没有数字则返回0。
    """
    s = str(s)
    match = re.search(r'\d+', s)
    return int(match.group(0)) if match else 0


def generate_sha256_hash(content):
    """生成图片的哈希值"""
    return hashlib.sha256(content.encode('utf-8')).hexdigest()


def convert_timestamp(inp_date=None, format_str='%Y-%m-%d'):
    """
    将日期字符串转换为时间戳。

    参数:
    inp_date (str): 日期字符串，格式为 'YYYY-MM-DD'。

    返回:
    float: 时间戳，表示自1970年1月1日以来的秒数。
    """
    inp_date = inp_date or datetime.now().strftime(format_str)
    if isinstance(inp_date, int):
        return inp_date
    elif isinstance(inp_date, float):
        return int(inp_date)
    elif isinstance(inp_date, datetime):
        return int(inp_date.timestamp())
    elif isinstance(inp_date, str):
        # 将字符串解析为 datetime 对象
        date_obj = datetime.strptime(inp_date, format_str)
        # 将 datetime 对象转换为时间戳
        timestamp = date_obj.timestamp()
        timestamp = int(timestamp)
        return timestamp


def get_last_n_days(n):
    """
    生成过去n天的日期列表。

    该函数以当前日期为基准，生成并返回包含过去n天的日期列表，列表中的日期按从近到远的顺序排列。

    参数:
    n (int): 日期范围，表示过去n天。

    返回:
    list: 包含过去n天日期的列表，日期格式为ISO格式（YYYY-MM-DD）。
    """
    # 获取当前日期
    today = datetime.now().date()
    # 生成过去n天的日期列表 当天在前
    dates = [(today - timedelta(days=i)).isoformat() for i in range(n)]
    return dates


def generate_uuid():
    if 'uuid' not in globals():
        import uuid
    return str(uuid.uuid4())


def calculate_md5_hash(content):
    if isinstance(content, bytes):
        return hashlib.md5(content).hexdigest()
    elif isinstance(content, str):
        return hashlib.md5(content.encode("utf-8")).hexdigest()
    else:
        raise TypeError("内容必须是字节或字符串类型！")


def convert_timestamp_column(df, column_name, timezone='Asia/Shanghai') -> pd.DataFrame:
    """
    将 DataFrame 中指定列的时间戳字符串转换为 Unix 时间戳，并处理无效数据。

    参数:
    - df (pandas.DataFrame): 包含时间戳列的 DataFrame。
    - column_name (str): 需要转换的时间戳列名称。

    返回:
    - pandas.Series: 转换后的 Unix 时间戳 Series。
    """
    # 将时间戳字符串转换为 datetime 对象，并处理无效数据
    df = df.copy()
    try:
        df[column_name] = pd.to_datetime(df[column_name], errors='coerce').dt.tz_localize(timezone, ambiguous='infer')
        # 将 datetime 对象转换为 Unix 时间戳，并将 NaT 值替换为 0
        df[column_name] = df[column_name].apply(lambda x: x.timestamp() if pd.notna(x) else 0)
        # 转换为 int64 类型
        df[column_name] = df[column_name].astype('int64')
        return df[column_name]
    except Exception as e:
        return 0


def generate_unique_id(df: pd.DataFrame, unique_columns: list = None, split_char='') -> pd.DataFrame:
    """
    添加一个 unique_id 列，并去除重复记录，保留最新的记录。

    :param df: pandas DataFrame
    :param unique_columns: 列名列表
    :return: 处理后的 DataFrame
    """
    # 如果 unique_columns 为空，则使用 df 的所有列
    if unique_columns is None:
        unique_columns = df.columns.tolist()
    # 检查列是否存在，如果不存在则添加空列
    for col in unique_columns:
        if col not in df.columns:
            # if is_test_environment(): logging(f"列 {col} 不存在，已添加空列")
            # df[col] = ''
            raise ValueError(f'生成唯一键的列 {col} 不存在！')

    # 创建一个新列，将 unique_columns 中的值组合成单个字符串
    df['combined'] = df[unique_columns].astype(str).agg(split_char.join, axis=1)
    # 计算每个组合字符串的 MD5 哈希值并赋值给 unique_id 列
    df['unique_id'] = df['combined'].apply(lambda x: calculate_md5_hash(x))
    # 删除重复项并保留最新记录
    df = df.drop_duplicates(subset=['unique_id'], keep='last')
    # 可选：删除临时的 combined 列
    df = df.drop(columns=['combined'])
    return df


def url_get_keyword(url, default='k'):
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)
    k = query_params.get(default, [''])[0]
    return k


def insert_data_from_pd(table_name: str, df, upd_cols: str = None, sql_conn: MysqlHelper = None):
    try:
        ms = sql_conn
        upd_cols = upd_cols or 'create_time'

        # 去除百分比 应用该函数到每一列
        df = remove_percent_signs(df)
        df_columns_new = [to_snake_case(col) for col in df.columns]  # 统一转换为蛇形命名
        df.columns = df_columns_new
        # 构建插入语句
        sql_tb_columns = sql_get_table_fields(table_name, ms)
        set_columns = list(set(sql_tb_columns) & set(df_columns_new))
        tag_columns = [f"`{col}`" for col in set_columns]
        placeholders = ', '.join(['%s'] * len(tag_columns))
        update_columns = set(set_columns) - set(upd_cols.split(','))
        update_clauses = ', '.join([f"`{col}`=VALUES(`{col}`)" for col in update_columns])
        insert_sql = f"""
        INSERT INTO {table_name} ({', '.join(tag_columns)})
        VALUES ({placeholders})
        ON DUPLICATE KEY UPDATE {update_clauses}
        """
        insert_data = df[set_columns].values.tolist()
        ms.insert_many(insert_sql, insert_data)
        result = ms.err if ms.err else True
        # logging(f"向 {table_name} 中操作了 {len(insert_data)} 条记录")
    except Exception as e:
        result = str(e)
        logging(f"{table_name}插入pd数据时发生错误: {e}")
    return result


# 定义一个函数来移除数字中的百分号
def remove_percent_signs(df: pd.DataFrame):
    # 使用 applymap 函数来应用 transformation
    # 只在指定字符串格式的列中应用
    df = df.applymap(lambda x: x.strip('%') if isinstance(x, str) and re.match(r'^[\d\s.]*%$', x) else x)
    # 将结果转换为数值，非数值默认填充为 NaN
    df = df.apply(pd.to_numeric, errors='ignore')
    df.fillna('', inplace=True)  # 处理 NaN 值
    return df


def sql_get_table_fields(table_name, ms: MysqlHelper = None):
    ms = ms or MysqlHelper()
    table_name = table_name.replace('`', '')
    if '.' in table_name:
        database_name, table_name = table_name.split('.', 1)
        sql = f"SELECT COLUMN_NAME FROM information_schema.columns WHERE TABLE_SCHEMA = '{database_name}' AND TABLE_NAME = '{table_name}'"
    else:
        sql = f"SELECT COLUMN_NAME FROM information_schema.columns WHERE TABLE_NAME = '{table_name}'"

    fields = ms.get_all(sql)
    key_list = [x[0] for x in fields]

    if not key_list:
        raise Exception(f'表【{table_name}】不存在，请检查表名是否正确！')

    return key_list


def sql_generate_tb_statement(df, columns_map, engine=None, tb_name='data_test_template', tb_comment='数据模板表', if_create=False):
    """
    生成创建 MySQL 表的 SQL 语句。

    根据提供的 DataFrame 和列名映射，生成一个包含固定和动态列的表结构 SQL 语句。
    如果指定的数据库连接引擎为空，则使用默认的 MysqlHelper 创建的引擎。
    可以选择性地创建表。

    参数:
    - df: pandas DataFrame，包含要创建表的数据。
    - columns_map: 表头译文列表或者直接传入映射字典
    - engine: 数据库连接引擎。如果未提供，则使用 MysqlHelper 创建的默认引擎。
    - tb_name: 要创建的表名。默认为 'test_data_template_table'。
    - tb_comment: 表的注释。默认为 '数据模板表'。
    - if_create: 是否实际创建表。默认为 False。

    返回:
    - create_table_sql_str: 创建表的 SQL 语句字符串。
    """
    # 检查是否需要导入 pandas
    if 'pd' not in globals():
        import pandas as pd

    # 导入 sqlalchemy 相关模块
    from sqlalchemy import MetaData, Table, Column, Integer, Float, String, Boolean, DECIMAL, text, TIMESTAMP
    from sqlalchemy.types import VARCHAR
    from sqlalchemy.dialects.mysql import TINYINT
    from sqlalchemy.schema import CreateTable

    try:
        df = remove_percent_signs(df)
        # 使用提供的引擎或创建新的引擎
        engine = engine or MysqlHelper().create_engine()

        if isinstance(columns_map, dict):
            dict_columns = columns_map
        else:
            # 获取 DataFrame 列名并应用新的列名
            columns = df.columns.tolist()
            dict_columns = dict(zip(columns_map, columns))
            df.columns = columns_map

        # 创建元数据对象
        metadata = MetaData()

        # 定义固定的表结构部分
        fixed_columns = [
            Column('id', Integer, primary_key=True, autoincrement=True, comment='自增ID'),
            Column('unique_id', VARCHAR(100), unique=True, nullable=False, server_default=text("''"), comment='唯一标识'),
            Column('app_id', Integer, nullable=False, server_default=text("0"), comment='应用ID'),
            Column('task_id', Integer, index=True, nullable=False, server_default=text("0"), comment='任务ID'),
            Column('datetime', Integer, index=True, nullable=False, server_default=text("0"), comment='所属时间'),
            Column('task_time', Integer, nullable=False, server_default=text("0"), comment='任务时间'),
            Column('data_status', TINYINT(1), nullable=False, server_default=text("0"), comment='数据状态 1正常2异常3无数据'),
            Column('user_id', Integer, index=True, nullable=False, server_default=text("0"), comment='用户'),
            Column('username', VARCHAR(40), nullable=False, server_default=text("''"), comment='用户名称'),
        ]

        # 固定的表结构部分（在动态字段之后）
        fixed_columns_after = [
            Column('create_time', Integer, nullable=False, server_default=text("0"), comment='创建时间'),
            Column('update_time', TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间'),
        ]

        # 获取固定列的名称
        fixed_column_names = {col.name for col in fixed_columns + fixed_columns_after}

        # 根据 DataFrame 的列名和数据类型创建表的列
        dynamic_columns = []
        for column, data_type in df.dtypes.items():
            column_name = f"{column}"
            if column_name in fixed_column_names:
                continue  # 跳过已存在的固定列
            comment = dict_columns.get(column_name)
            if data_type == 'object':
                max_length = df[column].astype(str).str.len().max()
                length = find_nearest_power_of_two(max_length)
                dynamic_columns.append(Column(column_name, VARCHAR(length), nullable=False, server_default=text("''"), comment=comment))
            elif data_type == 'bool':
                dynamic_columns.append(Column(column_name, TINYINT(1), nullable=False, server_default=text("'0'"), comment=comment))
            elif data_type == 'int64':
                dynamic_columns.append(Column(column_name, Integer, nullable=False, server_default=text("0"), comment=comment))
            elif data_type == 'float64':
                dynamic_columns.append(Column(column_name, DECIMAL(10, 2), nullable=False, server_default=text("0.00"), comment=comment))
            else:
                print(f"{column_name}不支持的数据类型: {data_type}")

        # 合并所有列
        all_columns = fixed_columns + dynamic_columns + fixed_columns_after

        # 创建表对象
        table = Table(
                tb_name,
                metadata,
                *all_columns,
                mysql_engine='InnoDB',
                mysql_charset='utf8mb4',
                mysql_comment=tb_comment
        )
        # 如果需要创建表，则执行创建操作
        if if_create:
            try:
                # 执行创建表
                table.create(bind=engine)
                print(f"已创建表{tb_name}")
            except Exception as e:
                print(f"创建表{tb_name}失败: {e}")
        else:
            # 获取创建表的 SQL 语句
            create_table_sql = CreateTable(table).compile(dialect=engine.dialect)
            create_table_sql_str = create_table_sql.string
            print(create_table_sql_str)
    except Exception as e:
        traceback.print_exc()
        print(f"生成 SQL 语句时发生错误，{e}")
    exit()
    return create_table_sql_str


def find_nearest_power_of_two(length):
    """找到最接近给定长度的 2 的幂次方"""
    length = max(length, 2)
    return 2 ** int(math.ceil(math.log2(length)))


def parse_column_name(column_name, column_map=None, reverse=False):
    if isinstance(column_map, list):
        column_map = column_map or LingxingConfig.column_targets_ads
        if '-本币' not in str(column_map): column_name = column_name.replace('-本币', '')
        column_name = column_name.strip().lower()
        for target in column_map:
            if target["title"].strip().lower() == column_name:
                return target["column"]
        else:
            print(f'[{column_name}]未找到匹配项')
            return column_name
    else:
        # 确保映射已初始化
        LingxingConfig.init_column_map()
        # 如果未传入 column_map 则使用默认的
        column_map = column_map or LingxingConfig.column_map
        # 去除 '-本币' 并处理字符串
        column_name_or_value = column_name.replace('-本币', '').strip().lower()

        if reverse:
            # 通过列名找到标题
            for title, column in column_map.items():
                if column == column_name_or_value:
                    return title
            print(f'[{column_name_or_value}]未找到匹配项（作为列名）')
            return column_name_or_value
        else:
            # 通过标题找到列名
            if column_name_or_value in column_map:
                return column_map[column_name_or_value]
            else:
                print(f'[{column_name_or_value}]未找到匹配项（作为标题）')
                return column_name_or_value


def multi_threaded_fetch_data(func, it, max_workers=None):
    # 使用 ThreadPoolExecutor 并行处理 issues
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        results = list(executor.map(func, it))
    return results


class LingxingConfig:
    column_targets_count = [
        {'column': 'rdate', 'title': '日期'},
        {'column': 'parent_asin', 'title': '父ASIN'},
        {'column': 'fba_stock', 'title': 'fba库存'},
        {'column': 'asin', 'title': 'ASIN'},
        {'column': 'msku', 'title': 'MSKU'},
        {'column': 'local_name', 'title': '品名'},
        {'column': 'model', 'title': '型号'},
        {'column': 'local_sku', 'title': 'SKU'},
        {'column': 'spu_name', 'title': '款名'},
        {'column': 'spu', 'title': 'SPU'},
        {'column': 'tags', 'title': 'listing标签'},
        {'column': 'item_name', 'title': '标题'},
        {'column': 'mid', 'title': '国家'},
        {'column': 'sid', 'title': '店铺'},
        {'column': 'bid', 'title': '品牌'},
        {'column': 'cid', 'title': '一级分类'},
        {'column': 'cid2', 'title': '二级分类'},
        {'column': 'cid3', 'title': '三级分类'},
        {'column': 'principal_names', 'title': '负责人'},
        {'column': 'developer_names', 'title': '开发人'},
        {'column': 'product_create_time', 'title': '创建时间'},
        {'column': 'landed_price', 'title': '售价(总价)'},
        {'column': 'volume', 'title': '销量'},
        {'column': 'amount', 'title': '销售额'},
        {'column': 'order_items', 'title': '订单量'},
        {'column': 'volume_chain_ratio', 'title': '销量环比'},
        {'column': 'amount_chain_ratio', 'title': '销量额环比'},
        {'column': 'order_chain_ratio', 'title': '订单量环比'},
        {'column': 'volume_yoy_ratio', 'title': '销量同比'},
        {'column': 'amount_yoy_ratio', 'title': '销量额同比'},
        {'column': 'order_yoy_ratio', 'title': '订单量同比'},
        {'column': 'net_amount', 'title': '净销售额'},
        {'column': 'b2b_volume', 'title': 'B2B 销量'},
        {'column': 'b2b_amount', 'title': 'B2B 销售额'},
        {'column': 'b2b_order_items', 'title': 'B2B 订单量'},
        {'column': 'promotion_volume', 'title': '促销销量'},
        {'column': 'promotion_amount', 'title': '促销销售额'},
        {'column': 'promotion_order_items', 'title': '促销订单量'},
        {'column': 'avg_custom_price', 'title': '销售均价'},
        {'column': 'avg_volume', 'title': '平均销量'},
        {'column': 'promotion_discount', 'title': '促销折扣'},
        {'column': 'fbm_buyer_expenses', 'title': 'FBM买家运费'},
        {'column': 'cate_rank', 'title': '大类排名'},
        {'column': 'small_cate_rank', 'title': '小类排名'},
        {'column': 'return_count', 'title': '退款量'},
        {'column': 'return_amount', 'title': '退款金额'},
        {'column': 'return_rate', 'title': '退款率'},
        {'column': 'avg_star', 'title': '评分'},
        {'column': 'reviews_count', 'title': '评论数'},
        {'column': 'gross_profit', 'title': '结算毛利润'},
        {'column': 'predict_gross_profit', 'title': '订单毛利润'},
        {'column': 'gross_margin', 'title': '结算毛利率'},
        {'column': 'predict_gross_margin', 'title': '订单毛利率'},
        {'column': 'roi', 'title': 'ROI'},
        {'column': 'return_goods_count', 'title': '退货量'},
        {'column': 'return_goods_rate', 'title': '退货率'},
        {'column': 'afn_fulfillable_quantity', 'title': 'FBA-可售'},
        {'column': 'available_inventory', 'title': '可用库存'},
        {'column': 'fbm_quantity', 'title': 'FBM可售'},
        {'column': 'available_days', 'title': 'FBA可售天数预估'},
        {'column': 'fbm_available_days', 'title': 'FBM可售天数预估'},
        {'column': 'oversea_quantity', 'title': '海外仓可用'},
        {'column': 'local_quantity', 'title': '本地可用'},
        {'column': 'purchase_num', 'title': '采购量'},
        {'column': 'month_stock_sales_ratio', 'title': '月库销比'},
        {'column': 'out_stock_date', 'title': '断货时间'},
        {'column': 'reserved_fc_transfers', 'title': 'FBA-待调仓'},
        {'column': 'reserved_fc_processing', 'title': 'FBA-调仓中'},
        {'column': 'afn_inbound_receiving_quantity', 'title': 'FBA-入库中'},
        {'column': 'afn_total_inbound', 'title': 'FBA-小计'},
        {'column': 'reserved_customerorders', 'title': 'FBA-待发货'},
        {'column': 'afn_inbound_shipped_quantity', 'title': 'FBA-在途'},
        {'column': 'afn_inbound_working_quantity', 'title': 'FBA-计划入库'},
        {'column': 'afn_unsellable_quantity', 'title': 'FBA-不可售'},
        {'column': 'cpc', 'title': 'CPC'},
        {'column': 'ctr', 'title': 'CTR'},
        {'column': 'spend', 'title': '广告花费'},
        {'column': 'sb_spend', 'title': 'sb广告费'},
        {'column': 'sbv_spend', 'title': 'sbv广告费'},
        {'column': 'sd_spend', 'title': 'sd广告费'},
        {'column': 'sp_spend', 'title': 'sp广告费'},
        {'column': 'roas', 'title': 'ROAS'},
        {'column': 'acos', 'title': 'ACOS'},
        {'column': 'acoas', 'title': 'ACoAS'},
        {'column': 'asoas', 'title': 'ASoAS'},
        {'column': 'cpo', 'title': 'CPO'},
        {'column': 'cpu', 'title': 'CPU'},
        {'column': 'cpm', 'title': 'CPM'},
        {'column': 'ad_sales_amount', 'title': '广告销售额'},
        {'column': 'ad_order_quantity', 'title': '广告订单量'},
        {'column': 'ad_direct_order_quantity', 'title': '直接成交订单量'},
        {'column': 'ad_direct_sales_amount', 'title': '直接成交销售额'},
        {'column': 'adv_rate', 'title': '广告订单占比'},
        {'column': 'ad_cvr', 'title': '广告CVR'},
        {'column': 'impressions', 'title': '展示'},
        {'column': 'clicks', 'title': '点击'}
    ]
    column_targets_ads = [
        {'column': 'impressions', 'title': '曝光量'},
        {'column': 'top_of_search_impression_share', 'title': '搜索结果首页首位IS'},
        {'column': 'clicks', 'title': '点击'}, {'column': 'ctr', 'title': 'CTR'}, {'column': 'cpc', 'title': 'CPC'}, {'column': 'spends', 'title': '花费'},
        {'column': 'sales', 'title': '销售额'}, {'column': 'direct_sales', 'title': '直接成交销售额'}, {'column': 'indirect_sales', 'title': '间接成交销售额'},
        {'column': 'acos', 'title': 'ACoS'}, {'column': 'roas', 'title': 'ROAS'}, {'column': 'orders', 'title': '广告订单'},
        {'column': 'direct_orders', 'title': '直接成交订单'}, {'column': 'indirect_orders', 'title': '间接成交订单'},
        {'column': 'indirect_orders_of_orders_percent', 'title': '间接订单占比'}, {'column': 'cpa', 'title': 'CPA'}, {'column': 'cvr', 'title': 'CVR'},
        {'column': 'unit_price', 'title': '广告笔单价'}, {'column': 'direct_unit_price', 'title': '直接成交笔单价'},
        {'column': 'indirect_unit_price', 'title': '间接成交笔单价'}, {'column': 'bn_orders', 'title': '"品牌新买家"订单数量'},
        {'column': 'bn_cvr', 'title': '"品牌新买家" 转化率'}, {'column': 'bn_sales', 'title': '"品牌新买家" 销售额'},
        {'column': 'bn_units', 'title': '"品牌新买家" 销量'}, {'column': 'ad_units', 'title': '广告销量'}, {'column': 'direct_units', 'title': '直接成交销量'},
        {'column': 'indirect_units', 'title': '间接成交销量'}, {'column': 'vcpm', 'title': 'vCPM'}, {'column': 'view_impressions', 'title': '可见展示次数'},
        {'column': 'dpv', 'title': 'DPV'}, {'column': 'video_5second_views', 'title': '5秒观看次数'}, {'column': 'video_5second_views_rate', 'title': '5秒观看率'},
        {'column': 'fist_quartile_views', 'title': '视频播放至四分之一的次数'}, {'column': 'midpoint_views', 'title': '视频播放至一半的次数'},
        {'column': 'third_quartile_views', 'title': '视频播放至四分之三的次数'}, {'column': 'complete_views', 'title': '视频完整播放的次数'},
        {'column': 'unmutes', 'title': '视频取消静音的次数'}, {'column': 'vtr', 'title': 'VTR'}, {'column': 'vctr', 'title': 'vCTR'},
        {'column': 'branded_search', 'title': '品牌搜索次数'}, {'column': 'avg_impressions_frequency', 'title': '平均触达次数'},
        {'column': 'cumulative_reach', 'title': '累计触达用户'}, {'column': 'tags', 'title': '标签'}, {'column': 'ad_units_percent', 'title': '广告销量百分比'},
        {'column': 'orders', 'title': '14天总订单数'}, {'column': 'sales', 'title': '14天总销售额'}, {'column': 'ad_units', 'title': '14天总件数'},
        {'column': 'bn_orders', 'title': '14天新客户订单'}, {'column': 'bn_sales', 'title': '14天新客户订单销售额'},
        {'column': 'bn_units', 'title': '14天新客户订单件数'}, {'column': 'bn_orders_percentage', 'title': '14天新客户订单百分比'},
        {'column': 'bn_sales_percentage', 'title': '14天新客户订单销售额百分比'}, {'column': 'bn_units_percentage', 'title': '14天新客户订单件数百分比'},
        {'column': 'oa_orders', 'title': 'OtherASIN销量'}, {'column': 'oa_sales', 'title': 'OtherASIN销售额'},
        {'column': 'oa_origin_orders', 'title': 'OtherASIN订单数'}, {'column': 'vcpm_orders', 'title': 'OtherASIN订单数(含vCPM)'},
        {'column': 'vcpm_units', 'title': 'OtherASIN销量(含vCPM)'}, {'column': 'vcpm_sales', 'title': 'OtherASIN销售额(含vCPM)'},
        {'column': 'bn_orders_percentage', 'title': '"品牌新买家"订单占比'}, {'column': 'bn_sales_percentage', 'title': '"品牌新买家"销售额占比'},
        {'column': 'bn_units_percentage', 'title': '"品牌新买家"销量占比'},
        {'column': 'match_type', 'title': '匹配方式'},
        {'column': 'bid', 'title': '竞价'},
        {'column': 'default_bid', 'title': '默认竞价'},
        {'column': 'valid_status', 'title': '有效状态'},
        {'column': 'source', 'title': '数据来源'},
        {'column': 'date', 'title': '日期'},
        {'column': 'remark', 'title': '备注'},
        {'column': 'store', 'title': '店铺名称'},
        {'column': 'keyword', 'title': '关键词'},
        {'column': 'country', 'title': '国家'},
        {'column': 'type', 'title': '类型'},
        {'column': 'portfolios', 'title': '广告组合'},
        {'column': 'campaign', 'title': '广告活动'},
        {'column': 'ad_group', 'title': '广告组'},
        {'column': 'ad_group_delivery_type', 'title': '广告组投放类型'},
        {'column': 'ad_status', 'title': '广告有效状态'},
        {'column': 'asin', 'title': 'asin'},
        {'column': 'msku', 'title': 'msku'},
    ]

    # 用于存储标题到列的映射
    column_map = {}

    # 依赖于所有的 column_targets_ads
    @classmethod
    def get_all_column_targets(cls):
        # 合并所有 column_targets_ads 属性
        columns = []
        for attr in dir(cls):
            if attr.startswith("column_targets"):
                columns.extend(getattr(cls, attr))
        return columns

    @classmethod
    def init_column_map(cls):
        # 仅在首次调用时生成映射
        if not cls.column_map:
            all_columns = cls.get_all_column_targets()
            cls.column_map = {target["title"].strip().lower(): target["column"] for target in all_columns}

    @staticmethod
    def get_title(value):
        LingxingConfig.init_column_map()
        dict = LingxingConfig.column_map
        for k, v in dict.items():
            if v == value:
                return k
        return value

    @staticmethod
    def get_date_column(columns: list) -> str:
        """
        从给定的列名列表中找到日期相关的列名。

        参数:
        columns (list): 包含列名的列表，这些列名是从数据库表中获取的。

        返回:
        str: 日期相关的列名，如果未找到则返回空字符串。
        """
        # 使用生成器表达式在提供的列名列表中查找第一个匹配的日期列名
        # 如果没有找到匹配项，则返回空字符串
        return next((column for column in ['rdate', 'date', '日期'] if column in columns), '')


class AmazonConfig:
    # 亚马逊相关配置信息
    COUNTRY_SITE_SPRITE = {
        'co.uk': 'uk',
        'uk': 'uk',
        'com': 'us',
        'com.be': 'be',
        'us': 'us',
        'es': 'es',
        'it': 'it',
        'fr': 'fr',
        'de': 'de',
        '德国': 'de',
        '美国': 'us',
        '英国': 'uk',
        '法国': 'fr',
        '意大利': 'it',
        '西班牙': 'es',
        '比利时': 'be',
        '波兰': 'pl',
        '荷兰': 'nl',
        '瑞典': 'se',
        '加拿大': 'ca',
        '墨西哥': 'mx',
    }
    CURRENCY = {
        "co.uk": "GBP",
        "uk": "GBP",
        "us": "USD",
        "es": "EUR",
        "it": "EUR",
        "fr": "EUR",
        "de": "EUR",
        "com": "USD",
        "德国": "EUR",
        "美国": "USD",
        "英国": "GBP",
        "法国": "EUR",
        "意大利": "EUR",
        "西班牙": "EUR"
    }
    MARKETPLACETOCOUNTRY = {
        "ATVPDKIKX0DER": "us",
        "A1F83G8C2ARO7P": "uk",
        "A1PA6795UKMFR9": "de",
        "A13V1IB3VIYZZH": "fr",
        "APJ6JRA9NG5V4": "it",
        "A1RKKUPIHCS9HS": "es",
        "ARBP9OOSHTCHU": "eg",
        "A21TJRUUN4KGV": "in",
        "A1VC38T7YXB528": "jp",
        "A2EUQ1WTGCTBG2": "ca",
        "A2Q3Y263D00KWC": "br",
        "A1AM78C64UM0Y8": "mx",
        "A39IBJ37TRP1C6": "au",
        "AD2EMQ3L3PG8S": "ru",
        "A1805IZSGTT6HS": "nl",
        "A2VIGQ35RCS4UG": "ae",
        "A17E79C6D8DWNP": "sa",
        "A19VAU5U5O7RUS": "sg",
        "A1C3SOZRARQ6R3": "pl",
        "A2NODRKZP88ZB9": "se",
        "AMEN7PMS3EDWL": "be",
        "A33AVAJ2PDY3EV": "tr",
    }

    # 常用站点目标地址
    SITE_CODE = {
        'co.uk': ['W1S 3PR', 'W1S 3', 'W1S3', ],
        'uk': ['W1S 3PR', 'W1S 3', 'W1S3', ],
        'es': '28035',
        'it': '00185',
        'fr': '75020',
        'de': '14199',
        'co.jp': '163-8001',
        'jp': '163-8001',
        'com': ['90001', '10001']  # 以数组形式存储多个匹配值
    }
    # 常用站点目标地址 的值集
    ADDRESS_CODE = set([item for value in SITE_CODE.values() for item in (value if isinstance(value, list) else [value])])
    # yxy常用站点
    COMMON_SITE = ['co.uk', 'de', 'fr', 'it', 'es', 'com', ]
    # 常用站点语言
    LANGUAGES = {
        'com': 'en_US',
        'us': 'en_US',
        'de': 'de_DE',
        'uk': 'en_GB',
        'co.uk': 'en_GB',
        'fr': 'fr_FR',
        'it': 'it_IT',
        'es': 'es_ES',
        'ca': 'en_CA',
        'mx': 'es_MX',
        'au': 'en_AU',
        'br': 'pt_BR',
        'cn': 'zh_CN',
        'jp': 'ja_JP',
        'in': 'en_IN',
        'sg': 'en_SG',
        'tr': 'tr_TR',
        'ae': 'en_AE',
        'sa': 'ar_SA',
        'eg': 'ar_EG',
        'il': 'he_IL',
    }

    CURRENCYS = {
        '$': 'ARS', '￥': 'CNY', '€': 'EUR', 'A$': 'AUD', '£': 'GBP', 'C$': 'CAD', '¥': 'JPY', '₽': 'RUB', 'RM': 'MYR', 'S/.': 'PEN', '฿': 'THB', 'NT$': 'TWD', 'Rp': 'IDR',
        'S$': 'SGD', '₱': 'PHP', '₦': 'NGN', '₫': 'VND', 'KSh': 'KES', 'ج.م': 'EGP', 'HK$': 'HKD', 'MAD': 'MAD', 'CFA': 'XOF', 'ر.س': 'SAR', '₹': 'INR', 'د.إ': 'AED',
        '₨': 'PKR', '৳': 'BDT', 'zł': 'PLN', 'Rs': 'LKR', 'R$': 'BRL', '₩': 'KRW', 'NZ$': 'NZD', '₺': 'TRY', 'Kč': 'CZK', 'Fr': 'CHF', 'Ft': 'HUF', 'kr': 'DKK', 'Ks': 'MMK'
    }

    @staticmethod
    def get_local_site(site):
        return AmazonConfig.COUNTRY_SITE_SPRITE.get(site) or site

    @staticmethod
    def get_amz_site(value):
        dict = AmazonConfig.COUNTRY_SITE_SPRITE
        for k, v in dict.items():
            if v == value:
                return k
        return value


def separate_currency_price(price_str, transform_symbols=False):
    """
    分离价格字符串中的货币符号和数值部分。
    支持识别开头或结尾的货币符号，以及逗号作为小数点的欧洲格式。
    """
    price_str = re.sub(r'\s+', '', price_str)
    match = re.match(r'^([^\d.,]+)?([\d,]+\.\d{2}|[\d,]+)([^\d.,]+)?$', price_str)
    if match:
        start_symbol = match.group(1) or ''
        end_symbol = match.group(3) or ''
        symbol = start_symbol + end_symbol  # 合并起始和结束的货币符号
        price_value = match.group(2).replace(',', '.')  # 将逗号替换为小数点

        # 确保只有一个点作为小数点
        if price_value.count('.') > 1:
            price_value = price_value.replace('.', '', price_value.count('.') - 1)

        symbol = symbol.strip()  # 去除货币符号前后的空白
        if transform_symbols: symbol = AmazonConfig.CURRENCYS.get(symbol, '未识别')
        return symbol, float(price_value)
    else:
        return '', price_str


# 通过值找键
def get_key_by_value(value, dict=None):
    if not dict:
        dict = AmazonConfig.COUNTRY_SITE_SPRITE
    for k, v in dict.items():
        if v == value:
            return k
    return value


def update_url_param(url, name, value):
    url = unescape(url)  # 处理url中包含HTML实体（如&amp;代表&）的字符串
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)
    query_params[name] = [value]
    # 重新编码查询参数
    encoded_query = urlencode(query_params, doseq=True)
    # 构建新的URL
    new_url = urlunparse((parsed_url.scheme, parsed_url.netloc, parsed_url.path, parsed_url.params, encoded_query, parsed_url.fragment))

    return new_url


def get_today(format='%Y%m%d%H%M%S'):
    formatted_date = datetime.now().strftime(format)
    return formatted_date


def remove_non_bmp_chars(input_str):
    """移除字符串中的非BMP字符"""
    return re.sub(r'[^\u0000-\uffff]', '', input_str)


def split_get_list(content, unique=True):
    """
    将输入的内容转换为列表。

    如果输入的是字符串，将其按照特定的分隔符进行分割，转换为列表。
    如果输入的是列表，则直接返回。

    参数:
    content (str 或 list): 需要转换为列表的输入内容。

    返回:
    list: 转换后的列表。
    """
    # 初始化数据列表
    data_list = []
    if content:
        # 如果输入的是字符串
        if isinstance(content, str):
            # 替换字符串中的分隔符
            content = content.replace('，', '、').replace('\n', '、').replace(',', '、')
            # 按照分隔符分割字符串，生成列表
            data_list = content.split("、")
        # 如果输入的是列表，直接赋值给返回变量
        elif isinstance(content, list):
            data_list = content
        if unique: data_list = list(set(data_list))
    # 返回处理后的列表
    return data_list


def text_to_unique_id(filename, len=8):
    """
    根据文件名生成唯一的数字 ID。

    :param filename: 文件名
    :return: 唯一的数字 ID
    """
    # 计算文件名的 SHA-256 哈希值
    hash_object = hashlib.sha256(filename.encode())
    hash_hex = hash_object.hexdigest()

    # 将哈希值转换为整数
    unique_id = int(hash_hex, 16) % (10 ** len)  # 生成一个 8 位的整数

    return unique_id


def unzip_and_rename(save_path):
    # 遍历指定目录下的所有文件
    for filename in os.listdir(save_path):
        if filename.endswith('.zip'):  # 如果文件是 .zip 结尾
            zip_file_path = os.path.join(save_path, filename)

            # 获取不带扩展名的文件名
            base_name = os.path.splitext(filename)[0]

            # 创建临时目录
            temp_dir = os.path.join(save_path, 'temp')
            os.makedirs(temp_dir, exist_ok=True)

            # 解压到临时目录
            with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)

            # 获取解压后的 xlsx 文件
            for inner_filename in os.listdir(temp_dir):
                if inner_filename.endswith('.xlsx'):
                    xlsx_file_path = os.path.join(temp_dir, inner_filename)
                    new_xlsx_file_path = os.path.join(save_path, f"{base_name}.xlsx")

                    # 移动并重命名 xlsx 文件
                    shutil.move(xlsx_file_path, new_xlsx_file_path)

            # 删除临时目录
            shutil.rmtree(temp_dir)

            # 解压完成后删除 .zip 文件
            os.remove(zip_file_path)
            # print(f"Deleted {zip_file_path}")


def to_snake_case(name):
    # """将带有连续下划线和空白的列名转换为蛇形命名"""
    # # 移除字符串中的多余下划线和空白
    # cleaned_name = re.sub(r'[ _]+', '_', name).strip('_')
    # # 转换为小写
    # snake_case_str = cleaned_name.lower()
    # return snake_case_str
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()


def convert_keys_to_snake_case(data):
    """
    递归地将嵌套字典或列表中的所有键转换为蛇形命名法。

    如果传入的数据是列表，则对列表中的每个元素递归调用本函数。
    如果传入的数据是字典，则将字典中的每个键转换为蛇形命名法，并递归调用本函数处理每个值。
    如果传入的数据既不是列表也不是字典，则直接返回该数据。

    参数:
    data: 要转换的字典或列表，或者任何其他数据类型。

    返回:
    转换后的数据，其中所有字典键都采用蛇形命名法。
    """
    if isinstance(data, list):
        # 对列表中的每个元素递归调用本函数
        return [convert_keys_to_snake_case(item) for item in data]
    elif isinstance(data, dict):
        # 将字典中的每个键转换为蛇形命名法，并递归调用本函数处理每个值
        return {to_snake_case(key): convert_keys_to_snake_case(value) for key, value in data.items()}
    else:
        # 如果数据既不是列表也不是字典，则直接返回
        return data


def chinese_to_pinyin(work):
    """将中文列名转换为英文"""
    if 'lazy_pinyin' not in globals():
        from pypinyin import lazy_pinyin
    # 清理特殊字符
    cleaned_name = work.replace('-', '_').replace('"', '_').strip().strip('_').lower()

    pinyin_list = lazy_pinyin(cleaned_name)
    result = '_'.join(pinyin_list).lower()
    result = to_snake_case(result)
    return result


def calculate_date_range(report_name):
    if '关键词报告-近3天-' in report_name:
        report_name = report_name.replace('近', '前')
    report_name = report_name.replace('一', '1').strip()
    if report_name.count('-') >= 1 and report_name.count('天') >= 1:
        day_str = report_name.split('-')[1].split('天')[0]
    else:
        day_str = report_name
    today = datetime.now().date()
    end_date = today

    match = re.search(r'\d+', day_str)
    if match:
        day_num = int(match.group(0))
        if day_str.startswith('前'):
            days = day_num
            end_date = today + timedelta(days=-1)
        else:
            days = day_num - 1
        start_date = today - timedelta(days=days)
        return f"{start_date.strftime('%Y-%m-%d')} - {end_date.strftime('%Y-%m-%d')}"
    return None


def extract_number_sales(text):
    '''
    提取销量数字
    :param text: 文本
    :return: 数字
    '''
    text = text.lower()  # 将文本转为小写
    match = re.search(r'(\d+)([kmb])?', text)
    if match:
        number = int(match.group(1))
        unit = match.group(2)
        if unit == 'k':
            return number * 1000
        elif unit == 'm':
            return number * 1000000
        elif unit == 'b':
            return number * 1000000000
        else:
            return number
    return 0


def convert_to_int(text):
    '''
    提取连接所有数字，转换为int
    :param text: 文本
    :return: 数字
    '''
    s1 = re.sub(r'\D', '', str(text)) or 0
    s2 = int(s1)
    return s2


def get_available_port():
    if 'closing' not in globals():
        from contextlib import closing
    with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as s:
        s.bind(('', 0))
        s.listen(1)
        port = s.getsockname()[1]
    return port


def make_less_than_10(n):
    # 将数字转换为字符串
    str_n = str(n)

    # 检查是否有小数部分
    if '.' in str_n:
        return float(str_n)

    # 在第一位后面添加小数点
    if len(str_n) > 1:
        str_n = str_n[0] + '.' + str_n[1:]

    # 转换回浮点数
    return float(str_n)


def get_chrome_free_port(min_port=9222, max_port=9299):
    if 'psutil' not in globals():
        import psutil
    # 存储chrome进程使用的端口
    chrome_ports = set()

    w = make_less_than_10(psutil.Process(os.getpid()).pid)  # 根据进程id延时获取，防止进程未完全启动就获取端口导致错误
    time.sleep(w)

    # 遍历所有进程
    for proc in psutil.process_iter(['pid', 'name']):
        # 检查进程名是否为chrome.exe
        if proc.info['name'] == 'chrome.exe':
            # 尝试获取进程的所有连接信息
            try:
                for conn in proc.connections():
                    # 检查连接状态是否为TCP类型且状态为ESTABLISHED
                    port = conn.laddr.port
                    if conn.status == psutil.CONN_ESTABLISHED and conn.type == socket.SOCK_STREAM and conn.laddr.ip == '127.0.0.1' and port not in chrome_ports:
                        chrome_ports.add(port)
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass  # 忽略没有权限访问或已经不存在的进程
    print("Chrome 浏览器已被使用端口：", chrome_ports) if chrome_ports else None

    all_ports = set(range(min_port, max_port + 1))
    difference = all_ports - chrome_ports
    first_available_port = min(difference) if difference else None
    print(f"即将使用的浏览器端口：{first_available_port}")
    if not first_available_port:
        raise Exception("没有可用端口!")

    return first_available_port


def check_port(port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('localhost', port))
            print(f'端口 {port} 可用')
            return True
        except OSError:
            print(f'端口 {port} 不可用')
            return False


def get_cur_run_file():
    # 获取当前帧（即调用some_method()的帧）的上一级帧
    frame = inspect.currentframe().f_back
    # 从帧中获取文件名
    file_full_path = inspect.getframeinfo(frame).filename
    file_name = os.path.basename(file_full_path)
    # print(f"当前运行: {file_name}")
    return file_name


def initialize_project_path():
    """ 通过环境变量获取路径 """
    project_path = os.getenv('PYTHON_PROJECT_PATH', r'D:\Documents\pythonProject')
    sys.path.append(project_path)
    # print(f'已添加项目路径: {project_path}')


def clear_plugin_cache_port(port):
    try:  # 清除插件缓存文件
        plugin_cache_path = rf'{os.getenv("TEMP")}\DrissionPage\userData_{port}\Default\Secure Preferences'
        # print("插件缓存路径:", plugin_cache_path)
        if os.path.exists(plugin_cache_path):
            os.remove(plugin_cache_path)
    except Exception:
        pass  # 如果文件不存在，忽略异常


def decode_values(data):
    """ 递归解码字典中的 URL 编码的字符串 """
    if isinstance(data, dict):
        return {k: decode_values(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [decode_values(item) for item in data]
    elif isinstance(data, str):
        return urllib.parse.unquote(data)
    else:
        return data


def get_token(cookies):
    return next((decode_values(value) for key, value in cookies.items() if 'token' in key.lower()), '')


def rpa_save_info(source, cookies):
    cookies_str = json.dumps(cookies)
    # token = decode_values(cookies.get('token') or cookies.get('auth-token') or cookies.get('authToken') or cookies.get('auth_token') or '')
    token = get_token(cookies)
    db = MysqlHelper()
    db.insert(
            "INSERT INTO rpa.rpa_info (source, info_data, remark, expired) VALUES (%s, %s, %s, %s) ON DUPLICATE KEY UPDATE info_data=VALUES(info_data), remark=VALUES(remark), expired=VALUES(expired)",
            (source, cookies_str, token, 0)
    )
    if db.err:
        print(db.err)
    else:
        print(f'{source} rpa信息已保存。')


def rpa_get_info(source, key=None, check_status=False):
    # 获取数据库查询结果
    sql = f'select * from rpa.rpa_info where source like "%{source}%"'
    if check_status: sql += ' and expired = 0'
    result = MysqlHelper().get_dict_one(sql)
    # 如果没有指定key或者没有查询到结果，则直接返回整个结果
    if not key or not result:
        return result
    # 根据key获取对应的数据
    if key in ['info_data', 'cookies']:
        data = result.get('info_data') or '{}'
        data = json.loads(data)
    elif key in ['remark', 'token']:
        data = decode_values(result.get('remark'))
    else:
        # 对于其他key，直接返回对应的值
        data = result.get(key)
    return data


initialize_project_path()

if __name__ == "__main__":
    result = to_snake_case('glProductGroup')
    print(result)
