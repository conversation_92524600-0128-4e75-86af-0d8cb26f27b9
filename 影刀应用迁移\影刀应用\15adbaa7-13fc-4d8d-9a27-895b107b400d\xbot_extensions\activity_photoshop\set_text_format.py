# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import re
import os
import xbot
import json
import win32com.client

from xbot import print, sleep
from .import package
from .package import variables as glv
from .js_code import SET_TEXT_LAYER_FORMAT


def set_text_format(ps_instance, layer_name, text_styles):
    """设置图层字体的样式"""

    # 激活指定的图层

    if ">" not in layer_name:
        try:
            layer = ps_instance.app.ActiveDocument.ArtLayers(layer_name)
            # print(layer)
        except Exception as e:
            layer = ps_instance.layer(layer_name, only_visible=False)
    else:
        *layersets, layer_name = [path.strip() for path in layer_name.split(">")]

        current_layerset = layer_name.doc
        for layerset_name in layersets:
            current_layerset = current_layerset.LayerSets(layerset_name)
        layer = current_layerset.Layers(layer_name)

    ps_instance.doc.ActiveLayer =  layer

    photoshop_app = ps_instance.doc.Application

    range_code = ""
    for style in text_styles:
        start = style["start"]
        end = style["end"] if style["end"] is not None else "undefined"
        font_family = '"' + style["font_family"] + '"' if style["font_family"] else "undefined"
        font_size = style["font_size"] if style["font_size"] else "undefined"
        font_color = f"Text.toRGBColor({int(style['font_color'][1:3], 16)}, {int(style['font_color'][3:5], 16)}, {int(style['font_color'][5:7], 16)})" if style["font_color"] else "undefined"
        range_code += f'  ranges.add(new TextStyleRange({start}, {end}, new TextStyle({font_family}, {font_size}, {font_color})));\n'

    code = f"""
        function execute(doc) {{
            var opts = new TextOptions(doc.activeLayer.textItem);
            opts.contents = doc.activeLayer.textItem.contents;
            var ranges = new TextStyleRanges();
            {range_code}
            opts.ranges = ranges;
            Text.modifyTextLayer(doc, opts, doc.activeLayer);
        }};

        execute(app.activeDocument);"""

    js_code = SET_TEXT_LAYER_FORMAT + code
    photoshop_app.DoJavaScript(js_code)


def main(args):
    ps_instance = args.get("ps_instance")
    layer_name = args.get("layer_name")
    text_styles = args.get("text_styles")

    # test
    # app = win32com.client.Dispatch("Photoshop.Application")
    # ps_instance = object()
    # ps_instance.doc
    # text_styles = [
    #     {"start": 0, "end": 1, "font_family": "ArialMT", "font_size": None, "font_color": None},
    #     {"start": 1, "end": 30, "font_family": None, "font_size": None, "font_color": None},
    #     {"start": 5, "end": 10, "font_family": "ArialMT", "font_size": 30, "font_color": "#00FF00"},
    #     {"start": 7, "end": 15, "font_family": None, "font_size": 80, "font_color": "#FF0000"},
    #     {"start": 25, "end": None, "font_family": "ArialMT", "font_size": 30, "font_color": "#00FF00"},
    # ]

    set_text_format(ps_instance, layer_name, text_styles)
