# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
from xbot import print, sleep
from .import package

from PIL import Image, ImageChops, ImageOps, ImageFont, ImageDraw
import os 

class PIL_API:
    def __init__(self, file_name=None, im=None):
        if file_name:
            if not os.path.exists(file_name):
                raise ValueError(f'图像 {file_name} 不存在')

            try:
                self.im = Image.open(file_name)
                self.file_name = file_name 
            except:
                raise ValueError(f'打开图片{file_name}失败，请检查图片是否有效')
        elif im:
            self.im = im 
        else:
            raise ValueError(f"初始化失败")
    
    def size(self):
        '''
        获取图片尺寸
        * @return <tuple>: width, height
        '''
        return self.im.size
    
    def resize(self, width, height, save_name=None):
        '''
        修改图片尺寸
        * @param width, 修改后的宽度
        * @param height, 修改后的高度
        * @save_name, 保存的文件名
        '''
        resize_img = self.im.resize((int(width), int(height)))
        resize_img = PIL_API(im=resize_img)
        if save_name:
            resize_img.save(save_name)
        return resize_img
    
    def save(self, save_name):
        '''
        图片保存，可以实现格式转换
        '''
        if not save_name:
            raise ValueError(f'图片路径未指定，无法保存')

        rgb_im = self.im
        _, file_extension = os.path.splitext(save_name)
        if file_extension.lower() == '.jpg' or file_extension.lower() == '.jpeg':
            rgb_im = rgb_im.convert('RGB')

        #文件夹处理
        file_folder= os.path.dirname(save_name)
        self._createfolderIfNeeded(file_folder)

        rgb_im.save(save_name)

