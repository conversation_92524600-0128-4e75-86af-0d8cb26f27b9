import feapder
import pymysql
import logging as std_logging  # 重命名标准库的logging避免冲突
import time
import random
from typing import Dict, List, Optional, Tuple
# 修改导入方式，与现有代码库保持一致
from utils_mrc.pub_fun import *
from utils_mrc.MysqlHelper import logging as mrc_logging
from utils_mrc.FeiShuAPI import fsmsg
# 暂时注释掉AmazonSK导入，使用现有的代理方式
# import sys
# import os
# sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
# from 亚马逊.Amazon import AmazonSK
import requests


amazon_sites = {
    "US": {"url": "amazon.com", "country": "美国", "postcode": "10001"},
    "CA": {"url": "amazon.ca", "country": "加拿大", "postcode": "M5V 3L9"},
    "UK": {"url": "amazon.co.uk", "country": "英国", "postcode": "EC1A 1BB"},
    "DE": {"url": "amazon.de", "country": "德国", "postcode": "10117"},
    "FR": {"url": "amazon.fr", "country": "法国", "postcode": "75001"},
    "IT": {"url": "amazon.it", "country": "意大利", "postcode": "00144"},
    "ES": {"url": "amazon.es", "country": "西班牙", "postcode": "28013"},
    "JP": {"url": "amazon.co.jp", "country": "日本", "postcode": "100-0005"},
    "AU": {"url": "amazon.com.au", "country": "澳大利亚", "postcode": "2000"},
    "IN": {"url": "amazon.in", "country": "印度", "postcode": "400001"},
    "MX": {"url": "amazon.com.mx", "country": "墨西哥", "postcode": "06140"},
    "BR": {"url": "amazon.com.br", "country": "巴西", "postcode": "20031-000"}
}


class AmazonDeliverySpider(feapder.AirSpider):
    """
    Amazon配送信息爬虫
    使用Playwright抓取Amazon商品页面的配送信息
    """
    
    def __init__(self, *args, proxy_type=1, **kwargs):
        super().__init__(*args, **kwargs)
        self.proxy_type = proxy_type

        # 暂时保持原有的代理方式，但使用与其他脚本一致的proxy_type参数
        self.proxy_type = proxy_type
        self.use_proxy = proxy_type > 0
        mrc_logging(f'Amazon配送爬虫初始化，代理类型：{proxy_type}，代理模式：{self.use_proxy}')
        # 使用现有代码库的数据库配置
        try:
            from utils_mrc.MysqlHelper import MS
            self.ms = MS
            self.mysql_config = None  # 使用MS实例，不需要单独配置
        except ImportError:
            # 如果导入失败，使用原有配置作为备用
            self.mysql_config = {
                'host': '*************',
                'port': 3306,
                'user': 'root',
                'password': '123456',
                'database': 'rpa',
                'charset': 'utf8mb4'
            }
            self.ms = None
        # 不再需要Playwright配置，使用AmazonSK的Session方式
        # 设置日志
        std_logging.basicConfig(level=std_logging.INFO)
        self.logger = std_logging.getLogger(__name__)
        # feapder配置 - 使用单线程避免Playwright线程问题
        self.custom_setting = {
            'SPIDER_THREAD_COUNT': 1,  # 改为单线程
            'SPIDER_MAX_RETRY_TIMES': 3,  # 重试次数
            'REQUEST_LOST_TIMEOUT': 600,  # 请求超时时间
            'RETRY_FAILED_REQUESTS': True,  # 重试失败请求
        }
        
    def start_requests(self):
        """
        从数据库获取需要抓取的URL
        """
        try:
            if self.ms:
                # 使用现有代码库的数据库连接
                sql = "SELECT asin, url FROM data_amazon_asin_attr WHERE app_id = 13"
                results = self.ms.get_all(sql)
                self.logger.info(f"从数据库获取到 {len(results or [])} 条数据")

                for row in results or []:
                    asin, url = row
                    if url and asin:
                        yield feapder.Request(
                            url=url,
                            callback=self.parse_delivery_info,
                            asin=asin  # 传递asin参数
                        )
            else:
                # 备用方案：使用原有的pymysql连接
                connection = pymysql.connect(**self.mysql_config)
                with connection.cursor() as cursor:
                    sql = "SELECT asin, url FROM data_amazon_asin_attr WHERE app_id = 13"
                    cursor.execute(sql)
                    results = cursor.fetchall()

                    self.logger.info(f"从数据库获取到 {len(results)} 条数据")

                    for asin, url in results:
                        if url and asin:
                            yield feapder.Request(
                                url=url,
                                callback=self.parse_delivery_info,
                                asin=asin  # 传递asin参数
                            )
                connection.close()

        except Exception as e:
            self.logger.error(f"数据库连接错误: {e}")
            # 发送错误通知
            try:
                fsmsg.send('亚马逊到货爬虫', '数据库连接错误', str(e))
            except:
                pass
            
    # 删除get_browser方法，改用AmazonSK的Session方式
        
    # 不再需要浏览器相关方法，使用AmazonSK的Session方式
            
    def close_browser_old(self):
        """
        关闭浏览器（旧版本，已弃用）
        """
        pass
            
    def safe_get_text(self, page, xpath: str, timeout: int = 5000) -> str:
        """
        安全获取元素文本
        """
        try:
            element = page.wait_for_selector(xpath, timeout=timeout)
            if element:
                return element.text_content().strip()
            return ""
        except Exception as e:
            self.logger.debug(f"获取元素失败 {xpath}: {e}")
            return ""
            
    def check_delivery_block_exists(self, page) -> bool:
        """
        检查配送信息块是否存在
        """
        try:
            # 检查配送信息容器是否存在
            delivery_container = page.locator('//div[@id="mir-layout-DELIVERY_BLOCK-slot-PRIMARY_DELIVERY_MESSAGE_LARGE"]')
            return delivery_container.count() > 0
        except:
            return False
            
    def simulate_zip_code_input(self, page, url=None) -> bool:
        """
        判断当前页面邮编是否已填写，未填写则自动填写。
        url: 当前页面url，用于判断站点
        """
        try:
            # 1. 根据url判断当前站点，获取目标邮编
            site_code = None
            if url:
                for code, info in amazon_sites.items():
                    if info["url"] in url:
                        site_code = code
                        break
            if not site_code:
                site_code = "US"  # 默认美国
            target_postcode = amazon_sites[site_code]["postcode"]

            # 2. 获取页面当前邮编文本
            try:
                ingress_elem = page.wait_for_selector('#glow-ingress-line2', timeout=5000)
                current_postcode = ingress_elem.text_content().strip() if ingress_elem else ""
            except Exception as e:
                self.logger.debug(f"获取glow-ingress-line2失败: {e}")
                current_postcode = ""

            # 3. 判断是否已填写目标邮编
            if target_postcode in current_postcode:
                self.logger.info(f"页面已填写目标邮编: {current_postcode}")
                return True

            # 4. 点击打开邮编弹窗
            try:
                popover_btn = page.locator('#nav-global-location-popover-link')
                if popover_btn.count() > 0:
                    popover_btn.first.click()
                    page.wait_for_timeout(1500)
                else:
                    self.logger.error("未找到邮编弹窗按钮")
                    return False
            except Exception as e:
                self.logger.error(f"点击邮编弹窗按钮失败: {e}")
                return False

            # 5. 检查弹窗input是否已填写
            try:
                input_elem = page.locator('#GLUXZipUpdateInput')
                if input_elem.count() > 0:
                    input_value = input_elem.first.get_attribute('value')
                    if input_value and input_value.strip() == target_postcode:
                        self.logger.info(f"弹窗已填写目标邮编: {input_value}")
                        # 直接关闭弹窗
                        close_btn = page.locator('//*[@id="a-popover-3"]/div/header/button')
                        if close_btn.count() > 0:
                            close_btn.first.click()
                            page.wait_for_timeout(1000)
                        return True
                    else:
                        # 填写邮编
                        input_elem.first.fill(target_postcode)
                        page.wait_for_timeout(50)
                else:
                    self.logger.error("未找到邮编输入框")
                    return False
            except Exception as e:
                self.logger.error(f"处理邮编输入框失败: {e}")
                return False


            page.wait_for_timeout(10002)

            # 6. 点击提交按钮
            try:
                submit_btn = page.locator('//*[@id="GLUXZipUpdate"]/span/input')
                if submit_btn.count() > 0:
                    submit_btn.first.click()
                    page.wait_for_timeout(1500)
                else:
                    self.logger.error("未找到邮编提交按钮")
                    return False
            except Exception as e:
                self.logger.error(f"点击邮编提交按钮失败: {e}")
                return False

            # 7. 点击确认关闭按钮//*[@id="a-popover-1"]/div/header/button
            try:
                close_btn = page.locator('//*[@id="a-popover-1"]/div/header/button')
                if close_btn.count() > 0:
                    close_btn.first.click()
                    page.wait_for_timeout(1000)
            except Exception as e:
                self.logger.debug(f"关闭邮编弹窗失败: {e}")

            self.logger.info(f"成功填写邮编: {target_postcode}")
            return True

        except Exception as e:
            self.logger.error(f"模拟邮编输入失败: {e}")
            return False
            
    def parse_delivery_info(self, request, response):
        """
        解析配送信息 - 使用AmazonSK的Session方式
        """
        asin = request.asin
        url = request.url

        try:
            # 使用requests方式访问页面（与其他脚本的代理方式一致）
            self.logger.info(f"ASIN {asin}: 开始访问页面 {url}")

            # 获取代理配置
            proxies = None
            if self.use_proxy:
                try:
                    from utils_mrc.SpiderTools import IPPool
                    proxies = IPPool.proxies
                    self.logger.info(f"ASIN {asin}: 使用代理访问")
                except Exception as e:
                    self.logger.warning(f"ASIN {asin}: 代理获取失败，使用本地IP: {e}")

            # 发送请求
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }

            response = requests.get(url, headers=headers, proxies=proxies, timeout=30)
            response.raise_for_status()

            self.logger.info(f"ASIN {asin}: 页面加载成功，状态码: {response.status_code}")

            # 随机等待
            time.sleep(random.uniform(2, 5))

            # 提取配送信息
            result = self.extract_delivery_info_from_html(response.text, asin)
            # 保存结果
            self.save_items([result])

            self.logger.info(f"ASIN {asin}: 配送信息提取完成")

        except Exception as e:
            self.logger.error(f"解析 {asin} 失败: {e}")
            # 失败时也直接写库
            error_result = {
                'asin': asin,
                'primary': '',
                'secondary': '',
                'status': 'error',
                'error': str(e),
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            self.save_items([error_result])

        finally:
            # 不再需要关闭页面，AmazonSK会自动管理Session
            pass
            
    def extract_delivery_info_from_html(self, html: str, asin: str) -> Dict:
        """
        从HTML提取配送信息
        """
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')

            # 检查页面是否存在到货信息主块
            offer_div = soup.find('div', {'id': 'apex_offerDisplay_single_desktop'})
            if not offer_div:
                self.logger.info(f"ASIN {asin}: 页面无到货信息主块，直接返回无到货信息")
                return {
                    "asin": asin,
                    "primary": "",
                    "secondary": "",
                    "status": "no_delivery_info",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }

            # 尝试获取主要配送信息
            primary = ''
            secondary = ''

            # 方法1：通过ID获取配送信息
            primary_selectors = [
                'span[id*="PRIMARY_DELIVERY_MESSAGE"]',
                'div[id*="PRIMARY_DELIVERY_MESSAGE"]',
                'span[data-csa-c-delivery-price]'
            ]

            for selector in primary_selectors:
                elem = soup.select_one(selector)
                if elem:
                    primary = elem.get_text(strip=True)
                    if primary:
                        break

            secondary_selectors = [
                'span[id*="SECONDARY_DELIVERY_MESSAGE"]',
                'div[id*="SECONDARY_DELIVERY_MESSAGE"]',
                'span[data-csa-c-delivery-time]'
            ]

            for selector in secondary_selectors:
                elem = soup.select_one(selector)
                if elem:
                    secondary = elem.get_text(strip=True)
                    if secondary:
                        break

            # 如果还没有获取到，尝试其他可能的选择器
            if not primary and not secondary:
                alternative_selectors = [
                    '#availability span',
                    '#priceblock_dealprice span',
                    '[class*="delivery"] span',
                    '[class*="shipping"] span'
                ]
                for selector in alternative_selectors:
                    elem = soup.select_one(selector)
                    if elem:
                        text = elem.get_text(strip=True)
                        if text:
                            primary = text
                            break

            result = {
                "asin": asin,
                "primary": primary,
                "secondary": secondary,
                "status": "success" if (primary or secondary) else "no_delivery_info",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            self.logger.info(f"ASIN {asin}: Primary='{primary}', Secondary='{secondary}'")
            return result

        except Exception as e:
            self.logger.error(f"提取配送信息失败 {asin}: {e}")
            return {
                "asin": asin,
                "primary": "",
                "secondary": "",
                "status": "error",
                "error": str(e),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
    def save_items(self, items):
        """
        保存抓取结果到 data_amazon_asin_attr 表
        """
        try:
            for item in items:
                asin = item.get('asin', '')
                primary = item.get('primary', '')
                secondary = item.get('secondary', '')
                status = item.get('status', '')
                error = item.get('error', '')

                # 失败时写入失败原因
                if status == 'no_delivery_info':
                    primary = ''
                    secondary = ''
                elif status == 'error':
                    primary = error[:10] if error else '抓取异常'
                    secondary = error[:10] if error else '抓取异常'

                update_sql = """
                UPDATE data_amazon_asin_attr
                SET primary_delivery_message = %s,
                    secondary_delivery_message = %s
                WHERE asin = %s
                """

                if self.ms:
                    # 使用现有代码库的数据库连接
                    self.ms.update(update_sql, (primary, secondary, asin))
                else:
                    # 备用方案：使用原有的pymysql连接
                    connection = pymysql.connect(**self.mysql_config)
                    with connection.cursor() as cursor:
                        cursor.execute(update_sql, (primary, secondary, asin))
                        connection.commit()
                    connection.close()

            self.logger.info(f"已写入 {len(items)} 条结果到 data_amazon_asin_attr")

        except Exception as e:
            self.logger.error(f"保存数据到 data_amazon_asin_attr 失败: {e}")
            # 发送错误通知
            try:
                fsmsg.send('亚马逊到货爬虫', '数据保存失败', str(e))
            except:
                pass
            
    def run(self):
        """
        运行爬虫
        """
        try:
            self.logger.info("开始运行Amazon配送信息爬虫")
            # 记录启动信息
            mrc_logging(f"Amazon配送信息爬虫启动，代理模式: {self.use_proxy}")
            super().run()
            mrc_logging("Amazon配送信息爬虫运行完成")
        except Exception as e:
            error_msg = f"爬虫运行出错: {e}"
            self.logger.error(error_msg)
            mrc_logging(error_msg)
            # 发送错误通知
            try:
                fsmsg.send('亚马逊到货爬虫', '爬虫运行异常', str(e))
            except:
                pass
        finally:
            # 不再需要关闭浏览器，AmazonSK会自动管理Session
            pass
            

if __name__ == "__main__":
    try:
        # 记录启动信息
        mrc_logging(f'开始运行Amazon配送信息爬虫 {get_cur_run_file()}...')

        # 根据环境选择代理模式（与其他脚本保持一致）
        try:
            proxy_type = 1 if not is_test_environment() else 0
        except:
            # 如果is_test_environment函数不存在，默认使用代理
            proxy_type = 1

        # 创建爬虫实例 - 使用单线程
        spider = AmazonDeliverySpider(
            thread_count=1,  # 单线程
            proxy_type=proxy_type  # 使用与其他脚本一致的proxy_type参数
            # proxy_type=0
        )

        mrc_logging(f"爬虫配置: 线程数=1, 代理类型={proxy_type}, 代理模式={proxy_type > 0}")

        # 运行爬虫
        spider.run()

    except Exception as e:
        import traceback
        error_msg = f'Amazon配送信息爬虫启动失败: {traceback.format_exc()}'
        print(error_msg)
        mrc_logging(error_msg)
        try:
            fsmsg.send('亚马逊到货爬虫', '启动失败', error_msg)
        except:
            pass
