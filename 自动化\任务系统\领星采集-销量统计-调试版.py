# -*- coding:UTF-8 -*-
# @FileName  :领星采集-销量统计-调试版.py
# @Time      :2025/7/24 
# <AUTHOR>

import time
import traceback
from datetime import datetime, timedelta
from utils_mrc.pub_fun import *
from utils_mrc.MysqlHelper import MS
from work.自动化.领星抓取.LingXingSessionPage import LingXingSessionPage


def now_int():
    """获取当前时间戳"""
    return int(time.time())


def test_sales_api():
    """测试销量统计API"""
    
    # 创建会话
    session = LingXingSessionPage(user='jszg01')
    
    # 计算日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=14)).strftime('%Y-%m-%d')
    
    logging(f"测试日期范围: {start_date} 到 {end_date}")
    
    # 测试参数
    test_params = {
        'sort_field': 'total',
        'sort_type': 'desc',
        'mids': '',
        'sids': '',
        'start': start_date,
        'end': end_date,
        'type': 'volume',
        'asin_type': 'msku',
        'search_field': 'asin',
        'search_value': [],
        'cid': '',
        'bid': '',
        'offset': 0,
        'length': 20,  # API要求必须是 20,50,100,200,500 中的一个
        'currency_code': '',
        'seller_relation_uids': '',
        'date_type': '1',
        'order_type': 0,
        'gtag_ids': '',
        'turn_on_summary': '0',
        'developers': [],
        'delivery_methods': [],
        'field_key': 't-sales-stat-fix-msku',
        'req_time_sequence': '/api/report/asinDailyLists$$1',
    }
    
    try:
        # 设置请求头
        session.page.set.header("auth-token", session.get_token())
        
        url = "https://muke.lingxing.com/api/report/asinDailyLists"
        logging(f"请求URL: {url}")
        logging(f"请求参数: {test_params}")
        
        # 发送请求
        session.page.post(url, json=test_params)
        
        # 获取响应
        result = session.check_page_json()
        
        logging(f"API响应: {result}")
        
        # 检查响应结构
        if isinstance(result, dict):
            logging(f"响应键: {list(result.keys())}")
            
            if 'total' in result:
                logging(f"总数据量: {result['total']}")
            else:
                logging("响应中没有 'total' 字段")
                
            if 'list' in result:
                logging(f"数据列表长度: {len(result['list'])}")
                if result['list']:
                    logging(f"第一条数据示例: {result['list'][0]}")
            else:
                logging("响应中没有 'list' 字段")
                
            if 'code' in result:
                logging(f"响应代码: {result['code']}")
                
            if 'message' in result:
                logging(f"响应消息: {result['message']}")
        else:
            logging(f"响应不是字典格式: {type(result)}")
            
    except Exception as e:
        logging(f"API调用异常: {e}")
        traceback.print_exc()


def test_different_params():
    """测试不同的参数组合"""
    
    session = LingXingSessionPage(user='jszg01')
    
    # 测试更大的日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')  # 30天
    
    logging(f"\n=== 测试30天日期范围 ===")
    logging(f"日期范围: {start_date} 到 {end_date}")
    
    test_params = {
        'sort_field': 'total',
        'sort_type': 'desc',
        'start': start_date,
        'end': end_date,
        'type': 'volume',
        'asin_type': 'msku',
        'offset': 0,
        'length': 20,
        'date_type': '1',
        'order_type': 0,
        'turn_on_summary': '0',
        'field_key': 't-sales-stat-fix-msku',
        'req_time_sequence': '/api/report/asinDailyLists$$1',
    }
    
    try:
        session.page.set.header("auth-token", session.get_token())
        url = "https://muke.lingxing.com/api/report/asinDailyLists"
        session.page.post(url, json=test_params)
        result = session.check_page_json()
        
        logging(f"30天范围API响应: {result}")
        
        if isinstance(result, dict) and 'total' in result:
            logging(f"30天范围总数据量: {result['total']}")
            
    except Exception as e:
        logging(f"30天范围API调用异常: {e}")


def test_minimal_params():
    """测试最小参数集"""
    
    session = LingXingSessionPage(user='jszg01')
    
    logging(f"\n=== 测试最小参数集 ===")
    
    # 最小参数 - 添加必要的日期参数
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=14)).strftime('%Y-%m-%d')

    minimal_params = {
        'start': start_date,
        'end': end_date,
        'type': 'volume',
        'offset': 0,
        'length': 20,
        'req_time_sequence': '/api/report/asinDailyLists$$1',
    }
    
    try:
        session.page.set.header("auth-token", session.get_token())
        url = "https://muke.lingxing.com/api/report/asinDailyLists"
        session.page.post(url, json=minimal_params)
        result = session.check_page_json()
        
        logging(f"最小参数API响应: {result}")
        
    except Exception as e:
        logging(f"最小参数API调用异常: {e}")


def check_token_validity():
    """检查token有效性"""
    
    session = LingXingSessionPage(user='jszg01')
    
    logging(f"\n=== 检查token有效性 ===")
    logging(f"当前用户: {session.user}")
    logging(f"当前token: {session.get_token()[:50]}...")  # 只显示前50个字符
    
    # 测试一个简单的API来验证token
    try:
        session.check_erp_user()
    except Exception as e:
        logging(f"token验证异常: {e}")


if __name__ == '__main__':
    logging(f'开始调试销量统计API...')
    
    # 1. 检查token有效性
    check_token_validity()
    
    # 2. 测试基本API调用
    test_sales_api()
    
    # 3. 测试不同参数
    test_different_params()
    
    # 4. 测试最小参数
    test_minimal_params()
    
    logging(f'调试完成')
