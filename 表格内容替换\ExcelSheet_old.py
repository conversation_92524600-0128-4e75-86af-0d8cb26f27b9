import copy

import xlwings as xw


class ExcelSheet:
    def __init__(self, file_name, ):
        self.file_name = file_name
        self.workbook = None
        self.sheet = None
        self.range_to_copy = None

    def open_sheet(self):
        self.workbook = xw.Book(self.file_name)
        self.sheet = self.workbook.sheets.active

    def close_sheet(self):
        self.workbook.save()
        self.workbook.close()

    def column_number_to_name(self, column_number):
        column_name = ""
        while column_number > 0:
            column_number -= 1
            column_name = chr(column_number % 26 + ord('A')) + column_name
            column_number //= 26
        return column_name

    def get_copy_range(self, start_range, end_range):
        self.range_to_copy = self.sheet.range(f"{start_range}:{end_range}")
        self.range_to_copy_values = self.range_to_copy.value
        self.len_copy = len(self.range_to_copy.value)
        return self.range_to_copy_values

    def replace_values(self, replacement_list):
        start_row = 4
        used_range = self.sheet.used_range
        max_col = used_range.last_cell.column
        max_row = self.sheet.range('A:A').end('down').row
        mcn = self.column_number_to_name(max_col)
        start_range = f'A{start_row}'
        end_range = f'{mcn}{max_row}'
        self.get_copy_range(start_range, end_range)  # 固定要复制的初始范围内容，每次替换都是从复制的初始范围内容开始
        self.sheet.range(f'{start_range}:{end_range}').api.EntireRow.Delete()
        # 定义需要替换的内容及替换成什么
        for li_ in replacement_list:
            values_data = copy.deepcopy(self.range_to_copy_values)  # 深拷贝，对象的修改不会影响原始列表或其嵌套的可变对象。
            print(self.range_to_copy_values)
            for search_value, replacement_value in li_:
                for i, li_vd in enumerate(values_data):
                    for j, li_v in enumerate(li_vd):
                        cell = values_data[i][j]
                        cell_value = str(cell) if cell is not None else ""
                        if search_value in cell_value:
                            values_data[i][j] = cell_value.replace(search_value, replacement_value)

            self.sheet.range(f'{start_range}:{end_range}').value = values_data
            print(self.len_copy)
            start_row += self.len_copy
            max_row += self.len_copy
            print(start_row, max_row)
            start_range = f'A{start_row}'
            end_range = f'{mcn}{max_row}'
            print(start_range, end_range)


# 创建 ExcelSheet 实例
excel_sheet = ExcelSheet('test.xlsx')

# 打开工作簿
excel_sheet.open_sheet()

replacement_list = [
    [('简写', '7'), ('型号', 'iPhone 7')],
    [('简写', '11'), ('型号', 'iPhone 11')]
    # 添加更多字典项...
]
# 在整个表格中替换内容
excel_sheet.replace_values(replacement_list)
# input('wait...')
# 关闭工作簿
excel_sheet.close_sheet()
