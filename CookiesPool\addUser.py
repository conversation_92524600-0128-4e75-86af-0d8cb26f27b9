import json

from cookiespool.db import *
from cookiespool.config import *

if __name__ == '__main__':
    # types = ['accounts', 'cookies']
    # websites = GENERATOR_MAP.keys()
    # for website in websites:
    #     for t in types:
    #         local = RedisClient(t, website, host='fetch02')
    #         xs = RedisClient(t, website, host='**************')
    #         for k, v in local.all().items():
    #             xs.set(k, v)
    # exit()

    co = RedisClient('cookies', 'amazon')
    ac = RedisClient('accounts', 'amazon')
    cookies = co.all()
    for user, cookies in cookies.items():
        co.delete(user)
    # co.delete(9222)
    print(ac.all())
    print(co.all())
    exit()
    # ac = RedisClient('accounts', 'sprite')
    # co = RedisClient('cookies', 'sprite')
    # # cookies = co.all()
    # # for user, cookies in cookies.items():
    # #     co.delete(user)
    # #     ac.delete(user)
    # users = [
    #     {"password": "yxylll..", "username": "yxylll"},
    #     {"password": "rpa@1688", "username": "rpa168"},
    #     {"password": "yxygsdd2..", "username": "yxygsd2"},
    #     {"password": "yxykaifa...", "username": "yxykaifa"},
    #     {"password": "yxykaifa2...", "username": "yxykaifa2"},
    # ]
    # for user in users:
    #     setAccount(user['username'], user['password'])
    # print(ac.all())
    # print(co.all())
    # exit()
    print('- ' * 30)
    # ac = RedisClient('accounts', 'amazonUS')
    # co = RedisClient('cookies', 'amazonUS')
    # for i in range(10):
    #     # ac.delete(user)
    #     setAccount(f'us{i}', '')
    # print(ac.all())
    # print(co.all())
    # exit()
    print('- ' * 30)
    # ac = RedisClient('accounts', 'amazonUS')
    # co = RedisClient('cookies', 'amazonUS')
    # for i in range(10):
    #     # ac.delete(user)
    #     setAccount(f'us{i}', '')
    # print(ac.all())
    # print(co.all())
    # exit()
    print('- ' * 30)
    ac = RedisClient('accounts', 'lingxing')
    co = RedisClient('cookies', 'lingxing')
    # users = [
    #     {"password": "yxyjs02..", "username": "yxyJS2"},
    #     {"password": "guornjszh1", "username": "jszg01"},
    #     {"password": "yxy023..", "username": "yxy023"},
    #     {"password": "MWNGT1", "username": "yxy025"},
    # ]
    # for user in users:
    #     setAccount(user['username'], user['password'])
    #     # co.delete(user['username'])
    print(ac.all())
    # print(co.all())
    # exit()
    print('- ' * 30)
    # ac = RedisClient('accounts', 'spriteEp')
    # co = RedisClient('cookies', 'spriteEp')
    # users = [
    #     {"password": "rpa@1688", "username": "rpa168"},
    #     {"password": "yxygsdd2..", "username": "yxygsd2"},
    #     {"password": "yxykaifa...", "username": "yxykaifa"},
    #     {"password": "yxykaifa2...", "username": "yxykaifa2"}
    # ]
    # # for user in users:
    # #     setAccount(user['username'], user['password'])
    # print(ac.all())
    # print(co.all())
    # print(co.random_obj())
    # print(co.random())
    # print(list(co.all().values()))
