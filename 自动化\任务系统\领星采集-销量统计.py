# -*- coding:UTF-8 -*-
# @FileName  :领星采集-销量统计.py
# @Time      :2025/7/24 
# <AUTHOR>

import schedule
import time
import traceback
import pandas as pd
from datetime import datetime, timedelta
from utils_mrc.pub_fun import *
from utils_mrc.MysqlHelper import MS
from work.自动化.领星抓取.DataFetcher import DataFetcher, FetchConfig, LingXingSalesProvider
from TimePinner import Pinner
import math
import json


def now_int():
    """获取当前时间戳"""
    return int(time.time())


def get_today_zero_timestamp():
    """获取今天0点时间戳"""
    from datetime import datetime, time as dtime
    return int(datetime.combine(datetime.today(), dtime.min).timestamp())


def get_cur_run_file():
    """获取当前运行文件名"""
    import os
    return os.path.basename(__file__)


def print_results(results):
    """打印抓取结果"""
    print("\n抓取结果统计:")
    logging(f"总数据量: {len(results.get('data', []))}")
    print("\n账号统计:")
    for user, stats in results.get('user_stats', {}).items():
        logging(f"{user}: 成功{stats['success']}页, 失败{stats['failed']}页")
    if results.get('failed_pages'):
        print("\n失败页面:")
        for page, retries in results.get('failed_pages', {}).items():
            logging(f"页码 {page}: 重试 {retries} 次")


def main_fetch_lingxing_sales_data(data_params):
    """使用DataFetcher采集销量统计数据"""
    finally_params = {  # 抓取参数
        'user': ['jszg01', 'yxyJS2'],
        'length': 500,  # 每页数据量，必须是 20,50,100,200,500 中的一个
        # 🔥 取消页数限制，抓取全部数据
    }
    save_params = {  # 入库参数
        'task_id': 2,
        'app_id': 2,
        'datetime': get_today_zero_timestamp(),
        'task_time': now_int()
    }
    finally_params.update(data_params)

    # 创建数据提供者
    provider = LingXingSalesProvider()

    # 创建抓取配置
    page_size = finally_params.get('length', 500)

    config = FetchConfig(
            data_provider=provider,
            users=finally_params.get('user'),
            page_size=page_size,
            task_params=save_params,
            fetch_params={
                'length': page_size
            }
    )

    # 创建抓取器并执行
    fetcher = DataFetcher(config)
    results = fetcher.fetch_data()  # 🔥 取消max_pages限制，抓取全部数据

    # 处理抓取结果
    if results.get('data'):
        logging(f"抓取到原始数据: {len(results['data'])} 条")

        save_params.update({
            'task_num': len(results['data']),
            'platform_num': len(results['data']),
            'done_time': now_int()
        })
        # 插入数据库
        insert_lingxing_sales_data(results['data'], save_params, provider.table_name)

    # 处理失败页面
    print_results(results)

    return results


def run_fetch_lingxing_sales_data(data_params):
    """抓取销量统计数据"""
    pin = Pinner(True, True)
    main_fetch_lingxing_sales_data(data_params)
    pin.pin('销量统计抓取任务结束')


def insert_lingxing_sales_data(data_list, params, table_name, batch_size=100000):
    """
    分批插入销量统计数据到数据库
    :param data_list: 数据列表
    :param params: 参数
    :param table_name: 表名
    :param batch_size: 每批插入的数据量，默认10000条
    """
    if not data_list:
        logging('没有销量数据需要插入')
        return

    try:
        total_records = len(data_list)
        logging(f"开始处理数据，原始数据量: {total_records}")

        # 🔥 确保使用正确的表名
        if table_name == 'idc.data_lingxing_sales':
            table_name = 'rpa.data_lingxing_sales'

        # 计算批次数量
        total_batches = (total_records + batch_size - 1) // batch_size
        logging(f"将分 {total_batches} 批次插入，每批 {batch_size} 条数据")

        success_count = 0
        failed_count = 0

        # 分批处理数据
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, total_records)
            batch_data = data_list[start_idx:end_idx]

            logging(f"正在处理第 {batch_num + 1}/{total_batches} 批，数据范围: {start_idx}-{end_idx}")

            try:
                # 转换为DataFrame
                df = pd.DataFrame(batch_data)

                # 删除不存在于数据库表中的字段
                columns_to_drop = ['id', 'unique_id', 'app_id', 'task_id', 'create_time']
                for col in columns_to_drop:
                    if col in df.columns:
                        df.drop(columns=[col], inplace=True)

                # 🔥 将datetime转换为时间戳
                if 'datetime' in df.columns:
                    df['datetime'] = pd.to_datetime(df['datetime']).astype('int64') // 10**9

                # 🔥 验证DataFrame中的字段是否都存在于数据库表中
                expected_columns = ['platform_account', 'country', 'msku', 'sale_price', 'fnsku', 'asin',
                                   'parent_asin', 'product_name', 'sku', 'model', 'datetime', 'daily_sales']

                # 只保留数据库表中存在的字段
                df = df[[col for col in expected_columns if col in df.columns]]

                # 构造 SQL
                columns = ', '.join([f"`{col}`" for col in df.columns])
                placeholders = ', '.join(['%s'] * len(df.columns))
                sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"

                # 转为 tuple 列表
                args = [tuple(row) for row in df.values]

                # 插入当前批次数据
                MS.insert_many(sql, args)

                if MS.err:
                    logging(f"第 {batch_num + 1} 批插入失败: {MS.err}")
                    failed_count += len(args)
                else:
                    logging(f"第 {batch_num + 1} 批成功插入 {len(args)} 条数据")
                    success_count += len(args)

                # 每批之间稍微休息，避免数据库压力过大
                if batch_num < total_batches - 1:  # 最后一批不需要休息
                    time.sleep(0.1)  # 休息100毫秒

            except Exception as batch_error:
                logging(f"第 {batch_num + 1} 批处理异常: {batch_error}")
                failed_count += len(batch_data)
                continue

        # 输出最终统计
        logging(f"数据插入完成！成功: {success_count} 条，失败: {failed_count} 条")

        if failed_count > 0:
            logging(f"⚠️ 有 {failed_count} 条数据插入失败，请检查日志")

    except Exception as e:
        logging(f"插入销量数据异常: {e}")
        traceback.print_exc()


user = ['jszg01', 'yxyJS2']


def main():
    """
    1. 运行时立即抓取一遍销量统计数据
    2. 开启定时任务，每天早上8点和晚上8点抓取
    """
    # 设置每天早上8点和晚上8点抓取
    schedule.every().day.at("08:00").do(run_fetch_lingxing_sales_data, {'user': user})
    schedule.every().day.at("20:00").do(run_fetch_lingxing_sales_data, {'user': user})

    logging('已设置定时任务，每天 08:00 和 20:00 抓取销量统计数据')

    # 立即执行一次
    logging('开始执行销量统计抓取任务')
    run_fetch_lingxing_sales_data({'user': user})

    while True:
        try:
            # 检查是否有定时任务需要执行
            schedule.run_pending()

        except Exception as e:
            logging(f"发生错误: {str(e)}")
            traceback.print_exc()

        # 短暂休眠以避免过度占用CPU
        time.sleep(5)
if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    main()
