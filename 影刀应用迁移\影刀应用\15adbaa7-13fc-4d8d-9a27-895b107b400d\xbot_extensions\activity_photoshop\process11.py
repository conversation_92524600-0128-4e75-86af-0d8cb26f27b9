import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        ps_instance = None
        layer_name = ""
        font_size = ""
        font = ""
        only_visible = True
        hex_value = ""
    else:
        ps_instance = args.get("ps_instance", None)
        layer_name = args.get("layer_name", "")
        font_size = args.get("font_size", "")
        font = args.get("font", "")
        only_visible = args.get("only_visible", True)
        hex_value = args.get("hex_value", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="set_artlayer_font", params={
            "ps_instance": ps_instance,
            "layer_name": layer_name,
            "font_size": font_size,
            "font": font,
            "hex_value": hex_value,
            "only_visible": only_visible,
        }, _block=("设置文字图层字体", 1, "调用模块"))
    finally:
        pass
