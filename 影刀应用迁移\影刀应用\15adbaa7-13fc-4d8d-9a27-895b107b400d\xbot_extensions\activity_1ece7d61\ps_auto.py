import xbot
from xbot import print, sleep
from . import package
from .package import variables as glv
import os
import win32com.client
from PIL import Image

def main(args):
    export_psd_to_jpg_with_quality(args["output_path"])

def export_psd_to_jpg_with_quality(save_path):
    try:
        # 打开 Photoshop 应用程序
        app = win32com.client.Dispatch("Photoshop.Application")
        
        # 设置 Photoshop 可见
        app.Visible = True

        # 获取当前活动的文档（如果有的话）
        doc = app.ActiveDocument
        
        # 检查是否有打开的文档
        if not doc:
            print("没有打开的 Photoshop 文档")
            return
        
        # 设置导出文件的选项
        options = win32com.client.Dispatch("Photoshop.JPEGSaveOptions")
        
        # 配置保存选项，设置为高质量
        options.quality = 12  # 质量 12 (高)
        options.formatOptions = 1  # 标准 JPG 格式
        options.scans = 3  # 使用 3 扫描来提高质量
        options.matte = 1  # 背景色填充透明部分
        
        # 导出文件为 JPG 格式
        doc.SaveAs(save_path, options, True)
        file_size = os.path.getsize(save_path)  # 获取文件大小，单位是字节
        
        # 如果文件大小大于 2.5MB（2.5MB = 2,500,000 字节），则压缩
        if file_size > 2.5 * 1024 * 1024:
            print(f"图片大小 {file_size} bytes, 超过 2.5MB, 执行压缩")
            compress_image(save_path, save_path)  # 调用压缩函数

        print(f"导出成功，图片保存在: {save_path}")
            
    except Exception as e:
        print(f"导出图片时出错: {e}")



def compress_image(input_path, output_path):
    # 打开图片
    with Image.open(input_path) as img:
        # 保存图片时，指定压缩质量和优化选项
        img.save(output_path, quality=95, optimize=True)
