import os
import sys
import math
import time
import pandas as pd
import schedule
import traceback
from typing import List, Dict

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from utils_mrc.pub_fun import logging
from utils_mrc.MysqlHelper import MS
from work.自动化.领星抓取.DataFetcher import FetchConfig, DataFetcher
from work.自动化.领星抓取.FeeDiffProvider import LingXingFeeDiffProvider
from work.自动化.任务系统.常用.feishu_bitable import upsert_data_batch_to_feishu_bitable


def now_int():
    return int(time.time())

def get_today_zero_timestamp():
    from datetime import datetime, time as dtime
    return int(datetime.combine(datetime.today(), dtime.min).timestamp())


def save_feediff_data(data: List[Dict], params: Dict):
    """保存FBA费用差异数据到MySQL，若mid+sid已存在则更新"""

    if not data:
        return

    df = pd.DataFrame(data)

    # 字段映射：接口字段 → 数据库字段
    rename_map = {
        "shop_name": "sname",
        "area_name": "country",
        "expect_order_num": "expected_compensation_num",
        "expect_amount": "expected_compensation_amount",
        "actual_order_num": "actual_compensation_order_num",
        "actual_amount": "actual_compensation_amount",
        "compensate_rate": "compensation_rate",
        "warning_180day_order_num": "warning_180day_order_num"
    }
    df.rename(columns=rename_map, inplace=True)

    # 添加缺失字段
    df['status'] = 1
    df['create_time'] = int(time.time())

    # 删除 id 字段（如果存在）
    if 'id' in df.columns:
        df.drop(columns=['id'], inplace=True)

    # 保证字段顺序
    expected_columns = [
        'mid', 'sid', 'sname', 'country',
        'expected_compensation_num', 'expected_compensation_amount',
        'actual_compensation_order_num', 'actual_compensation_amount',
        'compensation_rate', 'warning_180day_order_num',
        'status', 'create_time'
    ]
    df = df[[col for col in expected_columns if col in df.columns]]

    try:
        # 构造 SQL
        table = 'rpa.data_lingxing_fba_feediff'
        columns = ', '.join([f"`{col}`" for col in df.columns])
        placeholders = ', '.join(['%s'] * len(df.columns))
        # 构造 ON DUPLICATE KEY UPDATE 部分（排除主键id和唯一索引mid,sid）
        update_columns = [col for col in df.columns if col not in ('id', 'mid', 'sid')]
        update_clause = ', '.join([f"`{col}`=VALUES(`{col}`)" for col in update_columns])
        sql = f"INSERT INTO {table} ({columns}) VALUES ({placeholders}) ON DUPLICATE KEY UPDATE {update_clause}"

        # 转为 tuple 列表
        args = [tuple(row) for row in df.values]

        logging(f"准备插入表 {table}，共 {len(args)} 条（支持mid+sid唯一自动更新）")
        MS.insert_many(sql, args)  # ✅ 传入完整 SQL 和 args

        # ========== 推送到飞书多维表格 ===========
        feishu_field_map = {
            "sname": "店铺",
            "country": "国家",
            "expected_compensation_num": "预计赔付数量",
            "expected_compensation_amount": "预计赔付金额",
            "actual_compensation_order_num": "已赔付订单数量",
            "actual_compensation_amount": "已赔付订单金额"
        }
        # 需要为数字的字段
        feishu_number_fields = {"预计赔付数量", "预计赔付金额", "已赔付订单数量", "已赔付订单金额"}
        feishu_data_list = []
        for row in df.to_dict(orient='records'):
            feishu_data = {}
            for db_field, feishu_field in feishu_field_map.items():
                value = row.get(db_field, "")
                if feishu_field in feishu_number_fields:
                    try:
                        value = float(value) if value not in (None, "") else 0
                    except Exception:
                        value = 0
                feishu_data[feishu_field] = value
            # 时间字段格式化
            if row.get("create_time"):
                from datetime import datetime
                dt = datetime.fromtimestamp(row["create_time"])
                dt_str = dt.strftime("%Y-%m-%d %H:%M:%S")
            else:
                dt_str = ""
            feishu_data["更新时间"] = dt_str
            feishu_data["创建时间"] = dt_str
            feishu_data_list.append(feishu_data)
        if feishu_data_list:
            upsert_data_batch_to_feishu_bitable(feishu_data_list)
        # ========== END ===========
    except Exception as e:
        logging(f"插入费差异数据失败: {e}")




def print_results(results: Dict):
    """打印抓取结果"""
    print("\n抓取结果统计:")
    logging(f"总数据量: {len(results.get('data', []))}")
    print("\n账号统计:")
    for user, stats in results.get('user_stats', {}).items():
        logging(f"{user}: 成功 {stats['success']} 页, 失败 {stats['failed']} 页")
    if results.get('failed_pages'):
        print("\n失败页面:")
        for page, retries in results.get('failed_pages', {}).items():
            logging(f"页码 {page}: 重试 {retries} 次")


def run_fee_diff_fetch(users: list):
    """
    抓取费差异数据，按照 mids 分组（每组最多2个）
    """
    # {
    #     '美国': 1, '加拿大': 2, '墨西哥': 3, '巴西': 17, '英国': 4, '意大利': 7, '德国': 5, '法国': 6, '西班牙': 8, '荷兰': 15, '瑞典': 18, '土耳其': 20, '波兰': 19, '比利时': 21, '爱尔兰': 22, '埃及': 23, '印度': 9, '日本': 10, '澳洲': 12, '阿联酋': 13, '新加坡': 14, '沙特阿拉伯': 16
    # }
    # 按照指定顺序排列的mids
    country_mid_list = [
        1, 2, 3, 17, 4, 7, 5, 6, 8, 15, 18, 20, 19, 21, 22, 23, 9, 10, 12, 13, 14, 16
    ]
    # country_mid_list = [
    #     2, 3, 17
    # ]
    all_mids = country_mid_list  # 直接用这个顺序
    group_size = 1
    total_groups = math.ceil(len(all_mids) / group_size)

    for i in range(total_groups):
        mids_group = all_mids[i * group_size: (i + 1) * group_size]
        fetch_params = {
            'mids': mids_group,
            'sids': [],
            'length': 200,
            'offset': 0
        }

        config = FetchConfig(
            data_provider=LingXingFeeDiffProvider(),
            users=users,
            task_params={
                'task_id': 100 + i,
                'app_id': 1,
                'datetime': get_today_zero_timestamp(),
                'task_time': now_int()
            },
            fetch_params=fetch_params,
            save_data_func=save_feediff_data  # 👈 自定义保存
        )

        logging(f"\n开始抓取第 {i + 1}/{total_groups} 组 mids: {mids_group}")

        fetcher = DataFetcher(config)
        results = fetcher.fetch_data()
        print_results(results)


def scheduled_fee_diff_task():
    """
    定时执行的费差异抓取任务
    """
    try:
        logging("开始执行定时费差异抓取任务...")
        run_fee_diff_fetch(users=['jszg01', 'yxyJS2'])
        logging("定时费差异抓取任务执行完成")
    except Exception as e:
        err = traceback.format_exc()
        logging(f"定时费差异抓取任务执行异常: {err}")
        # 这里可以添加飞书通知等错误处理逻辑


def main_scheduler():
    """
    主调度器，设置定时任务并运行
    """
    logging("启动领星FBA费差异定时抓取任务调度器")
    
    # 启动时立即执行一次
    logging("启动时立即执行一次费差异抓取...")
    scheduled_fee_diff_task()
    
    # 设置定时任务：每天8点和晚上9点执行
    schedule.every().day.at("08:00").do(scheduled_fee_diff_task)
    schedule.every().day.at("21:00").do(scheduled_fee_diff_task)
    
    logging("定时任务已设置：每天 08:00 和 21:00 执行费差异抓取")
    
    while True:
        try:
            # 检查是否有定时任务需要执行
            schedule.run_pending()
        except Exception as e:
            err = traceback.format_exc()
            logging(f"调度器运行异常: {err}")
        
        # 等待10秒，避免CPU占用过高
        time.sleep(10)


if __name__ == '__main__':
    import sys
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--scheduler':
        # 运行定时任务模式
        main_scheduler()
    else:
        # 运行单次执行模式
        logging("启动：领星FBA费差异自动申述抓取任务（单次执行模式）")
        run_fee_diff_fetch(users=['jszg01', 'yxyJS2'])  # 你自己的领星账号


# python 领星FBA费差店铺获取.py --scheduler 