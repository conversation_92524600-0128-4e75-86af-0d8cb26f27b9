import xbot
import xbot_visual
from . import package
from .package import variables as glv
import time

def main(args):
    if args is None:
        photoshop_instance = ""
        name_of_layer = ""
        is_visible = ""
    else:
        photoshop_instance = args.get("photoshop_instance", "")
        name_of_layer = args.get("name_of_layer", "")
        is_visible = args.get("is_visible", "")
    try:
        invoke_result = xbot_visual.process.invoke_module(module="ps_back", package=__name__, function="hide_or_show_layer_by_name", params={
            "photoshop_instance": photoshop_instance,
            "name_of_layer": name_of_layer,
            "is_visible": is_visible,
        }, _block=("显示或隐藏图层", 1, "调用模块"))
    finally:
        pass
