# -*- coding:UTF-8 -*-
from CookiesPool.cookiespool.login.lingxing.cookies import *
from CookiesPool.cookiespool.login.sprite.cookies import *
from CookiesPool.cookiespool.login.amazon.cookies import *
from CookiesPool.cookiespool.tester import *
from CookiesPool.cookiespool.generator import *
from cookiespool.scheduler import Scheduler

if __name__ == '__main__':
    # s = Scheduler()
    # s.valid_cookie()
    # result = LingXingCookies('yxy023', '').main()
    # result = LingXingADValidTester().run()
    # result = SpriteExpansionValidTester().run()
    result = SpriteCookiesGenerator().run()
    # result = LingXingADCookiesGenerator().run()
    # result = AmazonCookies(9223).main()
    # result = AmazonCookies(9555).main('us')
    # result = AmazonCookies(9555)

    print(result)
