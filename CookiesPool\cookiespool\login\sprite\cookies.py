import random
import time
from utils_mrc.FeiShuAPI import *
from utils_mrc.SpiderTools import *
from utils_mrc.RedisClient import *
from DrissionPage import ChromiumOptions, WebPage, ChromiumPage, SessionPage, SessionOptions
import random
import hashlib


class SpriteCookies:
    def __init__(self, username, password):
        self.username = username
        self.password = password
        so = SessionOptions()
        so.set_headers(IPPool.headers)
        self.so = so
        self.page = SessionPage(session_or_options=self.so)

    def login(self):
        pwd = calculate_md5_hash(self.password)
        salt = calculate_md5_hash(self.username + pwd)
        data = {
            "callback": "",
            "noNeedAutoLogin": "1",
            "password": pwd,
            "email": self.username,
            "autoLogin": "Y",
            "salt": salt
        }
        url = "https://www.sellersprite.com/w/user/signin"
        self.page.set.cookies.clear()
        self.page.post(url, data=data, headers={
            **self.so.headers,
            "content-type": "application/x-www-form-urlencoded",
        })
        if (err_ele := self.page('#login_error_message')) and self.page.response.status_code == 200:
            err_text = err_ele.text
            msg = f"{self.username}卖家精灵网页端登录失败,提示为：【{err_text}】,请确认账号是否正常使用！"
            self.err = msg
            # fsmsg.send(None, msg)
            logging(msg)
            return False
        if user := self.page('c=[class="text-truncate u-header__user-avatar_nickname"]'):
            logging(f"卖家精灵登录成功,当前登录账号{user.text}")
            return True

    def set_cookies(self, cookies):
        if isinstance(cookies, (dict, list)):
            self.page.set.cookies(cookies)
        elif isinstance(cookies, str):
            cookies = json.loads(cookies)
            self.page.set.cookies(cookies)

    def login_successfully(self):
        """
        判断是否登录成功
        :return:
        """
        try:
            # 检查登录状态
            url = 'https://www.sellersprite.com/'
            self.page.get(url)
            if cur_user := self.page('@data-user-surname'):
                logging(f"卖家精灵登录状态生效,当前登录账号:{cur_user.text}")
                return True
            elif '/user/login' in self.page.html:
                logging('登录状态失效')
                return False
        except Exception as e:
            print(e)
            return False

    def main(self):
        """
        破解入口
        :return:
        """
        login_status = self.login()
        cookies = self.page.cookies().as_dict()
        if login_status:
            return {
                'status': 1,
                'content': cookies
            }
        else:
            return {
                'status': 3,
                'content': '登录失败'
            }


class SpriteExpansionCookies:
    def __init__(self, user='', pwd=''):
        so = SessionOptions()
        so.set_headers({
            'accept': 'application/json',
            'content-type': 'application/json',
            'user-agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0",
            'random-token': generate_uuid(),
            'Connection': 'keep-alive',
            'accept-language': 'zh-CN,zh;q=0.9,en-GB;q=0.8,en;q=0.7,en-US;q=0.6',
        })
        self.page = SessionPage(session_or_options=so)
        # 基本都是固定值，插件更新会有变动，这里是4.5.0的插件固定值
        # 插件id
        self.extension = "ecanjpklimgeijdcdpdfoooofephbbln"
        # 当前语言
        self.language = "zh_CN"
        # 插件版本号
        self.version = "4.6.1"
        # 基础列表页一级路由
        self.host = "https://www.sellersprite.com/v2/extension/competitor-lookup/"
        self.source = "offline"
        self.user = user
        self.pwd = pwd

    def login_successfully(self):
        """
        判断是否登录成功
        :return:
        """
        try:
            if self.token:
                print(f'{self.user}卖家精灵插件登录成功！')
            return True
        except Exception:
            return False

    def login(self, email, password):
        password = calculate_md5_hash(password)

        TokenGenerator.init()
        tk = TokenGenerator.seller_sprite_token(self.user, password, None, None)

        url = f"https://www.sellersprite.com/v2/extension/signin?email={email}&password={password}&tk={tk}&version={self.version}&language={self.language}&extension={self.extension}&source={self.source}"

        self.page.get(url)
        result = self.page.json
        if not result or result.get('code') != "OK" or not result.get('data').get('token'):
            fsmsg.send(None, f"{self.user} 卖家精灵插件登陆失败！请确认账号是否异常！")
            raise Exception(f"{self.user} 登陆失败！")
        self.token = result['data']['token']

    def main(self):
        """
        破解入口
        :return:
        """
        self.login(self.user, self.pwd)
        cookies = self.token
        if self.login_successfully():
            return {
                'status': 1,
                'content': cookies
            }
        else:
            return {
                'status': 3,
                'content': '登录失败'
            }


# UUID生成函数
def generate_uuid():
    return "{:08x}-{:04x}-4{:03x}-{:03x}-{:012x}".format(
            random.getrandbits(32),
            random.getrandbits(16),
            random.getrandbits(12) | 0x4000,
            random.getrandbits(12) | 0x8000,
            random.getrandbits(48)
    )


# 定义加密和token生成类
class TokenGenerator:
    # Google TKK 默认值
    GOOGLE_TKK_DEFAULT = "446379.1364508470"

    @classmethod
    def init(cls):
        cls.EXT_VERSION = '400500.1364508470'
        cls.EXT_VERSION = "400601.1364508470"

    @classmethod
    def google_token(cls, e, t):
        try:
            tkk = cls.update_token(e)
            return cls._cal(t, tkk) if tkk and tkk != "" else cls._cal(t, cls.GOOGLE_TKK_DEFAULT)
        except Exception as ex:
            raise ex

    @classmethod
    def seller_sprite_token(cls, e, t, r, n):
        s = []
        a = [e, t, r, n]
        for item in a:
            if item and item is not None and len(str(item)) > 0:
                if isinstance(item, list):
                    for i in range(len(item)):
                        s.append(item[i])
                else:
                    s.append(str(item))
        return "" if len(s) < 1 else cls._cal("".join(map(str, s)), cls.EXT_VERSION)

    @classmethod
    def update_token(cls, e):
        return cls.GOOGLE_TKK_DEFAULT

    @classmethod
    def _cal(cls, e, t):
        def r(e, t):
            for i in range(0, len(t) - 2, 3):
                n = t[i + 2]
                n = ord(n) - 87 if n >= "a" else int(n)
                n = e >> n if t[i + 1] == "+" else e << n
                e = (e + n) & 4294967295 if t[i] == "+" else e ^ n
            return e

        n = t.split(".")
        t = int(n[0]) or 0
        s = []
        a = 0
        for i in range(len(e)):
            o = ord(e[i])
            if o < 128:
                s.append(o)
            else:
                if o < 2048:
                    s.append((o >> 6) | 192)
                elif 55296 == (64512 & o) and i + 1 < len(e) and 56320 == (64512 & ord(e[i + 1])):
                    o = 65536 + ((1023 & o) << 10) + (1023 & ord(e[i + 1]))
                    s.append((o >> 18) | 240)
                    s.append((o >> 12) & 63 | 128)
                else:
                    s.append((o >> 12) | 224)
                    s.append((o >> 6) & 63 | 128)
                s.append(63 & o | 128)

        e = t
        for i in range(len(s)):
            e = r(e + s[i], "+-a^+6")

        e = r(e, "+-3^+b+-f")
        e ^= int(n[1]) or 0
        if e < 0:
            e = ********** + (********** & e)

        result = e % 1000000
        return f"{result}.{result ^ t}"


# 生成asin任务的令牌
def get_task_tk(asin_str):
    TokenGenerator.init()
    tk = TokenGenerator.seller_sprite_token(None, asin_str, None, None)
    # print(f"生成tk=>{tk}")
    return tk


# 生成登录所需的tk令牌
def get_login_tk(account, passwd):
    TokenGenerator.init()
    tk = TokenGenerator.seller_sprite_token(account, calculate_md5_hash(passwd), None, None)
    # print(f"生成tk=>{tk}")
    return tk


# MD5加密函数
def calculate_md5_hash(data):
    md5_hash = hashlib.md5()
    md5_hash.update(data.encode('utf-8'))
    md5_result = md5_hash.hexdigest()
    return md5_result


if __name__ == '__main__':
    s1 = time.time()
    cc = RedisClient('cookies', 'sprite')
    ac = RedisClient('accounts', 'sprite')
    # s = SpriteCookies('', '')
    # accounts = ac.all()
    # for user, pwd in accounts.items():
    #     print(user, pwd)
    #     cookies = cc.get(user)
    #     if not cookies:
    #         continue
    #     s.set_cookies(cookies)
    #     login = s.login_successfully()
    #     if not login:
    #         cc.delete(user)
    #     print()
    # result = SpriteExpansionCookies('rpa168', 'rpa@1688').main()
    result = SpriteCookies('rpa168', 'rpa@1688').main()
    # result = SpriteCookies('yxylll', 'yxylll..').main()
    s2 = time.time()
    print(f'耗时: {s2 - s1:.6f}秒')
