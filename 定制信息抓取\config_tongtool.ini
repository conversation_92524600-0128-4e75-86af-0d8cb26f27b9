[BrowserInfo]
# (选填）业务填写：不填默认拉取所有店铺数据，这里填写店铺名称就行（比如AM_MX），脚本会自动匹配符合的店铺；注意AM_ZF(EU)和AM_ZF是两个不同的店铺;不要乱写，否则会导致脚本无法正常运行
store_name=
# 数据时间，不填默认为昨天到今天的数据，比如今天是2025-04-17，默认为2025-04-16 00:00:00 到 2025-04-17 23:59:59 之间的数据
# 举例：今天为2025-04-14，我需要查询前天到今天的订单，那么start_time这里就填写2025-04-12;end_time 就填 2025-04-14；脚本会自动查询2025-04-12 00:00:00 到 2025-04-14 23:59:59 之间的订单；
# 业务填写 比如 2025-04-13（格式）
start_time=2025-07-17
# 业务填写 比如 2025-04-14（格式）
end_time=2025-07-19
[Status]
# 这里是订单状态的映射，由技术维护，业务人员请不要动
status_map = 1:同步中,2:已同步,3:未付款,4:待审核发货,5:待仓库出库,6:仓库已出库,7:不发货,8:不显示,9:平台发货